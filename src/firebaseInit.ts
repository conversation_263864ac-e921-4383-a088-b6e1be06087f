import { initializeApp, } from "firebase/app";
import { getAuth } from "firebase/auth";

const firebaseConfig = {
    apiKey: "AIzaSyD8QaDm7tLpR3m5PMV2MO_rssxMo8GOI9c",
    authDomain: "usi-erp.firebaseapp.com",
    projectId: "usi-erp",
    storageBucket: "usi-erp.firebasestorage.app",
    messagingSenderId: "20099275400",
    appId: "1:20099275400:web:d0ab927050ed83ca985d3d",
    measurementId: "G-WMXFHVFHL9"
};

const usiFirebaseApp = initializeApp(firebaseConfig);
const usiFirebaseAuth = getAuth(usiFirebaseApp);

export {
    usiFirebaseApp,
    usiFirebaseAuth,
}
