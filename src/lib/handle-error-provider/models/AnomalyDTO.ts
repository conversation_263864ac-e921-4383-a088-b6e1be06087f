import { getUid } from "$lib/common/utils/common-utils";
import type { USER_TYPE } from "$lib/users/models/BaseUser";
import type { IUser } from "$lib/users/models/User";

export enum ANOMALY {
    STALE_PRODUCT_IN_CART = "STALE_PRODUCT_IN_CART",
    NULL_START_DATE_FOR_CUSTOMISED_HELMET_CLUB = "NULL_START_DATE_FOR_CUSTOMISED_HELMET_CLUB",
    GENERIC = "GENERIC",
    DELIVERY_METHOD_NOT_ASSIGNED_TO_CLUB = "DELIVERY_METHOD_NOT_ASSIGNED_TO_CLUB",
    MISSING_BARCODE_IN_PRODUCT = "MISSING_BARCODE_IN_PRODUCT",
    ORDER_TRANSACTION = "ORDER_TRANSACTION",
    INVALID_DISCOUNT_GROUP_ID_IN_USER = "INVALID_DISCOUNT_GROUP_ID_IN_USER",
    MISSING_CATEGORY_IN_PRODUCT = "MISSING_CATEGORY_IN_PRODUCT",
    ORDER_MAIL_ERROR = "ORDER_MAIL_ERROR",
    PAYPAL_ORDER_FAILED = "PAYPAL_ORDER_FAILED",
    RP_ORDER_CREATION = "RP_ORDER_CREATION",
    RP_PAYMENT_FAILED = "RP_PAYMENT_FAILED",
    RP_PAYMENT_SAVING_FAILED_TRANSACTION = "RP_PAYMENT_SAVING_FAILED_TRANSACTION",
    RP_ORDER_SAVING_FAILED = "RP_ORDER_SAVING_FAILED",
    RP_PAYMENT_CAPTURE_FAILED = "RP_PAYMENT_CAPTURE_FAILED",
    SHIPMENT_SYNC_FAILED = "SHIPMENT_SYNC_FAILED",
    CANCEL_ORDER_FAILED = "CANCEL_ORDER_FAILED",
    VALIDATION_ERROR = "Validation failed",
    UNKNOWN = "Something Went Wrong",
    GET_ORDERS_COUNT_FAILED = "GET_ORDERS_COUNT_FAILED",
    ORDER_TRANSACTION_RETRY_FAILED = "ORDER_TRANSACTION_RETRY_FAILED",
    ORDER_REFUND_FAILED = "ORDER_REFUND_FAILED",
    COUPON_UPDATION_FAILED = "COUPON_UPDATION_FAILED",
    COUPON_VALIDATION_FAILED = "COUPON_VALIDATION_FAILED",
    COUPON_VALIDATION_FAILED_DEEP = "COUPON_VALIDATION_FAILED_DEEP",
    RECOMMENDED_COUPON_FAILED = "RECOMMEND_COUPON_FAILED",
    RP_PAYMENT_ORDER_FAILED = "RP_PAYMENT_ORDER_FAILED",
    RP_SOCIAL_MEDIA_SUBSCRIPTION_FAILED = "RP_SOCIAL_MEDIA_SUBSCRIPTION_FAILED",
    REWARD_POINTS_TRANSACTION_CRASH = "REWARD_POINTS_TRANSACTION_CRASH",
    SHEER_ID_SAVING_ERROR = "SHEER_ID_SAVING_ERROR",
    SAVE_POINTS_TRANSACTION_FAILED = "SAVE_POINTS_TRANSACTION_FAILED",
    PROCESS_ORDER_API_FAILED = "PROCESS_ORDER_API_FAILED",
    CALLABLE_FUNCTION_PROCESS_ORDER = "CALLABLE_FUNCTION_PROCESS_ORDER",
    CLICK_UP_API_FAILED = "CLICK_UP_API_FAILED",
    ADD_TO_CART_FAILED = "ADD_TO_CART_FAILED ",
    REWARD_POINTS_TRANSACTION_APPROVING = "REWARD_POINTS_TRANSACTION_APPROVING",
    REWARD_POINTS_GET_USER_POINTS_FAIL = "REWARD_POINTS_GET_USER_POINTS_FAIL",
    REWARD_POINTS_SOCIAL_MEDIA_TRANSACTION_CHECK_FAILED = "REWARD_POINTS_SOCIAL_MEDIA_TRANSACTION_CHECK_FAILED",
    PAYMENT_MODAL_ERROR = "PAYMENT_MODAL_ERROR",
    FORGOT_PASSWORD_CALLABLE_FUNCTION_FAILED = "FORGOT_PASSWORD_CALLABLE_FUNCTION_FAILED",
}

export enum ANOMALY_STATUS {
    TO_DO = "TO DO",
    UNASSIGNED = "UNASSIGNED",
    RESOLVED = "RESOLVED",
    REOPENED = "REOPENED",
}

export enum ANOMALY_LEVEL {
    URGENT = 1,
    HIGH = 2,
    MEDIUM = 3,
    LOW = 4,
}

export interface AnomalyLog {
    user: USER_TYPE;
    message: string | null;
    createdAt: Date;
}

interface AnomalyDTOLegacy {
    id: string;
    type: ANOMALY;
    error: any;
    message: string;
    user: IUser | null;
    file: string;
    createdAt: Date;
    updatedAt: Date;
    args: any[];
}

export interface AnomalyDTO extends AnomalyDTOLegacy {
    level: ANOMALY_LEVEL;
    status: ANOMALY_STATUS;
    logs: AnomalyLog[];
    updatedAt: Date;
}

export interface AnomalyClickUpDTO extends AnomalyDTO {
    clickUpURL: string | null;
}

export const getAnomalyLevelSymbol = (level: ANOMALY_LEVEL) => {
    switch (level) {
        case ANOMALY_LEVEL.URGENT:
            return "🔴";

        case ANOMALY_LEVEL.HIGH:
            return "🟠";

        case ANOMALY_LEVEL.MEDIUM:
            return "🟡";

        case ANOMALY_LEVEL.LOW:
            return "🔵";

        default:
            return "🚨";
    }
};

export function getAnomalyDTO(
    error: any,
    file: string,
    type: ANOMALY = ANOMALY.GENERIC,
    user: IUser | null = null,
    level: ANOMALY_LEVEL = ANOMALY_LEVEL.LOW,
    ...args: any[]
): AnomalyDTO {
    const date = new Date();
    return {
        updatedAt: date,
        args,
        createdAt: date,
        error: error.stack ?? "no stack found",
        file,
        id: getUid(),
        level,
        logs: [],
        message: error?.message ?? "Something Went wrong",
        status: ANOMALY_STATUS.TO_DO,
        type,
        user,
    };
}

export function getGENERICAnomalyDTO(error: any, file: string, ...args: any[]): AnomalyDTO {
    const date = new Date();
    return {
        updatedAt: date,
        args,
        createdAt: date,
        error: JSON.stringify(error),
        file,
        id: getUid(),
        level: ANOMALY_LEVEL.LOW,
        logs: [],
        message: error?.message ?? "Something Went wrong",
        status: ANOMALY_STATUS.TO_DO,
        type: ANOMALY.GENERIC,
        user: null,
    };
}
