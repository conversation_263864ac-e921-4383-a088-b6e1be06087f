<script lang="ts">
    import { Select } from "flowbite-svelte";
    import { onMount } from "svelte";
    import CustomModal from "$lib/common/components/CustomModal.svelte";
    import type { IUserRole } from "../models/IUserRole";
    import { UserRoleUtils } from "../utils/IUserRoleUtils";
    import UserRoleForm from "./UserRoleForm.svelte";

    export let data: IUserRole[] = [];
    export let id: string = "";
    export let cssClass: string = "";
    export let selectedValue: number = 0;
    export let onSelected: (data: any) => void = () => {};
    export let disabled: boolean = false;
    export let allowAddNew: boolean = false;

    let showAddNewModal: boolean = false;

    const pushAddNew = () => {
        const obj = UserRoleUtils.getEmpty();
        obj.id = -1;
        obj.role = "Add new";
        data.push(obj);

        data = data;
    };

    onMount(() => {
        if (allowAddNew) {
            pushAddNew();
        }
    });
</script>

<Select
    {id}
    {disabled}
    class={cssClass}
    items={data.map((item) => ({
        value: item.id.toString(),
        name: item.role.toUpperCase(),
    }))}
    value={selectedValue.toString()}
    on:change={(e) => {
        // @ts-ignore
        const value = Number(e.currentTarget.value);

        if (value === -1) {
            selectedValue = 1;
            showAddNewModal = true;
        } else {
            onSelected(value);
        }
    }}
/>

<CustomModal title="Add new role" bind:showModal={showAddNewModal}>
    <UserRoleForm
        isInsideModal={true}
        onSubmitSuccess={(obj) => {
            data.push(obj);
            data = data.filter((item) => item.id !== -1);
            pushAddNew();
            selectedValue = obj.id;
            onSelected(obj.id);
            showAddNewModal = false;
        }}
    />
</CustomModal>
