import type { BaseModel, DeletedMetaData, UpdatedMetaData } from "$lib/common/models/base_model";


enum USER_ROLE_STATUS {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    DELETED = 'deleted'
}

interface ICreateUserRole {
    role: string;
}

interface ICreateUserRoleWithPermissions extends ICreateUserRole {
    permissions: string[];
}

interface IUpdateUserRole extends ICreateUserRole {
    id: number;
    status: USER_ROLE_STATUS;
}

interface IUpdateUserRoleWithPermissions extends IUpdateUserRole {
    permissions: string[];
}

interface IUserRole extends ICreateUserRoleWithPermissions, BaseModel, UpdatedMetaData, DeletedMetaData {
    status: USER_ROLE_STATUS;

}

export { USER_ROLE_STATUS };
export type { IUserRole, ICreateUserRole, IUpdateUserRole, ICreateUserRoleWithPermissions, IUpdateUserRoleWithPermissions };
