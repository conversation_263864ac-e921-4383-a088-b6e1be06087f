import { z } from "zod";
import { USER_ROLE_STATUS, type IUserRole } from "../models/IUserRole";
import { DEBIT_NOTE_PERMISSIONS, FACTORY_GATE_PERMISSIONS, ITEM_CATEGORY_PERMISSIONS, ITEM_UNIT_PERMISSIONS, LOG_PERMISSIONS, STOCK_ADJUSTMENT_PERMISSIONS, PERMISSION_RELATIONSHIPS, RAW_MATERIAL_PERMISSIONS, RAW_MATERIAL_STOCK_PERMISSIONS, STORAGE_LOCATION_PERMISSIONS, SUPPLIER_PERMISSIONS, USER_PERMISSIONS, USER_ROLE_PERMISSIONS } from "$lib/users/sub-features/permissions/models/AppPermissions";

export abstract class UserRoleUtils {
    static getEmpty(): IUserRole {
        return {
            id: -1,
            role: "",
            status: USER_ROLE_STATUS.ACTIVE,
            permissions: [],
            createdAt: new Date(),
            updatedAt: null,
            deletedAt: null,
            createdBy: '',
            updatedBy: null,
            deletedBy: null,
        }
    }


    static createSchema =
        z.object({
            role: z.string({ errorMap: () => ({ message: "Role is required" }) })
                .min(3, "Role must be at least 3 characters").max(255, "Role must be upto 255 characters").refine(val => !val.toLowerCase().trim().includes("admin"), { message: "Invalid role. Please donot use 'admin' word" }),
            permissions: z.array(z.union([
                z.nativeEnum(USER_ROLE_PERMISSIONS),
                z.nativeEnum(USER_PERMISSIONS),
                z.nativeEnum(SUPPLIER_PERMISSIONS),
                z.nativeEnum(STORAGE_LOCATION_PERMISSIONS),
                z.nativeEnum(FACTORY_GATE_PERMISSIONS),
                z.nativeEnum(ITEM_CATEGORY_PERMISSIONS),
                z.nativeEnum(ITEM_UNIT_PERMISSIONS),
                z.nativeEnum(RAW_MATERIAL_PERMISSIONS),
                z.nativeEnum(RAW_MATERIAL_STOCK_PERMISSIONS),
                z.nativeEnum(DEBIT_NOTE_PERMISSIONS),
                z.nativeEnum(LOG_PERMISSIONS),
                z.nativeEnum(STOCK_ADJUSTMENT_PERMISSIONS),
            ]), {
                errorMap: () => ({
                    message: "Permissions are required",
                })
            }),
        });

    static updateSchema =
        UserRoleUtils.createSchema.extend({
            id: z.number({ errorMap: () => ({ message: "ID is required" }) }).int().positive("ID must be positive"),
            status: z.nativeEnum(USER_ROLE_STATUS, {
                errorMap: () => ({
                    message: "Status is required",
                })
            }).refine(val => Object.values(USER_ROLE_STATUS).includes(val), {
                message: "Invalid status",
            }),
        });


    static getDirectDependencies(
        permission: string
    ): {
        displayName: string;
        permission: string;
    }[] {
        return PERMISSION_RELATIONSHIPS[permission]?.dependencies?.map((dep) => dep) || [];
    }

    static getTransitiveDependencies(
        permission: string,
        obj: IUserRole
    ): {
        displayName: string;
        permission: string;
    }[] {
        const transitiveDependencies =
            PERMISSION_RELATIONSHIPS[permission]?.transitiveDependencies?.map((dep) => dep) || [];

        const presentDependencies = [];

        /* get dependencies of transitive */
        for (const dep of transitiveDependencies) {
            if (obj.permissions.includes(dep.permission)) {
                presentDependencies.push(dep);
                presentDependencies.push(
                    ...(PERMISSION_RELATIONSHIPS[dep.permission]?.dependencies?.map((dep) => dep) ||
                        [])
                );
            }
        }

        return presentDependencies;
    }

    static getAllDependencies(
        permission: string,
        obj: IUserRole
    ): {
        displayName: string;
        permission: string;
    }[] {
        let dependencies = [];
        let directDependencies = UserRoleUtils.getDirectDependencies(permission);

        let transitiveDependencies = UserRoleUtils.getTransitiveDependencies(permission, obj);

        dependencies = [...directDependencies, ...transitiveDependencies];

        /* remove duplicates and also self permission */

        dependencies = dependencies.filter(
            (item, index, self) =>
                index === self.findIndex((t) => t.permission === item.permission) &&
                item.permission !== permission
        );

        return dependencies;
    }

    static formatPermission(permission: string): string {
        const name = permission.split("#")[0];
        const data = name.split("_");
        return data.join(" ").toUpperCase();
    }

    static filterRemovableDependencies(
        permissionToRemove: string,
        obj: IUserRole,
    ): {
        displayName: string;
        permission: string;
    }[] {
        const allDependencies = UserRoleUtils.getAllDependencies(
            permissionToRemove, obj
        );


        const dependenciesRequiredByOtherPermissions: string[] = [];

        for (const permission of obj.permissions) {
            if (permission !== permissionToRemove && !allDependencies.map(e => e.permission).includes(permission)) {
                const deps = UserRoleUtils.getDirectDependencies(
                    permission,

                );
                deps.forEach(dep => {



                    if (
                        !dependenciesRequiredByOtherPermissions.includes(dep.permission)) {
                        dependenciesRequiredByOtherPermissions.push(dep.permission);
                    }
                });
            }
        }
        const removableDependencies = allDependencies.filter(dep => !dependenciesRequiredByOtherPermissions.includes(dep.permission)).map(d => d);
        return removableDependencies;
    }
}