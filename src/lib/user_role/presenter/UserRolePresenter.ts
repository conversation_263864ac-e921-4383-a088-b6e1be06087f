import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { ValidationErrors } from "$lib/common/utils/types";
import type { IUserRolePresenter, } from "./IUserRolePresenter";
import type { IUserRole, ICreateUserRole, IUpdateUserRole, ICreateUserRoleWithPermissions, IUpdateUserRoleWithPermissions } from "../models/IUserRole";
import { UserRoleUtils } from "../utils/IUserRoleUtils";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class UserRolePresenter implements IUserRolePresenter {
    getAll(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IUserRole>>> {
        return RepoProvider.userRoleRepo.getAll(page, pageSize, text);
    }
    getById(id: number): Promise<DTO<IUserRole>> {
        return RepoProvider.userRoleRepo.getById(id);
    }
    onSubmit(payload: ICreateUserRoleWithPermissions): Promise<DTO<IUserRole>> {
        return RepoProvider.userRoleRepo.create(payload);
    }
    onUpdate(payload: IUpdateUserRoleWithPermissions): Promise<DTO<boolean>> {
        return RepoProvider.userRoleRepo.update(payload);
    }
    onDelete(ids: number[]): Promise<DTO<boolean>> {
        return RepoProvider.userRoleRepo.delete(ids);
    }
    validateCreate(payload: ICreateUserRole): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = UserRoleUtils.createSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }
    validateUpdate(payload: ICreateUserRole): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = UserRoleUtils.updateSchema.safeParse(payload);
        console.log(result);

        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }


}