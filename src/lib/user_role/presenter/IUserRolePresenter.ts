import type { DTO } from "$lib/common/models/BaseDTO";
import type { ValidationErrors } from "../../common/utils/types";
import type { ICreateUserRole, ICreateUserRoleWithPermissions, IUpdateUserRole, IUpdateUserRoleWithPermissions, IUserRole } from "../models/IUserRole";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export interface IUserRolePresenter {
    getAll(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IUserRole>>>;
    getById(id: number): Promise<DTO<IUserRole>>;
    onSubmit(payload: ICreateUserRoleWithPermissions): Promise<DTO<IUserRole>>;
    onUpdate(payload: IUpdateUserRoleWithPermissions): Promise<DTO<boolean>>;
    onDelete(ids: number[]): Promise<DTO<boolean>>;
    validateCreate(payload: ICreateUserRoleWithPermissions): ValidationErrors;
    validateUpdate(payload: IUpdateUserRoleWithPermissions): ValidationErrors;
}