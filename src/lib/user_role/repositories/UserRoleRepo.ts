import { USER_ROLES_API_PATH } from "$lib/common/configs/serverConfig";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    getUnhandledErrorDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { handleError } from "$lib/common/utils/logging";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { IUserRoleRepo } from "./IUserRoleRepo";
import type { ICreateUserRoleWithPermissions, IUpdateUserRoleWithPermissions, IUserRole } from "../models/IUserRole";

export class UserRoleRepo implements IUserRoleRepo {
    private _apiPath: string = USER_ROLES_API_PATH;

    async create(payload: ICreateUserRoleWithPermissions): Promise<DTO<IUserRole>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/create",
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async update(payload: IUpdateUserRoleWithPermissions): Promise<DTO<boolean>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<boolean> = await fetchData(
                this._apiPath + "/update/" + payload.id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    delete(ids: number[]): Promise<DTO<boolean>> {
        throw new Error("Method not implemented.");
    }

    async getById(id: number): Promise<DTO<IUserRole>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IUserRole> = await fetchData(
                this._apiPath + "/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getAll(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IUserRole>>> {
        try {
            const options = {
                method: "GET",
            };


            let query = "page=" + page + "&pageSize=" + pageSize;
            if (text) {
                query += "&text=" + text;
            }

            const res: FetchResult<PaginatedBaseResponse<IUserRole>> = await fetchData(this._apiPath + "/?" + query, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

}
