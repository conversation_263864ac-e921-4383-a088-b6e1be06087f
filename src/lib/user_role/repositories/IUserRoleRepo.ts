import type { DTO } from "$lib/common/models/BaseDTO";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { ICreateUserRoleWithPermissions, IUpdateUserRole, IUpdateUserRoleWithPermissions, IUserRole } from "../models/IUserRole";

export interface IUserRoleRepo {
    create(payload: ICreateUserRoleWithPermissions): Promise<DTO<IUserRole>>;
    update(payload: IUpdateUserRoleWithPermissions): Promise<DTO<boolean>>;
    delete(ids: number[]): Promise<DTO<boolean>>;
    getById(id: number): Promise<DTO<IUserRole>>;
    getAll(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IUserRole>>>;
}