<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, Select } from "flowbite-svelte";

    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import { ITEM_CATEGORY_STAUS, type IItemCategory } from "../models/IItemCategory";
    import { ItemCategoryUtils } from "../utils/ItemCategoryUtils";

    export let obj: IItemCategory = ItemCategoryUtils.getEmpty();
    export let isInsideModal: boolean = false;
    export let onSubmitSuccess: null | ((data: IItemCategory) => void) = null;

    let isDoingTask: boolean = false;

    let validationErrors: Map<string, string> = new Map();

    const handleSubmit = async () => {
        validationErrors = PresenterProvider.itemCategoryPresenter.onValidate(obj);
        if (validationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        isDoingTask = true;
        let res;
        if (obj.id > 0) {
            res = await PresenterProvider.itemCategoryPresenter.onUpdate(obj.id, obj);
        } else {
            res = await PresenterProvider.itemCategoryPresenter.onSubmit(obj);
        }

        if (res.success) {
            showSuccessToast(`Done`);
            if (onSubmitSuccess) {
                onSubmitSuccess(res.data! as IItemCategory);
            } else {
                await goto("/admin/item-categories");
            }
        } else {
            showErrorToast(res.message);
            isDoingTask = false;
        }
    };
</script>

<div class="{isInsideModal ? '' : ''} flex items-center justify-center">
    <div class="{isInsideModal ? '' : ''} w-[90vw] p-2">
        {#if !isInsideModal}
            <div class=" flex items-center justify-between py-2">
                <FormHeader label={obj.id > 0 ? "Edit item category" : "Add item category"}
                ></FormHeader>
                <BreadCrumbs breadCrumbData={[]} />
            </div>
            <hr class="mb-5" />
        {/if}
        <div class=" grid grid-cols-2 gap-6">
            <div>
                <Label for="name" class="mb-2 font-sans capitalize tracking-[0px]">
                    Category name
                    {#if validationErrors.has("name")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>
                <Input
                    type="text"
                    id="name"
                    placeholder="Name"
                    class="dark:bg-primary-700 {validationErrors.has('name')
                        ? 'border-red-500'
                        : ''}"
                    bind:value={obj.name}
                />
                {#if validationErrors.has("name")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("name")}
                    </p>
                {/if}
            </div>
        </div>
        <div class="m-2"></div>

        <div class=" grid grid-cols-2 gap-6">
            {#if obj.id > 0}
                <div>
                    <Label for="status" class="mb-2 font-sans capitalize tracking-[0px]">
                        Status
                        {#if validationErrors.has("status")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Select
                        id="status"
                        class="dark:bg-primary-700 {validationErrors.has('status')
                            ? 'border-red-500'
                            : ''}"
                        items={Object.values(ITEM_CATEGORY_STAUS).map((item) => ({
                            value: item,
                            name: item.toUpperCase(),
                        }))}
                        bind:value={obj.status}
                    />
                    {#if validationErrors.has("status")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("status")}
                        </p>
                    {/if}
                </div>
            {/if}
        </div>
        <div class="m-2"></div>

        <div class="mt-5 flex w-full justify-end">
            <CustomButton
                onClick={handleSubmit}
                cssClass="w-32 bg-black"
                title={"Save"}
                isLoading={isDoingTask}
            />
        </div>
    </div>
</div>
