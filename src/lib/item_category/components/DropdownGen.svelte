<script lang="ts">
    import { onMount } from "svelte";
    export let loadItems: () => Promise<{ id: number; title: string }[]>;
    export let value: string | number | undefined;
    let items: { id: number; title: string }[] = [];
    let open = false;
    export let disabled = false;
    onMount(async () => {
        items = await loadItems();
    });

    function selectItem(id: number) {
        value = id;
        open = false;
    }
</script>

<div class="relative inline-block w-full">
    <button
        {disabled}
        class="border p-2 rounded w-full text-left {disabled
            ? 'text-gray-400  cursor-not-allowed'
            : ''}"
        on:click={() => (open = !open)}
    >
        {items.find((i) => i.id === value)?.title || "Select..."}
    </button>

    {#if open}
        <ul class="absolute z-10 mt-1 w-full bg-white border rounded shadow">
            {#each items as item}
                <li
                    class="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                    on:click={() => selectItem(item.id)}
                >
                    {item.title}
                </li>
            {/each}
        </ul>
    {/if}
</div>
