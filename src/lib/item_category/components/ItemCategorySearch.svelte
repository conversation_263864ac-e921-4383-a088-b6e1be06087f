<script lang="ts">
    import CustomModal from "$lib/common/components/CustomModal.svelte";
    import SearchWithDrop from "$lib/common/components/SearchWithDrop.svelte";
    import { debounce } from "$lib/common/utils/common-utils";
    import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
    import ItemCategoryForm from "$lib/item_category/components/ItemCategoryForm.svelte";
    import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
    import { ItemCategoryUtils } from "$lib/item_category/utils/ItemCategoryUtils";
    import { RepoProvider } from "$lib/RepoProvider";

    export let isLabel: boolean = true;
    export let labelText: string = "Categories";
    export let selected: IItemCategory | null = null;
    export let onSelected: (data: IItemCategory) => void;

    let searchTerm: string = "";
    let fetchedData: IItemCategory[] = [];
    let showAddNewModal: boolean = false;
    let doingSearch: boolean = false;

    const getData = async () => {
        doingSearch = true;
        const res = await RepoProvider.itemCategoryRepo.search(searchTerm);
        if (res.success) {
            fetchedData = res.data;
        }
        if (fetchedData.length === 0) {
            const obj = ItemCategoryUtils.getEmpty();
            obj.name = "Add new";
            fetchedData = [obj];
        }
        doingSearch = false;
    };

    const debounceSearch = debounce(getData, 600);

    const doSearch = (search: string) => {
        searchTerm = search; // Update the search term
        debounceSearch(); // Call the debounced search
    };

    const addNew = () => {
        showAddNewModal = true;
    };
</script>

<SearchWithDrop
    {isLabel}
    label={labelText}
    bind:searchTerm
    level="name"
    loading={doingSearch}
    searchInput={doSearch}
    selected={selected?.name ?? null}
    selectedObj={selected}
    selectedFunc={(data) => {
        fetchedData = [];
        searchTerm = "";
        if (data) {
            if (data.id === -1) {
                return addNew();
            }
        }
        onSelected(data);
        selected = data;
    }}
    bind:filteredData={fetchedData}
/>

<CustomModal title="Add New Category" bind:showModal={showAddNewModal}>
    <ItemCategoryForm
        isInsideModal={true}
        onSubmitSuccess={(data) => {
            fetchedData = [];
            searchTerm = "";
            selected =data;
            onSelected(data);
            showAddNewModal = false;
        }}
    />
</CustomModal>
