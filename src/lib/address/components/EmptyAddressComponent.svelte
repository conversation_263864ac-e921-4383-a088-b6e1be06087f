<script lang="ts">
    import { Input, Label } from "flowbite-svelte";
    import type { <PERSON><PERSON><PERSON><PERSON>, ICreateAddress, IUpdateAddress } from "$lib/address/models/address";
    export let errorMap = new Map();
    export let address: ICreateAddress | IUpdateAddress;
</script>

<div class="grid grid-cols-3 gap-6 pt-4">
    <div>
        <Label for="Street" class="mb-2 font-sans capitalize tracking-[0px]">
            Street
            <span class="text-red-600">*</span>
        </Label>
        <Input
            type="text"
            id="Street"
            placeholder="Street"
            on:input={() => (errorMap = new Map())}
            class="uppercase dark:bg-primary-700 {errorMap.has('address-street') ? 'border-red-500' : ''}"
            bind:value={address.street}
        />
        {#if errorMap.has("address-street")}
            <span class="pt-2 font-serif text-[14px] italic text-red-500">
                {errorMap.get("address-street")}
            </span>
        {/if}
    </div>
    <div>
        <Label for="State" class="mb-2 font-sans capitalize tracking-[0px]">
            State
            <span class="text-red-600">*</span>
        </Label>
        <Input
            type="text"
            id="State"
            placeholder="State"
            on:input={() => (errorMap = new Map())}
            class="uppercase dark:bg-primary-700 {errorMap.has('address-state') ? 'border-red-500' : ''}"
            bind:value={address.state}
        />
        {#if errorMap.has("address-state")}
            <span class="pt-2 font-serif text-[14px] italic text-red-500">
                {errorMap.get("address-state")}
            </span>
        {/if}
    </div>
    <div>
        <Label for="City" class="mb-2 font-sans capitalize tracking-[0px]">
            City
            <span class="text-red-600">*</span>
        </Label>
        <Input
            type="text"
            id="City"
            placeholder="City"
            on:input={() => (errorMap = new Map())}
            class="uppercase dark:bg-primary-700 {errorMap.has('address-city') ? 'border-red-500' : ''}"
            bind:value={address.city}
        />
        {#if errorMap.has("address-city")}
            <span class="pt-2 font-serif text-[14px] italic text-red-500">
                {errorMap.get("address-city")}
            </span>
        {/if}
    </div>
    <div>
        <Label for="Country" class="mb-2 font-sans capitalize tracking-[0px]">
            Country
            <span class="text-red-600">*</span>
        </Label>
        <Input
            type="text"
            id="Country"
            placeholder="Country"
            on:input={() => (errorMap = new Map())}
            class="uppercase dark:bg-primary-700 {errorMap.has('address-country') ? 'border-red-500' : ''}"
            bind:value={address.country}
        />
        {#if errorMap.has("address-country")}
            <span class="pt-2 font-serif text-[14px] italic text-red-500">
                {errorMap.get("address-country")}
            </span>
        {/if}
    </div>

    <div>
        <Label for="PostalCode-billing" class="mb-2 font-sans capitalize tracking-[0px]">
            Postal Code
            <span class="text-red-600">*</span>
        </Label>
        <Input
            type="number"
            id="PostalCode-billing"
            placeholder="Postal Code"
            on:input={() => (errorMap = new Map())}
            class="dark:bg-primary-700 {errorMap.has('address-postalCode') ? 'border-red-500' : ''}"
            bind:value={address.postalCode}
        />
        {#if errorMap.has("address-postalCode")}
            <span class="pt-2 font-serif text-[14px] italic text-red-500">
                {errorMap.get("address-postalCode")}
            </span>
        {/if}
    </div>
</div>
