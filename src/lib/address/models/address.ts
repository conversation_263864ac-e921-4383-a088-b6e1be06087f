export interface IAddress {
    id: number;
    street: string;
    postalCode: string;
    city: string;
    state: string;
    country: string;
}


interface ICreateAddress {
    street: string;
    postalCode: string;
    city: string;
    state: string;
    country: string;
}


interface IUpdateAddress {
    id: number;
    street: string;
    postalCode: string;
    city: string;
    state: string;
    country: string;
}



export { type ICreateAddress, type IUpdateAddress };



