import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { ValidationErrors } from "$lib/common/utils/types";
import { PurchaseInvoiceUtils, } from "../utils/PurchaseInvoiceUtils";
import type { IFactoryGate } from "$lib/factory_gates/models/IFactoryGate";
import type { IStorageLocation } from "$lib/storage_locations/models/IStorageLocation";
import type { ISupplier } from "$lib/supplier/models/ISupplier";
import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
import type { ICreateManualDebitNotePayload, IDebitNoteData, IPurchaseInvoice, IPurchaseInvoiceAddNewItemRequest, IPurchaseInvoiceDetailed, IPurchaseInvoiceRequest, IPurchaseInvoiceUpdateRequest } from "../models/IPurchaseInvoice";
import type { IPurchaseInvoicePresenter } from "./IPurchaseInvoicePresenter";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class PurchaseInvoicePresenter implements IPurchaseInvoicePresenter {


    getById(id: number): Promise<DTO<IPurchaseInvoiceDetailed>> {
        return RepoProvider.purchaseInvoiceRepo.getById(id);

    }

    addNewItems(payload: IPurchaseInvoiceAddNewItemRequest): Promise<DTO<null>> {
        return RepoProvider.purchaseInvoiceRepo.addNewItems(payload);
    }

    // getAll(page: number, pageSize: number): Promise<DTO<IRawMaterialStock[]>> {
    //     return RepoProvider.rawMaterialStockRepo.getAll(page, pageSize);
    // }

    onSubmit(payload: IPurchaseInvoiceRequest): Promise<DTO<null>> {
        return RepoProvider.purchaseInvoiceRepo.create(payload);
    }
    onUpdate(payload: IPurchaseInvoiceUpdateRequest): Promise<DTO<null>> {
        return RepoProvider.purchaseInvoiceRepo.update(payload);
    }
    onUpdateFull(payload: IPurchaseInvoiceUpdateRequest): Promise<DTO<null>> {
        return RepoProvider.purchaseInvoiceRepo.updateFull(payload);
    }

    onValidate(payload: IPurchaseInvoiceRequest): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = PurchaseInvoiceUtils.creationSchema.safeParse(payload);
        console.log(result);

        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }


    getStorageLocations(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>> {
        return RepoProvider.storageLocationRepo.getAll(page, pageSize);
    }

    getFactoryGates(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IFactoryGate>>> {
        return RepoProvider.factoryGatesRepo.getAll(page, pageSize);
    }

    getRawMaterialDetails(id: number): Promise<DTO<IRawMaterialVariation>> {
        return RepoProvider.rawMaterialRepo.getRawMaterialVariationById(id);
    }
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseInvoice>>> {
        return RepoProvider.purchaseInvoiceRepo.getAll(page, pageSize);
    }

    getByDate(startDate: string, endDate: string, page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseInvoice>>> {
        return RepoProvider.purchaseInvoiceRepo.getByDate(startDate, endDate, page, pageSize);
    }
    searchByAny(text: string, page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseInvoice>>> {
        return RepoProvider.purchaseInvoiceRepo.searchByAny(text, page, pageSize);
    }

    createManualDebitNote(payload: ICreateManualDebitNotePayload): Promise<DTO<null>> {
        return RepoProvider.purchaseInvoiceRepo.createManualDebitNote(payload)
    }

}