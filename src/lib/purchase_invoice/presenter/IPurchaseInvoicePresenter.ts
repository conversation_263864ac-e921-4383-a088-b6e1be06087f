import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IFactoryGate } from "$lib/factory_gates/models/IFactoryGate";
import type { IStorageLocation } from "$lib/storage_locations/models/IStorageLocation";
import type { ISupplier } from "$lib/supplier/models/ISupplier";
import type { ValidationErrors } from "../../common/utils/types";
import type { ICreateManualDebitNotePayload, IDebitNoteData, IPurchaseInvoice, IPurchaseInvoiceAddNewItemRequest, IPurchaseInvoiceDetailed, IPurchaseInvoiceRequest, IPurchaseInvoiceUpdateRequest } from "../models/IPurchaseInvoice";


export interface IPurchaseInvoicePresenter {
    onSubmit(payload: IPurchaseInvoiceRequest): Promise<DTO<null>>;
    onUpdate(payload: IPurchaseInvoiceUpdateRequest): Promise<DTO<null>>;
    onUpdateFull(payload: IPurchaseInvoiceUpdateRequest): Promise<DTO<null>>;
    onValidate(payload: IPurchaseInvoiceRequest): ValidationErrors;
    getStorageLocations(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>>;
    getFactoryGates(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IFactoryGate>>>;
    getById(id: number): Promise<DTO<IPurchaseInvoiceDetailed>>;
    addNewItems(payload: IPurchaseInvoiceAddNewItemRequest): Promise<DTO<null>>;
    // validateMannualDebits(payload: IDebitNoteData[]): Map<string,string>;
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseInvoice>>>;
    getByDate(startDate: string, endDate: string, page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseInvoice>>>;
    searchByAny(text: string, page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseInvoice>>>;
    createManualDebitNote(payload: ICreateManualDebitNotePayload): Promise<DTO<null>>
}
