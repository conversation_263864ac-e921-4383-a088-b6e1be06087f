<script lang="ts">
    import { Spinner } from "flowbite-svelte";
    import { debounce, formatDateUI, showErrorToast } from "$lib/common/utils/common-utils";
    import { onMount } from "svelte";
    import { goto } from "$app/navigation";
    import Loader from "$lib/common/components/Loader.svelte";
    import type { IPurchaseInvoice } from "../models/IPurchaseInvoice";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";

    export let paginationData: PaginatedDataWrapper<IPurchaseInvoice>;
    export let searchTerm: string = "";

    export let onDateFilterChange: (
        filter: {
            startDate: string;
            endDate: string;
            currentFilter: "today" | "week" | "month";
        } | null
    ) => void;

    export let currentFilter: "today" | "week" | "month" | undefined;
    export let onSearchClear: () => void = () => {};

    let isLoading: boolean = false;
    let searchLoading: boolean = false;
    let isSearching: boolean = false;
    let filteredData: IPurchaseInvoice[] = [];
    let alreadyFetchedData: IPurchaseInvoice[] = [];

    let debounceSearch = debounce(async (e: any) => {
        if (searchTerm.trim().length === 0 && !currentFilter) {
            paginationData.searchText = undefined;
            onSearchClear();
            return;
        }
        if (e.target.value.trim().length > 2) {
            isSearching = true;

            paginationData.searchText = e.target.value.trim();

            const result = await PresenterProvider.purchaseInvoicePresenter.searchByAny(
                paginationData.searchText!,
                paginationData.pagination.currentPage,
                paginationData.pageSize
            );
            if (!result.success) {
                return showErrorToast(result.message);
            } else {
                filteredData = result.data.data;

                paginationData.pagination = result.data;

                isSearching = false;
            }
        } else {
            filteredData = alreadyFetchedData;
        }
    }, 300);

    const getDateOrMonthFormat = (num: number) => {
        return (num > 9 ? num : "0" + num).toString();
    };

    const weekFilter = () => {
        const now = new Date();
        let currentDayOfWeek = now.getDay();
        const diffToSunday = now.getDate() - currentDayOfWeek;
        const startDate = new Date(now.setDate(diffToSunday));
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);

        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        return {
            startDate:
                startDate.getFullYear() +
                "-" +
                getDateOrMonthFormat(startDate.getMonth() + 1) +
                "-" +
                getDateOrMonthFormat(startDate.getDate()),
            endDate:
                endDate.getFullYear() +
                "-" +
                getDateOrMonthFormat(endDate.getMonth() + 1) +
                "-" +
                getDateOrMonthFormat(endDate.getDate() + 1),
        };
    };

    onMount(() => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;

        searchTerm = paginationData.searchText || "";
    });
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <h1
        class="mb-4 font-primary text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
    >
        Purchase Invoices
    </h1>

    <div class="relative overflow-x-auto font-primary shadow-md sm:rounded-lg">
        <div
            class="flex-column flex flex-wrap items-center justify-between space-y-4 rounded-lg bg-[#E3E3E3] p-4 text-sm focus:ring-primary-500 dark:bg-[#131313] md:flex-row md:space-y-0"
        >
            <div>
                <label for="table-search" class="sr-only">Search</label>

                <div class="relative">
                    <div
                        class="rtl:inset-r-0 pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
                    >
                        <img src="/images/svg/search.svg" alt="l" width="15px" />
                    </div>
                    <input
                        type="text"
                        id="franchise-search"
                        bind:value={searchTerm}
                        on:input={debounceSearch}
                        class="block w-96 rounded-lg p-2 ps-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:bg-primary-800 dark:text-white"
                        placeholder="Search by PO, Invoice number or Supplier"
                    />
                    {#if isSearching}
                        <div
                            class="absolute right-0 top-0 bottom-0 flex items-center justify-center"
                        >
                            <Loader />
                        </div>
                    {/if}
                </div>
            </div>

            <div class="space-x-4">
                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    type="button"
                    on:click={() => {
                        goto("/admin/purchase-invoices/add");
                    }}
                >
                    Add New
                </button>
            </div>
        </div>
        <div class="my-3">
            <span class="mx-3">Filter By Date Range</span>

            <CustomButton
                title="Today"
                cssClass={currentFilter === "today" ? "!bg-primary-500" : "!bg-gray-400"}
                onClick={() => {
                    if (currentFilter === "today") {
                        onDateFilterChange(null);
                        return;
                    }
                    const today = new Date();

                    onDateFilterChange({
                        startDate:
                            today.getFullYear() +
                            "-" +
                            getDateOrMonthFormat(today.getMonth() + 1) +
                            "-" +
                            getDateOrMonthFormat(today.getDate()),
                        endDate:
                            today.getFullYear() +
                            "-" +
                            getDateOrMonthFormat(today.getMonth() + 1) +
                            "-" +
                            getDateOrMonthFormat(today.getDate() + 1),
                        currentFilter: "today",
                    });
                }}
            />

            <CustomButton
                title="This Week"
                cssClass={currentFilter === "week" ? "!bg-primary-500" : "!bg-gray-400"}
                onClick={() => {
                    if (currentFilter === "week") {
                        onDateFilterChange(null);
                        return;
                    }

                    onDateFilterChange({
                        ...weekFilter(),
                        currentFilter: "week",
                    });
                }}
            />

            <CustomButton
                title="This Month"
                cssClass={currentFilter === "month" ? "!bg-primary-500" : "!bg-gray-400"}
                onClick={() => {
                    if (currentFilter === "month") {
                        onDateFilterChange(null);
                        return;
                    }
                    const today = new Date();

                    onDateFilterChange({
                        startDate:
                            today.getFullYear() +
                            "-" +
                            getDateOrMonthFormat(today.getMonth() + 1) +
                            "-" +
                            "01",
                        endDate:
                            today.getFullYear() +
                            "-" +
                            getDateOrMonthFormat(today.getMonth() + 1) +
                            "-" +
                            31,
                        currentFilter: "month",
                    });
                }}
            />
        </div>

        {#if searchLoading}
            <div class="flex h-[30rem] w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <div class="max-h-[46vh] overflow-y-auto">
                <table
                    class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right"
                >
                    <thead
                        class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                    >
                        <tr>
                            <th scope="col" class="px-6 py-3">SR No.</th>
                            <th scope="col" class="px-6 py-3">Entry No.</th>
                            <th scope="col" class="px-6 py-3">Supplier</th>
                            <th scope="col" class="px-6 py-3">PO Number</th>
                            <th scope="col" class="px-6 py-3">Invoice Number</th>
                            <th scope="col" class="px-6 py-3">Invoice Date</th>
                            <th scope="col" class="px-6 py-3">Created/ Entry Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each filteredData as row, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-invoices/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {(paginationData.pagination.currentPage - 1) *
                                            paginationData.pageSize +
                                            index +
                                            1}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-invoices/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {#if row.entryId.length > 0}
                                            {"USI-PI-" + row.entryId.toUpperCase()}
                                        {/if}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-invoices/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.supplier.toUpperCase()}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-invoices/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.poNumber.toUpperCase()}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-invoices/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.invoiceNumber.toUpperCase()}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-invoices/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {formatDateUI(row.invoiceDate, false)}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-invoices/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {formatDateUI(row.createdAt)}
                                    </a>
                                </td>
                            </tr>
                        {/each}
                        {#if filteredData.length === 0}
                            <tr class="font-medium text-black dark:text-gray-400">
                                <td colspan="6" class="h-[50vh] text-center">
                                    {searchTerm.trim().length > 0
                                        ? "No results found"
                                        : "Yet no purchase invoices"}
                                </td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>
        {/if}
    </div>
    <PaginationButtons {paginationData} />
{/if}
