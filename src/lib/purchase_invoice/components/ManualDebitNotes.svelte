<script lang="ts">
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { onMount } from "svelte";
    import { PurchaseInvoiceUtils } from "../utils/PurchaseInvoiceUtils";
    import Input from "flowbite-svelte/Input.svelte";
    import type {
        ICreateManualDebitNotePayload,
        IDebitNoteData,
        IManualDebitNotePayload,
        IRejectedItemDetails,
    } from "../models/IPurchaseInvoice";
    import {
        capitalizeInitials,
        showErrorToast,
        showSuccessToast,
    } from "$lib/common/utils/common-utils";
    import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
    import IdTitleLocalSearch from "$lib/common/components/IdTitleLocalSearch.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { type IndexedValidationErrors } from "$lib/common/utils/types";
    import { goto } from "$app/navigation";

    export let purchaseInv = PurchaseInvoiceUtils.getEmpty();
    export let debitNotes: IDebitNoteData[] = [];
    export let rawMaterials: IdTitle[] = [];
    export let rejectionData: IRejectedItemDetails[] = [];

    let isSavingManualDebitNote: boolean = false;

    let payload: ICreateManualDebitNotePayload = {
        purchaseInvoiceId: -1,
        debitNotes: [],
    };
    let selectedRawMaterials: Map<string, IdTitle> = new Map();

    let errorMap: IndexedValidationErrors = new Map();

    let isShow: boolean = false;

    const _onRawMaterialSelected = (data: IdTitle | null, index: number) => {
        if (data) {
            if (payload.debitNotes.findIndex((item) => item.rawMaterialId === data.id) !== -1) {
                showErrorToast("This item is already selected.");
                return;
            }
            selectedRawMaterials.set(index.toString(), data);
            payload.debitNotes[index].rawMaterialId = data.id;
        } else {
            selectedRawMaterials.delete(index.toString());
            payload.debitNotes[index].rawMaterialId = -1;
        }
        selectedRawMaterials = selectedRawMaterials;
    };

    const _onRejectedQtyInput = (e: Event, index: number) => {
        const userInput = (e.target as HTMLInputElement).value;

        /* check if rejection qty is valid */
        const isNumeric = !isNaN(Number(userInput)) && Number(userInput) >= 0;
        if (!isNumeric) {
            payload.debitNotes[index].rejectedQty = 0;
        } else {
            const rejectionQty = Number(userInput);

            payload.debitNotes[index].rejectedQty = rejectionQty;
        }
    };

    const _onRejectedReasonInput = (e: Event, index: number) => {
        const userInput = (e.target as HTMLInputElement).value;

        const reason = userInput.trim();
        if (reason.length > 7) {
            payload.debitNotes[index].rejectedReason = reason;
        } else {
            payload.debitNotes[index].rejectedReason = "";
        }
    };

    const _onValidate = () => {
        errorMap = new Map();
        let allowedRejectionQty = 0;
        let item: IManualDebitNotePayload;
        let foundItem;
        for (let i = 0; i < payload.debitNotes.length; i++) {
            item = payload.debitNotes[i];

            if(!errorMap.has(i))
        {
            errorMap.set(i,[]);
        }

            if (item.rawMaterialId === -1) {
                errorMap.get(i)!.push({
                    fieldName: `rawMaterialId`,
                    errorMessage: "Please select a raw material.",
                });
            }

            foundItem = rejectionData.find((data) => data.itemId === item.rawMaterialId);
            if (foundItem) {
                allowedRejectionQty = foundItem!.receivedQty - foundItem!.rejectedQty;
            }

            if (item.rejectedQty > allowedRejectionQty) {
                errorMap.get(i)!.push({
                    fieldName: `rejectedQty`,
                    errorMessage: "Can reject upto " + allowedRejectionQty + " only.",
                });
            } else if (item.rejectedQty <= 0) {
                errorMap.get(i)!.push({
                    fieldName: `rejectedQty`,
                    errorMessage: "Please enter a valid quantity.",
                });
            }

            if (item.rejectedReason.length < 8) {
                errorMap.get(i)!.push({
                    fieldName: `rejectedReason`,
                    errorMessage: "Please enter a reason with at least 8 characters.",
                });
            }
        }
        errorMap = new Map([...errorMap].filter((item) => item[1].length > 0));

        return errorMap;
    };

    const saveManualDebitNote = async () => {
        if (payload.debitNotes.length === 0) {
            showErrorToast("Please add at least one manual debit note.");
            return;
        }

        errorMap = _onValidate();
        console.log(errorMap);
        
        if (errorMap.size > 0) {
            showErrorToast("Please fix the errors before saving.");
            return;
        }

        isSavingManualDebitNote = true;

        let res = await PresenterProvider.purchaseInvoicePresenter.createManualDebitNote(payload);
        if (res.success) {
            showSuccessToast("Manual debit note saved successfully.");
            goto("/admin/debit-notes");
        } else {
            showErrorToast(res.message ?? "Something went wrong.");
        }
        isSavingManualDebitNote = false;
    };

    onMount(() => {
        payload.purchaseInvoiceId = purchaseInv.id;
    });
</script>

<div class="my-3">
    <h2 class="text-lg font-bold mb-2">Existing Debit Notes</h2>
    <table
        class="w-full mt-2 text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right border"
    >
        <thead
            class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
        >
            <tr>
                <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">SR No.</th>
                <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Item</th>
                <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Rejected Qty.</th>
                <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Rejected Reason</th>
                <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Type</th>
            </tr>
        </thead>
        <tbody>
            {#each debitNotes as note, index}
                <tr
                    class="border-b bg-white text-gray-600 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400"
                >
                    <td class="px-6 py-4">{index + 1}</td>
                    <td class="px-6 py-4">{capitalizeInitials(note?.rawMaterial?.name || "")}</td>
                    <td class="px-6 py-4">{note.qty}</td>
                    <td class="px-6 py-4">{note.note}</td>
                    <td class="px-6 py-4">{note.is_manual ? "Manual" : "Automatic"}</td>
                </tr>
            {/each}
        </tbody>
    </table>

    <h2 class="text-lg font-bold mt-6 mb-2">Add New Manual Debit Note</h2>
    <CustomButton
        title="Add Manual Debit Notes"
        onClick={() => {
            if (payload.debitNotes.length === 0) {
                payload.debitNotes.push({
                    rawMaterialId: -1,
                    rejectedQty: 0,
                    rejectedReason: "",
                });
            }
            isShow = !isShow;
        }}
    />
    {#if isShow}
        <table
            class="w-full mt-2 text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right border"
        >
            <thead
                class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
            >
                <tr>
                    <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">SR No.</th>
                    <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Item</th>
                    <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Rejected Qty.</th>
                    <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Rejected Reason</th>
                </tr>
            </thead>
            <tbody>
                {#each payload.debitNotes as item, index}
                    <tr
                        class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                    >
                        <td class="px-6 py-4">{index + 1}</td>
                        <td class="px-6 py-4">
                            <IdTitleLocalSearch
                                placeholder="Search Raw Material"
                                selected={selectedRawMaterials.get(index.toString()) ?? null}
                                searchData={rawMaterials}
                                onSelected={(data) => {
                                    _onRawMaterialSelected(data, index);
                                }}
                            />

                            {#if errorMap.has(index) && errorMap.get(index)!.find((item) => item.fieldName === `rawMaterialId`)}
                                <span class="text-red-500 italic text-sm">
                                    {errorMap.get(index)!.find((item) => item.fieldName === `rawMaterialId`)!.errorMessage}
                                </span>
                            {/if}
                        </td>
                        <td class="px-6 py-4">
                            <Input
                                class=""
                                type="number"
                                value={item.rejectedQty}
                                on:input={(e) => {
                                    _onRejectedQtyInput(e, index);
                                }}
                            />
                            {#if errorMap.has(index) && errorMap.get(index)!.find((item) => item.fieldName === `rejectedQty`)}
                                <span class="text-red-500 italic text-sm">
                                    {errorMap.get(index)!.find((item) => item.fieldName === `rejectedQty`)!.errorMessage}
                                </span>
                            {/if}
                        </td>
                        <td class="px-6 py-4">
                            <Input
                                class=""
                                type="text"
                                value={item.rejectedReason}
                                on:input={(e) => {
                                    _onRejectedReasonInput(e, index);
                                }}
                            />

                            {#if errorMap.has(index) && errorMap.get(index)!.find((item) => item.fieldName === `rejectedReason`)}
                                <span class="text-red-500 italic text-sm">
                                    {errorMap.get(index)!.find((item) => item.fieldName === `rejectedReason`)!.errorMessage}
                                </span>
                            {/if}
                        </td>
                        <td
                            on:click={() => {
                                payload.debitNotes.splice(index, 1);
                                payload.debitNotes = [...payload.debitNotes];
                            }}
                            class="px-6 py-4 flex justify-center items-center text-2xl font-bold cursor-pointer hover:text-red-600"
                        >
                            x
                        </td>
                    </tr>
                {/each}
            </tbody>
        </table>
        <div class="flex justify-between mt-3">
            <CustomButton
                title="Add New Item"
                onClick={() => {
                    payload.debitNotes.push({
                        rawMaterialId: -1,
                        rejectedQty: 0,
                        rejectedReason: "",
                    });
                    payload.debitNotes = [...payload.debitNotes];
                }}
            />
            <CustomButton
                title="Save"
                onClick={saveManualDebitNote}
                isLoading={isSavingManualDebitNote}
            />
        </div>
    {/if}
</div>
