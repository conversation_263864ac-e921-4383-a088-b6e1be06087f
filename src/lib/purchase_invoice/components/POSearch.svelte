<script lang="ts">
    import CustomModal from "$lib/common/components/CustomModal.svelte";
    import SearchWithDrop from "$lib/common/components/SearchWithDrop.svelte";
    import { debounce } from "$lib/common/utils/common-utils";
    import type { IPurchaseOrder } from "$lib/purchase-order/models/IPurchaseOrder";
    import RawMaterialForm from "$lib/raw_material/components/RawMaterialForm.svelte";
    import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
    import { RawMaterialUtils } from "$lib/raw_material/utils/RawMaterialUtils";
    import { RepoProvider } from "$lib/RepoProvider";

    export let isLabel: boolean = true;
    export let selected: IPurchaseOrder | null = null;
    export let onSelected: (data: IPurchaseOrder) => void;
    export let disabled: boolean = false;
    export let showAddNew: boolean = false;
    export let onInput: (value:string) => void = (value)=>{
        console.log(value)
        
    };

    let searchTerm: string = "";
    let fetchedData: IPurchaseOrder[] = [];
    let showAddNewModal: boolean = false;
    let isSearching = false;

    const getData = async () => {
        isSearching = true;
        const res = await RepoProvider.purchaseOrderRepo.searchByPO(
            searchTerm,
        );
        
      
        if (showAddNew && fetchedData.length === 0) {
            // const obj = RawMaterialUtils.getEmpty();
            // obj.name = "Add new";
            // fetchedData = [obj];
        }
        isSearching = false;
        if (res.success) {
            fetchedData = res.data;
        }
    };

    const debounceSearch = debounce(getData, 600);

    const doSearch = (search: string) => {
        onInput(search);
        searchTerm = search; // Update the search term
        debounceSearch(); // Call the debounced search
    };

    const addNew = () => {
        showAddNewModal = true;
    };
</script>

<SearchWithDrop
    {isLabel}
    {disabled}
    bind:searchTerm
    keepUserInput={true}
    loading={isSearching}
    level="poNumber"
    searchInput={doSearch}
    selected={selected?.poNumber.toUpperCase() ?? null}
    selectedObj={selected}
    selectedFunc={async(data) => {
        fetchedData = [];
        searchTerm = "";
        if (data) {
            if (data.id === -1) {
                return addNew();
            }
        }
        onSelected(data);
    }}
    bind:filteredData={fetchedData}
/>
<!-- 
<CustomModal title="Add New RawMaterial" bind:showModal={showAddNewModal}>
    <RawMaterialForm
        isInsideModal={true}
        suppliers={selectedPO
            ? [
                  {
                      supplier: {
                          id: selectedPO.id,
                          title: selectedPO.name,
                      },
                      price: 0,
                      moq: 0,
                  },
              ]
            : []}
        onSubmitSuccess={(data) => {
            fetchedData = [];
            searchTerm = "";
            if (!selectedPO) {
                onSelected(data);
            }
            showAddNewModal = false;
        }}
    />
</CustomModal> -->
