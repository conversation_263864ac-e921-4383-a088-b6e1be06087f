import type { IPurchaseOrder } from "$lib/purchase-order/models/IPurchaseOrder";
import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
import type { ISupplier } from "$lib/supplier/models/ISupplier";


enum PURCHASE_INVOICE_STATUS {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    DELETED = 'deleted'
}

interface IPurchaseInvoiceRequest {
    invoiceNumber: string;
    invoiceDate: Date;
    poNumber: string;
    poDate: Date;
    purchasedById: number;
    supplierId: number;
    factoryGateId: number;
    rawMaterials: IPurchaseInvoiceReceivedItem[];
    purchaseOrderId: number|null;
}

interface IPurchaseInvoiceUpdateRequest extends IPurchaseInvoiceRequest {
    id: number;
}

interface IPurchaseInvoiceReceivedItem {
    rawMaterialId: number;
    storageLocationId: number | null;
    totalQty: number;
    price: number;
    rejectedQty: number;
    rejectionReason: string | null;
    rejectedById: number | null;
    holdQty: number;
    holdReason: string | null;
    holdById: number | null;
}

interface IPurchaseInvoiceReceivedItemDetails {
    rawMaterialId: number;
    rawMaterial: string;
    unit: string;
    storageLocationId: number | null;
    totalQty: number;
    excessQty:number;
    replaceableQty: number;
    price: number;
    rejectedQty: number;
    rejectionReason: string | null;
    rejectedById: number | null;
    holdQty: number;
    holdReason: string | null;
    holdById: number | null;
}

interface IPurchaseInvoiceReceivedItemInfo {
    rawMaterialId: number;
    rawMaterial: string;
    unitName: string;
    price: number;
}


interface IDebitNoteData {
    rawMaterialId: number;
    rejectedQty: number;
    rejectedReason: string;
    qty?: number;
    note?: string;
    is_manual?: boolean;
    rawMaterial?: IRawMaterialVariation
    searchTerm?: string;
}


interface IPurchaseInvoice extends IPurchaseInvoiceRequest {
    id: number;
    supplier: string;
    entryId: string;
    createdAt: Date;
}


interface IPurchaseInvoiceDetailed extends IPurchaseInvoiceUpdateRequest {
    [x: string]: any;
    supplier: ISupplier;
    rawMaterials: IPurchaseInvoiceReceivedItemDetails[];
    purchaseOrder:IPurchaseOrder | null;
}


interface IPurchaseInvoiceAddNewItemRequest {
    id: number;
    rawMaterials: IPurchaseInvoiceReceivedItem[];
}


interface IManualDebitNotePayload {
    rawMaterialId: number;
    rejectedQty: number;
    rejectedReason: string;

}

interface ICreateManualDebitNotePayload {
    purchaseInvoiceId: number,
    debitNotes: IManualDebitNotePayload[],
}


interface IRejectedItemDetails {
    itemId: number;
    receivedQty: number;
    rejectedQty: number;
}



export { type IDebitNoteData, type IPurchaseInvoice, type IPurchaseInvoiceRequest, type IPurchaseInvoiceReceivedItem, PURCHASE_INVOICE_STATUS, type IPurchaseInvoiceAddNewItemRequest, type IPurchaseInvoiceDetailed, type IPurchaseInvoiceUpdateRequest, type IPurchaseInvoiceReceivedItemInfo, type ICreateManualDebitNotePayload, type IRejectedItemDetails, type IManualDebitNotePayload ,type IPurchaseInvoiceReceivedItemDetails};