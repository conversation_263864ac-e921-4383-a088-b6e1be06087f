<script lang="ts">
    import { onMount } from "svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import Loader from "$lib/common/components/Loader.svelte";
    import type { OperationalLogs } from "../models/OperationalLogs";
    import {
        debounce,
        formatDateUI,
        parseDate,
        splitCamelCase,
    } from "$lib/common/utils/common-utils";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";

    export let paginationData: PaginatedDataWrapper<OperationalLogs>;

    let modelOpen: boolean = false;
    let isNoMatchFound: boolean = false;
    let deletedObjectRow: OperationalLogs;
    let isLoading: boolean = false;
    let filteredData: OperationalLogs[] = [];
    let alreadyFetchedData: OperationalLogs[] = [];
    const handleDelete = async () => {
        modelOpen = false;
        isLoading = true;

        // logsArr = logsArr.filter((item) => item.id !== deletedObjectRow.id);

        // const response = await PresenterProvider.organizationPresenter.onDelete(deletedObjectRow);
        // if (!response.success) {
        //     showErrorToast(response.message ?? "Organization Deletion unsuccessful");
        //     isLoading = false;
        //     return;
        // }
        // showSuccessToast("Organization Deleted SuccessFully");

        isLoading = false;
    };

    // const setAllData = async () => {
    //     isLoading = true;
    //     const res = await PresenterProvider.operationalLogsPresenter.onLoad();
    //     console.log(res);

    //     if (res.success && res.data && res.data.data && res.data.data) {
    //         logsArr = res.data.data;
    //         console.log(logsArr);
    //     }
    //     isLoading = false;
    // };

    onMount(() => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;
    });
</script>

<h1
    class="mb-4 font-sans text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
>
    Operational Logs List
</h1>

<div class="relative overflow-x-auto shadow-md sm:rounded-lg">
    <div
        class="flex-column flex flex-wrap items-center justify-between space-y-4 rounded-lg bg-[#E3E3E3] p-4 text-sm focus:ring-primary-500 dark:bg-[#131313] md:flex-row md:space-y-0"
    >
        <label for="table-search" class="sr-only">Search</label>
        <div class="relative">
            <div
                class="rtl:inset-r-0 pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
            >
                <svg
                    class="h-4 w-4 text-gray-500 dark:text-gray-400"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 20 20"
                >
                    <path
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                    />
                </svg>
            </div>
            <input
                type="text"
                id="order-search"
                class="block w-96 rounded-lg p-2 ps-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:bg-primary-800 dark:text-white"
                placeholder="Search.."
            />
        </div>
        <!-- <div class="space-x-4">
            {#if canAdminPerformAction($userData, ADMIN_ROLES.REGION_CREATE)}
                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class=" inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    type="button"
                    on:click={() => {
                        goto("/admin/organization/add");
                    }}
                >
                    New Organization
                </button>
            {/if}
                        </div> -->
    </div>
    {#if isLoading}
        <div class="flex h-[30rem] w-full items-center justify-center">
            <Loader />
        </div>
    {:else if !isNoMatchFound}
        <div class="max-h-[55vh] overflow-y-auto">
            <table class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
                <thead
                    class="bg-gray-50 text-xs uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                >
                    <tr>
                        <th scope="col" class="px-6 py-3">SR No.</th>
                        <th scope="col" class="px-6 py-3">Id</th>
                        <th scope="col" class="px-6 py-3">Action</th>
                        <th scope="col" class="px-6 py-3">Record Id</th>
                        <th scope="col" class="px-6 py-3">User Id</th>
                        <th scope="col" class="px-6 py-3">Model</th>
                        <th scope="col" class="px-6 py-3">Date</th>
                    </tr>
                </thead>
                <tbody>
                    {#if filteredData.length > 0}
                        {#each filteredData as row, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-6 py-4">
                                    <span
                                        class="block h-full w-full whitespace-nowrap font-medium text-gray-600 dark:text-gray-400 dark:hover:bg-gray-600"
                                    >
                                        {index + 1}
                                    </span>
                                </td>

                                <td class="px-6 py-4">
                                    <span
                                        class="block h-full w-full whitespace-nowrap font-medium text-gray-600 dark:text-gray-400 dark:hover:bg-gray-600"
                                    >
                                        {row.id}
                                    </span>
                                </td>

                                <td class="px-6 py-4">
                                    <span
                                        class="block h-full w-full whitespace-nowrap font-medium text-gray-600 dark:text-gray-400 dark:hover:bg-gray-600"
                                    >
                                        {row.action}
                                    </span>
                                </td>

                                <td class="px-6 py-4">
                                    <span
                                        class="block h-full w-full whitespace-nowrap font-medium text-gray-600 dark:text-gray-400 dark:hover:bg-gray-600"
                                    >
                                        {row.recordId}
                                    </span>
                                </td>

                                <td class="px-6 py-4">
                                    <span
                                        class="block h-full w-full whitespace-nowrap font-medium text-gray-600 dark:text-gray-400 dark:hover:bg-gray-600"
                                    >
                                        {row.userId}
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <span
                                        class="block h-full w-full whitespace-nowrap font-medium uppercase text-gray-600 dark:text-gray-400 dark:hover:bg-gray-600"
                                    >
                                        {splitCamelCase(row.model)}
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <span
                                        class="block h-full w-full whitespace-nowrap font-medium uppercase text-gray-600 dark:text-gray-400 dark:hover:bg-gray-600"
                                    >
                                        {formatDateUI(row.createdAt)}
                                    </span>
                                </td>
                            </tr>
                        {/each}
                    {:else}
                        <tr class="left-[40%] font-medium text-black dark:text-gray-400">
                            <td colspan="8" class="h-[50vh] text-center">
                                No Operational Logs Found !
                            </td>
                        </tr>
                    {/if}
                </tbody>
            </table>
        </div>
    {:else}
        <div class="flex h-[60vh] items-center justify-center text-sm">No Match Found!</div>
    {/if}
</div>
<PaginationButtons {paginationData} />
