import { type DTO, getHandledErrorDTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { IOperationalLogsPresenter } from "./IOperationalLogsPresenter";
import type { OperationalLogs } from "../models/OperationalLogs";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class OperationalLogsPresenter implements IOperationalLogsPresenter {


    async getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<OperationalLogs>>> {
        return RepoProvider.operationalLogsRepo.getAll(page, pageSize);
    }


    async getById(itemId: string): Promise<DTO<OperationalLogs>> {
        try {
            return await RepoProvider.operationalLogsRepo.getOperationalLogById(itemId);
        } catch (error: any) {
            return getHandledErrorDTO(error.message || "An error occurred ");
        }
    }

}
