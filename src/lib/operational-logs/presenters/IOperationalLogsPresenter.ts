import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { OperationalLogs } from "../models/OperationalLogs";

interface IOperationalLogsPresenter {
    getById(itemId: string): Promise<DTO<OperationalLogs>>;
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<OperationalLogs>>>;
}

export { type IOperationalLogsPresenter };
