import { LOGS_API_PATH, OPERATION_LOGS, ORGANIZATION_API } from "$lib/common/configs/serverConfig";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    getUnhandledErrorDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { handleError } from "$lib/common/utils/logging";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { OperationalLogs } from "../models/OperationalLogs";
import type { IOperationalLogsRepo } from "./IOperationalLogsRepo";

export class OperationalLogsRepo implements IOperationalLogsRepo {
    private _apiPath: string = LOGS_API_PATH;

   

    async getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<OperationalLogs>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<OperationalLogs>> = await fetchData(this._apiPath + "/?page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getOperationalLogById(id: string): Promise<DTO<OperationalLogs>> {
        try {
            const options = {
                method: "GET",
            };
            const res = await fetchData<{ data: OperationalLogs }>(
                ORGANIZATION_API + "/get/" + id,
                options
            );

            if (res.data) {
                return getSuccessDTO(res.data);
            } else {
                return getHandledErrorDTO("Organization Not Found");
            }
        } catch (error: any) {
            handleError(error);
            return getHandledErrorDTO(error.message);
        }
    }

  

    async getAllOperationalLogs(): Promise<DTO<PaginatedBaseResponse<OperationalLogs>>> {
        try {
            const options = {
                method: "GET",
            };
            const res = await fetchData<DTO<PaginatedBaseResponse<OperationalLogs>>>(
                OPERATION_LOGS + "/list?page=1&limit=10",
                options
            ); 
            console.log("*****************************", res);
            return res;
        } catch (error: any) {
            handleError(error);
            return getHandledErrorDTO(error.message);
        }
    }
}
