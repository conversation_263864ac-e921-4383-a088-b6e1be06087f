import { z } from "zod";
import { FACTORY_GATE_STAUS, type IFactoryGate } from "../models/IFactoryGate";

export abstract class FactoryGateUtils {
    static getEmpty(): IFactoryGate {
        return {
            id: -1,
            name: "",
            description: null,
            status: FACTORY_GATE_STAUS.ACTIVE,
            createdAt: new Date(),
            updatedAt: null,
            deletedAt: null,
            createdBy: '',
            updatedBy: null,
            deletedBy: null,
        }
    }


    static creationSchema = z.object({
        name: z.string().min(3, 'Name must be at least 3 characters long').max(50, 'Name must be less than 50 characters long'),
        description: z.string().optional().nullable(),

    });
}