<script lang="ts">
    import { Select } from "flowbite-svelte";
    import { onMount } from "svelte";
    import CustomModal from "$lib/common/components/CustomModal.svelte";
    import type { IFactoryGate } from "../models/IFactoryGate";
    import { FactoryGateUtils } from "../utils/FactoryGateUtils";
    import FactoryGateForm from "./FactoryGateForm.svelte";

    export let data: IFactoryGate[] = [];
    export let id: string = "";
    export let cssClass: string = "";
    export let selectedValue: number = 0;
    export let onSelected: (data: any) => void = () => {};
    export let disabled: boolean = false;
    let showAddNewModal: boolean = false;
 
    const pushAddNew = () => {
        const obj = FactoryGateUtils.getEmpty();
        obj.id = -1;
        obj.name = "Add new";
        data.push(obj);

        data = data;
    };

    onMount(() => {
        pushAddNew();
    }); 
</script>

<Select
    {id}
    {disabled}
    class={cssClass}
    items={data.map((item) => ({
        value: item.id.toString(),
        name: item.name.toUpperCase(),
    }))}
    value={selectedValue.toString()}
    on:change={(e: any) => {
        const value = e.currentTarget.value;
        if (value === "-1") {
            selectedValue = 1;
            showAddNewModal = true;
        } else {
            onSelected(value);
        }
    }}
/>

<CustomModal title="Add new factory gate" bind:showModal={showAddNewModal}>
    <FactoryGateForm
        isInsideModal={true}
        onSubmitSuccess={(obj) => {
            data.push(obj);
            data = data.filter((item) => item.id !== -1);
            pushAddNew();
            const id = obj.id;
            selectedValue = id;
            onSelected(id);
            showAddNewModal = false;
        }}
    />
</CustomModal>
