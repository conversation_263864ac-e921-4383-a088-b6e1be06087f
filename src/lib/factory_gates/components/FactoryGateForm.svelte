<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, Select } from "flowbite-svelte";

    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import { FACTORY_GATE_STAUS, type IFactoryGate } from "../models/IFactoryGate";
    import { FactoryGateUtils } from "../utils/FactoryGateUtils";

    export let obj: IFactoryGate = FactoryGateUtils.getEmpty();
    export let isInsideModal: boolean = false;
    export let onSubmitSuccess: null | ((data: IFactoryGate) => void) = null;

    let isDoingTask: boolean = false;

    let validationErrors: Map<string, string> = new Map();

    const handleDescription = (e: any) => {
        obj.description = e.currentTarget.value.trim();
        if (obj.description!.length === 0) {
            obj.description = null;
        }
    };

    const handleSubmit = async () => {
        validationErrors = PresenterProvider.factoryGatesPresenter.onValidate(obj);
        if (validationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        isDoingTask = true;
        let res;
        if (obj.id > 0) {
            res = await PresenterProvider.factoryGatesPresenter.onUpdate(obj.id, obj);
        } else {
            res = await PresenterProvider.factoryGatesPresenter.onSubmit(obj);
        }

        if (res.success) {
            showSuccessToast(`Done`);
            if (onSubmitSuccess) {
                onSubmitSuccess(res.data! as IFactoryGate);
            } else {
                await goto("/admin/factory-gates");
            }
        } else {
            showErrorToast(res.message);
            isDoingTask = false;
        }
    };
</script>

<div class="flex items-center justify-center">
    <div class="w-[90vw] p-2">
        {#if !isInsideModal}
            <div class=" flex items-center justify-between py-2">
                <FormHeader label={obj.id > 0 ? "Edit Factory Gate" : "Add Factory Gate"}
                ></FormHeader>
                <BreadCrumbs breadCrumbData={[]} />
            </div>
            <hr class="mb-5" />
        {/if}

        <div class=" grid grid-cols-2 gap-6">
            <div>
                <Label for="name" class="mb-2 font-sans capitalize tracking-[0px]">
                    Name
                    {#if validationErrors.has("name")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>
                <Input
                    type="text"
                    id="name"
                    placeholder="Name"
                    class="dark:bg-primary-700 {validationErrors.has('name')
                        ? 'border-red-500'
                        : ''}"
                    bind:value={obj.name}
                />
                {#if validationErrors.has("name")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("name")}
                    </p>
                {/if}
            </div>

            <div>
                <Label for="description" class="mb-2 font-sans capitalize tracking-[0px]">
                    Description (Optional)
                    {#if validationErrors.has("description")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>
                <Input
                    type="text"
                    id="description"
                    placeholder="Description (Optional)"
                    class="dark:bg-primary-700 {validationErrors.has('description')
                        ? 'border-red-500'
                        : ''}"
                    value={obj.description}
                    on:change={handleDescription}
                />
                {#if validationErrors.has("description")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("description")}
                    </p>
                {/if}
            </div>
        </div>
        <div class="m-2"></div>

        <div class=" grid grid-cols-2 gap-6">
            {#if obj.id > 0}
                <div>
                    <Label for="status" class="mb-2 font-sans capitalize tracking-[0px]">
                        Status
                        {#if validationErrors.has("status")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Select
                        id="status"
                        class="dark:bg-primary-700 {validationErrors.has('status')
                            ? 'border-red-500'
                            : ''}"
                        items={Object.values(FACTORY_GATE_STAUS).map((item) => ({
                            value: item,
                            name: item.toUpperCase(),
                        }))}
                        bind:value={obj.status}
                    />
                    {#if validationErrors.has("status")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("status")}
                        </p>
                    {/if}
                </div>
            {/if}
        </div>
        <div class="m-2"></div>

        <div class="mt-5 flex w-full justify-end">
            <CustomButton
                onClick={handleSubmit}
                cssClass="w-32 bg-black"
                title={"Save"}
                isLoading={isDoingTask}
            />
        </div>
    </div>
</div>
