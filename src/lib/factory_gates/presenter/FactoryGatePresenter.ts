import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { ValidationErrors } from "$lib/common/utils/types";
import type { IFactoryGatesPresenter, } from "./IFactoryGatePresenter";
import type { IFactoryGate, ICreateFactoryGates } from "../models/IFactoryGate";
import { FactoryGateUtils } from "../utils/FactoryGateUtils";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class FactoryGatesPresenter implements IFactoryGatesPresenter {
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IFactoryGate>>> {
        return RepoProvider.factoryGatesRepo.getAll(page, pageSize);
    }
    searchBytext(text: string): Promise<DTO<PaginatedBaseResponse<IFactoryGate>>> {
        return RepoProvider.factoryGatesRepo.searchByText(text);
    }
    getById(id: number): Promise<DTO<IFactoryGate>> {
        return RepoProvider.factoryGatesRepo.getById(id);
    }
    onSubmit(payload: ICreateFactoryGates): Promise<DTO<IFactoryGate>> {
        return RepoProvider.factoryGatesRepo.create(payload);
    }
    onUpdate(id: number, payload: ICreateFactoryGates): Promise<DTO<boolean>> {
        return RepoProvider.factoryGatesRepo.update(id, payload);
    }
    onDelete(ids: number[]): Promise<DTO<boolean>> {
        return RepoProvider.factoryGatesRepo.delete(ids);
    }
    onValidate(payload: ICreateFactoryGates): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = FactoryGateUtils.creationSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }


}