import type { BaseModel, DeletedMetaData, UpdatedMetaData } from "$lib/common/models/base_model";

interface IFactoryGate extends BaseModel, UpdatedMetaData, DeletedMetaData {
    id: number;
    name: string;
    description: string | null;
    status: FACTORY_GATE_STAUS;
}

interface ICreateFactoryGates {
    name: string;
    description: string | null;
    status: FACTORY_GATE_STAUS;
}
enum FACTORY_GATE_STAUS {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    DELETED = 'deleted'
}



export {
    type ICreateFactoryGates,
    type IFactoryGate,
    FACTORY_GATE_STAUS,

}