<script lang="ts">
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import Loader from "$lib/common/components/Loader.svelte";
    import {
        signInWithEmailAndPassword,
        AuthErrorCodes,
    } from "firebase/auth";
    import { usiFirebaseAuth } from "../../../../firebaseInit";
    import { RepoProvider } from "$lib/RepoProvider";
    import { onMount } from "svelte";
    
    import { dev } from "$app/environment";

    // States
    let isLoading = false;
    let email = "";
    let password = "";
    let showPassword = false;
    let validationMap = new Map();
    let forgotPass: boolean = false;

    const handleEmailLogin = async () => {
        isLoading = true;

        try {
            await signInWithEmailAndPassword(usiFirebaseAuth, email, password);
        } catch (error: any) {
            
            if (error.code === AuthErrorCodes.INVALID_LOGIN_CREDENTIALS) {
                showErrorToast("Invalid email or password");
            } else {
                showErrorToast("Something went wrong");
            }
        } finally {
            // isLoading = false;
        }
    };

    const handleForgotPassword = async () => {
        isLoading = true;
        const res = await RepoProvider.authProvider.sendForgotPasswordLink(email);

        if (res.success) {
            showSuccessToast(
                "If email is registered, you will recieve an email to reset password!"
            );
            forgotPass = false;
        } else {
            showErrorToast(res.message ?? "Failed to send Email!");
        }
        isLoading = false;
    };

    const togglePasswordVisibility = () => {
        showPassword = !showPassword;
    };

    onMount(() => {
        if (dev) {
            email = "<EMAIL>";
            password = "123456";
        }
    });
</script>

<div
    class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8"
>
    <div class="max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-2xl">
        <div class="flex flex-col items-center">
            <img class="h-16 w-auto" src="/images/logo.png" alt="Logo" />
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                {forgotPass ? "Reset Your Password" : "Welcome Back"}
            </h2>
            {#if !forgotPass}
                <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                    Please sign in to your account
                </p>
            {/if}
        </div>

        <div class="mt-8 space-y-6">
            {#if forgotPass}
                <div class="space-y-4">
                    <div>
                        <label
                            for="LoggingEmailAddress"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            Email address
                        </label>
                        <div class="mt-1">
                            <input
                                id="LoggingEmailAddress"
                                type="email"
                                placeholder="Enter your email"
                                class="appearance-none block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[rgb(20,30,51)] focus:border-[rgb(20,30,51)] dark:bg-gray-700 dark:text-white transition-colors duration-200 {validationMap.has(
                                    'email'
                                )
                                    ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                                    : ''}"
                                on:input={() => {
                                    validationMap = new Map();
                                }}
                                on:change={({ currentTarget }) => {
                                    email = currentTarget.value.toLowerCase();
                                }}
                                bind:value={email}
                            />
                        </div>
                    </div>

                    {#if isLoading}
                        <div class="flex justify-center">
                            <Loader />
                        </div>
                    {:else}
                        <button
                            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200"
                            on:click={handleForgotPassword}
                        >
                            Send Reset Link
                        </button>
                    {/if}

                    <div class="text-center">
                        <button
                            class="text-sm text-[rgb(20,30,51)] hover:text-[rgb(30,40,61)] dark:text-[rgb(20,30,51)] dark:hover:text-[rgb(30,40,61)] transition-colors duration-200"
                            on:click={() => {
                                forgotPass = false;
                            }}
                        >
                            Back to Sign In
                        </button>
                    </div>
                </div>
            {:else}
                <div class="space-y-4">
                    <div>
                        <label
                            for="LoggingEmailAddress"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            Email address
                        </label>
                        <div class="mt-1">
                            <input
                                id="LoggingEmailAddress"
                                type="email"
                                placeholder="Enter your email"
                                class="appearance-none block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[rgb(20,30,51)] focus:border-[rgb(20,30,51)] dark:bg-gray-700 dark:text-white transition-colors duration-200 {validationMap.has(
                                    'email'
                                )
                                    ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                                    : ''}"
                                on:input={() => {
                                    validationMap = new Map();
                                }}
                                bind:value={email}
                            />
                        </div>
                    </div>

                    <div>
                        <div class="flex items-center justify-between">
                            <label
                                for="loggingPassword"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300"
                            >
                                Password
                            </label>
                            <button
                                class="text-sm text-[rgb(20,30,51)] hover:text-[rgb(30,40,61)] dark:text-[rgb(20,30,51)] dark:hover:text-[rgb(30,40,61)] transition-colors duration-200"
                                on:click={() => {
                                    forgotPass = true;
                                }}
                            >
                                Forgot password?
                            </button>
                        </div>
                        <div class="mt-1 relative">
                            <input
                                id="loggingPassword"
                                type={showPassword ? "text" : "password"}
                                placeholder="Enter your password"
                                class="appearance-none block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[rgb(20,30,51)] focus:border-[rgb(20,30,51)] dark:bg-gray-700 dark:text-white transition-colors duration-200 {validationMap.has(
                                    'password'
                                )
                                    ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                                    : ''}"
                                on:input={() => {
                                    validationMap = new Map();
                                }}
                                on:keypress={({ key }) => {
                                    if (key === "Enter") handleEmailLogin();
                                }}
                                bind:value={password}
                            />
                            <button
                                type="button"
                                on:click={togglePasswordVisibility}
                                class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200"
                            >
                                {#if showPassword}
                                    <img
                                        src="/images/openEye.png"
                                        class="h-5 w-5"
                                        alt="Hide password"
                                    />
                                {:else}
                                    <img
                                        src="/images/closedEye.png"
                                        class="h-5 w-5"
                                        alt="Show password"
                                    />
                                {/if}
                            </button>
                        </div>
                    </div>

                    {#if isLoading}
                        <div class="flex justify-center">
                            <Loader />
                        </div>
                    {:else}
                        <button
                            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200"
                            on:click={handleEmailLogin}
                        >
                            Sign In
                        </button>
                    {/if}
                </div>
            {/if}
        </div>
    </div>
</div>
