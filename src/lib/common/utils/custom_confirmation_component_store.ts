import { writable } from 'svelte/store';

interface ConfirmDialogOptions {
  show?: boolean;
  heading?: string;
  body?: string;
  positiveButtonText?: string;
  negativeButtonText?: string;
  positiveAction?: () => void;
  negativeAction?: () => void;
}

const initialConfirmDialogState: ConfirmDialogOptions = {
    show: false,
    heading: "Confirmation",
    body: "Are you sure you want to proceed?",
    positiveButtonText: "Confirm",
    negativeButtonText: "Cancel",
    positiveAction: undefined,
  negativeAction: undefined,
};

export const confirmDialog = writable<ConfirmDialogOptions>(initialConfirmDialogState);

export function openConfirmDialog(options: ConfirmDialogOptions) {
    confirmDialog.update((state) => ({
        ...initialConfirmDialogState,
        ...options,
        show: true,
    }));
}

export function closeConfirmDialog() {
  confirmDialog.set(initialConfirmDialogState);
}