import { writable } from "svelte/store";
import type { IToast } from "$lib/toasts/models/IToast";
import type { IUser } from "$lib/users/models/User";
export const successToastStore = writable<IToast>({ status: false, message: "" });
export const errorToastStore = writable<IToast>({ status: false, message: "" });
export const userLoggedIn = writable<boolean>(false);
export const sidebarOpen = writable<boolean>(false);
export const newUsername = writable<string>("");
export const toggleMobileNav = writable<boolean>(false);
export const toggleModal = writable(false);
export const reload = writable(true);
export const isUserLoggedIn = writable<boolean>(false);
export const loggedInUser = writable<IUser | null>(null);
export const userPermissions = writable<string[]>([]);