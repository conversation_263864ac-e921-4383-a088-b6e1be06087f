/**
 * @deprecated
 * use isEmptyString with a negation
 * @param str
 */
function isNonEmptyString(str: any): str is string {
    return str !== null && str !== undefined && typeof str === "string" && str.trim().length > 0;
}

function isEmptyString(str: any): str is string {
    return str === null || str === undefined || typeof str === "string" && str.trim().length === 0;
}


function isNotNumber(value: any) {
    return typeof value !== "number";
}




export { isNonEmptyString, isEmptyString, isNotNumber };
