// src/lib/common/utils/activity_dialog_store.ts
import { writable } from "svelte/store";

// Define the shape of the activity dialog state
interface ActivityDialogState {
    show: boolean;
    message: string;
}

// Create a writable store with initial state
export const activityDialog = writable<ActivityDialogState>({
    show: false,
    message: "Please wait...",
});

/**
 * Function to show the activity dialog with a custom message.
 * @param message The message to display in the activity dialog.
 */
export function showActivity(message: string) {
    activityDialog.set({ show: true, message });
}

/**
 * Function to close the activity dialog.
 */
export function closeActivity() {
    activityDialog.set({ show: false, message: "" });
}
