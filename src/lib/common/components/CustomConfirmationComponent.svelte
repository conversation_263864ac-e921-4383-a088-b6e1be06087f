<script lang="ts">
    import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ing, <PERSON> } from "flowbite-svelte";
    import RedRoundCloseButton from "./RedRoundCloseButton.svelte";
    export let show: boolean = false;
    export let heading: string = "Confirmation";
    export let body: string = "Are you sure you want to proceed?";
    export let positiveButtonText: string = "Confirm";
    export let negativeButtonText: string = "Cancel";
    export let positiveAction: (() => void) | undefined = undefined;
    export let negativeAction: (() => void) | undefined = undefined;

    function handlePositive() {
        if (positiveAction) {
            positiveAction();
        }
        show = false;
    }

    function handleNegative() {
        if (negativeAction) {
            negativeAction();
        }
        show = false;
    }

  
</script>

<Modal
    bind:open={show}
    autoclose={false}
    size="sm"
    class="w-[30rem] z-[10000]"
    dismissable={false}
>
    <div class="flex justify-end">
        <!-- <RedRoundCloseButton onClick={handleNegative} /> -->
    </div>
    <div class="relative text-center !mt-0">
            <Heading tag="h3" class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">
                {heading}
            </Heading>
            <P class="my-6 text-base text-gray-500 dark:text-gray-400">
                {body}
            </P>

        <div class="flex justify-center space-x-4">
            <Button
                class="min-w-[120px] px-6 py-3 text-base rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 bg-red-600 text-white h-[40px]"
                on:click={handlePositive}
            >
                {positiveButtonText}
            </Button>
            <Button
                class="min-w-[120px] px-6 py-3 text-base rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2 bg-gray-200 text-gray-700 h-[40px]"
                on:click={handleNegative}
            >
                {negativeButtonText}
            </Button>
        </div>
    </div>
</Modal>

<style>
</style>
