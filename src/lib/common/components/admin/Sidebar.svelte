<script lang="ts">
    import {
        Drawer,
        Sidebar,
        SidebarGroup,
        SidebarItem,
        SidebarWrapper,
        Label,
    } from "flowbite-svelte";
    import { circInOut, quintOut } from "svelte/easing";
    import { onMount } from "svelte";
    import {
        isUserLoggedIn,
        loggedInUser,
        sidebarOpen,
        userPermissions,
    } from "$lib/common/utils/store";
    import { page } from "$app/stores";
    import { slide } from "svelte/transition";
    import { browser, dev } from "$app/environment";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";

    export let drawerHidden: boolean = false;
    export let width: number;

    let transitionParams = {
        x: -320,
        duration: 400,
        easing: circInOut,
    };
    let breakPoint: number = 1280;
    let backdrop: boolean = false;
    let activateClickOutside = true;

    // Collapsible sections state
    let expandedSections = {
        userManagement: true,
        masterData: true,
        purchasing: true,
        inventory: true,
        system: true,
        debug: true,
    };

    $: {
        if (browser) {
            drawerHidden = $sidebarOpen;
        }
    }

    onMount(() => {
        if (width >= breakPoint) {
            drawerHidden = false;
            activateClickOutside = false;
        } else {
            drawerHidden = true;
            activateClickOutside = true;
        }
    });

    const toggleSide = () => {
        if (width < breakPoint) {
            drawerHidden = !drawerHidden;
        }
    };

    const toggleSection = (section: keyof typeof expandedSections) => {
        expandedSections[section] = !expandedSections[section];
    };

    // Helper function to check if a section contains the active URL
    const sectionContainsActiveUrl = (section: string) => {
        switch (section) {
            case "userManagement":
                return (
                    activeUrl.includes("/admin/user-roles") || activeUrl.includes("/admin/users")
                );
            case "masterData":
                return (
                    activeUrl.includes("/admin/suppliers") ||
                    activeUrl.includes("/admin/storage-locations") ||
                    activeUrl.includes("/admin/factory-gates") ||
                    activeUrl.includes("/admin/item-categories") ||
                    activeUrl.includes("/admin/tax") ||
                    activeUrl.includes("/admin/item-units") ||
                    activeUrl.includes("/admin/raw-materials")
                );
            case "purchasing":
                return (
                    activeUrl.includes("/admin/purchase-invoices") ||
                    activeUrl.includes("/admin/purchase-orders") ||
                    activeUrl.includes("/admin/debit-notes")
                );
            case "inventory":
                return (
                    activeUrl.includes("/admin/raw-materials-stock") ||
                    activeUrl.includes("/admin/opening-stock") ||
                    activeUrl.includes("/admin/assign-storage-to-raw-material") ||
                    activeUrl.includes("/admin/stock-adjustments")
                );
            case "system":
                return activeUrl.includes("/admin/logs");

            case "debug":
                return (
                    activeUrl.includes("/admin/debug/problematic-stocks") ||
                    activeUrl.includes("/admin/debug/morgan-logs")
                );
            default:
                return false;
        }
    };

    let navigationSpanNormalClass = "ml-3 font-primary";
    let navigationSpanActiveClass = "!text-black ml-3 font-primary";

    let activeUrl = "";

    $: {
        activeUrl = $page.url.pathname;
    }
</script>

<Drawer
    transitionType="fly"
    {backdrop}
    {transitionParams}
    bind:hidden={drawerHidden}
    bind:activateClickOutside
    width="w-64"
    class="hideScroll top-[25px] z-[1] overflow-y-scroll overflow-x-hidden bg-primary-500 pb-32 pt-0 duration-500 ease-in-out"
    id="sidebar"
    style="scrollbar-width: none;"
>
    <Sidebar asideClass="min-w-[120px]" {activeUrl}>
        <SidebarWrapper divClass="overflow-y-auto overflow-x-hidden py-4 rounded mt-7">
            <SidebarGroup ulClass="space-y-2">
                <Label class="mb-4 mt-5 font-primary text-xl tracking-widest text-white">
                    Dashboard
                </Label>
                <div
                    class="mb-6 h-px bg-gradient-to-r  from-transparent via-white/30 to-transparent"
                ></div>

                <!-- User Management Section -->
                <div class="mb-4">
                    <button
                        on:click={() => toggleSection("userManagement")}
                        class="flex w-full items-center justify-between rounded-lg px-3 py-2 text-white transition-all duration-200 {!expandedSections.userManagement &&
                        sectionContainsActiveUrl('userManagement')
                            ? 'bg-white/20 border border-white/30 shadow-lg hover:bg-white/30 hover:shadow-xl'
                            : 'hover:bg-white/10'}"
                    >
                        <span
                            class="font-primary text-sm font-medium tracking-wide {!expandedSections.userManagement &&
                            sectionContainsActiveUrl('userManagement')
                                ? 'text-yellow-200'
                                : ''}"
                        >
                            👥 User Management
                        </span>
                        <svg
                            class="h-4 w-4 transform transition-transform duration-200 {expandedSections.userManagement
                                ? 'rotate-180'
                                : ''} {!expandedSections.userManagement &&
                            sectionContainsActiveUrl('userManagement')
                                ? 'text-yellow-200'
                                : ''}"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M19 9l-7 7-7-7"
                            />
                        </svg>
                    </button>

                    {#if expandedSections.userManagement}
                        <div
                            class="mt-2 space-y-1"
                            transition:slide={{ duration: 300, easing: quintOut }}
                        >
                            <button
                                class="smooth group relative w-full group-hover:text-black {activeUrl.includes(
                                    '/admin/user-roles'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 50,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/user-roles")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/user-roles"
                                    label="User Roles"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/suppliers.png"
                                            alt="User Roles"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/user-roles/add"
                                    class="absolute right-2 top-2 hidden group-hover:block hover:text-white"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/users'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 100,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/users")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/users"
                                    label="Users"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/suppliers.png"
                                            alt="Users"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/users/add"
                                    class="absolute right-2 top-2 hidden group-hover:block hover:text-white"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>
                        </div>
                    {/if}
                </div>

                <div
                    class="mb-2 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"
                ></div>

                <!-- Master Data Section -->
                <div class="mb-4">
                    <button
                        on:click={() => toggleSection("masterData")}
                        class="flex w-full items-center justify-between rounded-lg px-3 py-2 text-white transition-all duration-200 {!expandedSections.masterData &&
                        sectionContainsActiveUrl('masterData')
                            ? 'bg-white/20 border border-white/30 shadow-lg hover:bg-white/30 hover:shadow-xl'
                            : 'hover:bg-white/10'}"
                    >
                        <span
                            class="font-primary text-sm font-medium tracking-wide {!expandedSections.masterData &&
                            sectionContainsActiveUrl('masterData')
                                ? 'text-yellow-200'
                                : ''}"
                        >
                            🏭 Master Data
                        </span>
                        <svg
                            class="h-4 w-4 transform transition-transform duration-200 {expandedSections.masterData
                                ? 'rotate-180'
                                : ''} {!expandedSections.masterData &&
                            sectionContainsActiveUrl('masterData')
                                ? 'text-yellow-200'
                                : ''}"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M19 9l-7 7-7-7"
                            />
                        </svg>
                    </button>

                    {#if expandedSections.masterData}
                        <div
                            class="ml-2 mt-2  space-y-1"
                            transition:slide={{ duration: 300, easing: quintOut }}
                        >
                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/suppliers'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 50,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/suppliers")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/suppliers"
                                    label="Suppliers"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/suppliers.png"
                                            alt="Suppliers"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/suppliers/add"
                                    class="absolute right-2 top-2 hidden group-hover:block hover:text-white"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/storage-locations'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 100,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/storage-locations")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/storage-locations"
                                    label="Storage Locations"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/storage-locations.png"
                                            alt="Storage Locations"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/storage-locations/add"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/factory-gates'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 150,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/factory-gates")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/factory-gates"
                                    label="Factory Gates"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/factory-gates.png"
                                            alt="Factory Gates"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/factory-gates/add"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/item-categories'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 200,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/item-categories")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/item-categories"
                                    label="Item Categories"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/item-categories.png"
                                            alt="Item Categories"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/item-categories/add"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/tax'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 200,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/tax")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/tax"
                                    label="Tax Rate"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/tax.png"
                                            alt="Tax"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/tax/add"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/item-units'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 250,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/item-units")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/item-units"
                                    label="Item Units"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/item-units.png"
                                            alt="Item Units"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/item-units/add"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl ===
                                    '/admin/raw-materials' ||
                                activeUrl === '/admin/raw-materials/add'
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 300,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl === "/admin/raw-materials" ||
                                    activeUrl === "/admin/raw-materials/add"
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/raw-materials"
                                    label="Raw Material"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/raw-material.png"
                                            alt="Raw Material"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/raw-materials/add"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>
                            <button
                            class="smooth group relative w-full {activeUrl ===
                                    '/admin/final-goods' ||
                                activeUrl === '/admin/final-goods/add'
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 300,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}>

                                <SidebarItem
                                spanClass={activeUrl === "/admin/final-goods" ||
                                activeUrl === "/admin/final-goods/add"
                                    ? navigationSpanActiveClass
                                    : navigationSpanNormalClass}
                                href="/admin/final-goods"
                                label="Final Goods"
                                on:click={toggleSide}
                            >
                                <svelte:fragment slot="icon">
                                    <img
                                        src="/images/sidebar-icons/raw-material.png"
                                        alt="Final Goods"
                                        class="h-5"
                                    />
                                </svelte:fragment>
                            </SidebarItem>
                            <a
                                href="/admin/final-goods/add"
                                class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                <img src="/images/sidebar/add.svg" alt="add" />
                            </a>
                        </button>
                        <button
                            class="smooth group relative w-full {activeUrl ===
                                    '/admin/bill-of-materials' ||
                                activeUrl === '/admin/bill-of-materials/add' ||
                                activeUrl === '/admin/bill-of-materials/edit'
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 400,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}>

                                <SidebarItem
                                spanClass={activeUrl === "/admin/bill-of-materials" ||
                                activeUrl === "/admin/bill-of-materials/add" ||
                                activeUrl === "/admin/bill-of-materials/edit"
                                    ? navigationSpanActiveClass
                                    : navigationSpanNormalClass}
                                href="/admin/bill-of-materials"
                                label="Bill of Materials"
                                on:click={toggleSide}
                            >
                                <svelte:fragment slot="icon">
                                    <img
                                        src="/images/sidebar-icons/raw-material.png"
                                        alt="Bill of Materials"
                                        class="h-5"
                                    />
                                </svelte:fragment>
                            </SidebarItem>
                            <a
                                href="/admin/bill-of-materials/add"
                                class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                <img src="/images/sidebar/add.svg" alt="add" />
                            </a>
                        </button>
                        </div>
                    {/if}
                </div>

                <div
                    class="mb-2 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"
                ></div>

                <!-- Purchasing Section -->
                <div class="mb-4">
                    <button
                        on:click={() => toggleSection("purchasing")}
                        class="flex w-full items-center justify-between rounded-lg px-3 py-2 text-white transition-all duration-200 {!expandedSections.purchasing &&
                        sectionContainsActiveUrl('purchasing')
                            ? 'bg-white/20 border border-white/30 shadow-lg hover:bg-white/30 hover:shadow-xl'
                            : 'hover:bg-white/10'}"
                    >
                        <span
                            class="font-primary text-sm font-medium tracking-wide {!expandedSections.purchasing &&
                            sectionContainsActiveUrl('purchasing')
                                ? 'text-yellow-200'
                                : ''}"
                        >
                            🛒 Purchasing
                        </span>
                        <svg
                            class="h-4 w-4 transform transition-transform duration-200 {expandedSections.purchasing
                                ? 'rotate-180'
                                : ''} {!expandedSections.purchasing &&
                            sectionContainsActiveUrl('purchasing')
                                ? 'text-yellow-200'
                                : ''}"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M19 9l-7 7-7-7"
                            />
                        </svg>
                    </button>

                    {#if expandedSections.purchasing}
                        <div
                            class="ml-2 mt-2 space-y-1"
                            transition:slide={{ duration: 300, easing: quintOut }}
                        >
                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/purchase-invoices'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 50,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl === "/admin/purchase-invoices" ||
                                    activeUrl.includes("/admin/purchase-invoices")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/purchase-invoices"
                                    label="Purchase Invoices"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/purchase-stock.png"
                                            alt="Purchase Invoices"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/purchase-invoices/add"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/purchase-orders'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 100,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl === "/admin/purchase-orders" ||
                                    activeUrl.includes("/admin/purchase-orders")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/purchase-orders"
                                    label="Demand Slips"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/purchase-stock.png"
                                            alt="Purchase Orders"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/purchase-orders/add"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/debit-notes'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 150,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/debit-notes")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/debit-notes"
                                    label="Debit Notes"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/purchase-stock.png"
                                            alt="Debit Notes"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                            </button>
                        </div>
                    {/if}
                </div>

                <div
                    class="mb-2 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"
                ></div>

                <!-- Inventory Management Section -->
                <div class="mb-4">
                    <button
                        on:click={() => toggleSection("inventory")}
                        class="flex w-full items-center justify-between rounded-lg px-3 py-2 text-white transition-all duration-200 {!expandedSections.inventory &&
                        sectionContainsActiveUrl('inventory')
                            ? 'bg-white/20 border border-white/30 shadow-lg hover:bg-white/30 hover:shadow-xl'
                            : 'hover:bg-white/10'}"
                    >
                        <span
                            class="font-primary text-sm font-medium tracking-wide {!expandedSections.inventory &&
                            sectionContainsActiveUrl('inventory')
                                ? 'text-yellow-200'
                                : ''}"
                        >
                            📦 Inventory
                        </span>
                        <svg
                            class="h-4 w-4 transform transition-transform duration-200 {expandedSections.inventory
                                ? 'rotate-180'
                                : ''} {!expandedSections.inventory &&
                            sectionContainsActiveUrl('inventory')
                                ? 'text-yellow-200'
                                : ''}"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M19 9l-7 7-7-7"
                            />
                        </svg>
                    </button>

                    {#if expandedSections.inventory}
                        <div
                            class="ml-2 mt-2 space-y-1"
                            transition:slide={{ duration: 300, easing: quintOut }}
                        >
                            <button
                                class="smooth group relative w-full {activeUrl ===
                                '/admin/raw-materials-stock'
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 50,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl === "/admin/raw-materials-stock"
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/raw-materials-stock"
                                    label="Current Stock"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar/crm management.png"
                                            alt="Current Stock"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                            </button>

                            <!-- <button
                                class="smooth group relative w-full {activeUrl ===
                                '/admin/opening-stock'
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 100,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl === "/admin/opening-stock"
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/opening-stock"
                                    label="Opening Stock"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar/crm management.png"
                                            alt="Opening Stock"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                            </button> -->

                            <button
                                class="smooth group relative w-full {activeUrl ===
                                '/admin/raw-materials-stockin'
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 150,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl === "/admin/raw-materials-stock/in"
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/raw-materials-stock/in"
                                    label="Stock In Details"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar/crm management.png"
                                            alt="Stock In Details"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/raw-materials-stock/issuance'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 200,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes(
                                        "/admin/raw-materials-stock/issuance"
                                    )
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/raw-materials-stock/issuance"
                                    label="Stock Issuance"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/purchase-stock.png"
                                            alt="Stock Issuance"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/raw-materials-stock/issuance/issue"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/assign-storage-to-raw-material'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 250,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes(
                                        "/admin/assign-storage-to-raw-material"
                                    )
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/assign-storage-to-raw-material"
                                    label="Assign Storage"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/purchase-stock.png"
                                            alt="Assign Storage"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                            </button>

                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/stock-adjustments'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 300,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/stock-adjustments")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/stock-adjustments"
                                    label="Stock Adjustments"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/item-categories.png"
                                            alt="Stock Adjustments"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                                <a
                                    href="/admin/stock-adjustments/add"
                                    class="absolute right-2 top-2 hidden group-hover:block"
                                >
                                    <img src="/images/sidebar/add.svg" alt="add" />
                                </a>
                            </button>
                        </div>
                    {/if}
                </div>

                <div
                    class="mb-2 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"
                ></div>

                <!-- System Section -->
                <div class="mb-4">
                    <button
                        on:click={() => toggleSection("system")}
                        class="flex w-full items-center justify-between rounded-lg px-3 py-2 text-white transition-all duration-200 {!expandedSections.system &&
                        sectionContainsActiveUrl('system')
                            ? 'bg-white/20 border border-white/30 shadow-lg hover:bg-white/30 hover:shadow-xl'
                            : 'hover:bg-white/10'}"
                    >
                        <span
                            class="font-primary text-sm font-medium tracking-wide {!expandedSections.system &&
                            sectionContainsActiveUrl('system')
                                ? 'text-yellow-200'
                                : ''}"
                        >
                            ⚙️ System
                        </span>
                        <svg
                            class="h-4 w-4 transform transition-transform duration-200 {expandedSections.system
                                ? 'rotate-180'
                                : ''} {!expandedSections.system &&
                            sectionContainsActiveUrl('system')
                                ? 'text-yellow-200'
                                : ''}"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M19 9l-7 7-7-7"
                            />
                        </svg>
                    </button>

                    {#if expandedSections.system}
                        <div
                            class="ml-2 mt-2 space-y-1"
                            transition:slide={{ duration: 300, easing: quintOut }}
                        >
                            <button
                                class="smooth group relative w-full {activeUrl.includes(
                                    '/admin/logs'
                                )
                                    ? '!bg-gray-200 !rounded-md'
                                    : ''}"
                                transition:slide={{
                                    delay: 50,
                                    duration: 300,
                                    easing: quintOut,
                                    axis: "y",
                                }}
                            >
                                <SidebarItem
                                    spanClass={activeUrl.includes("/admin/logs")
                                        ? navigationSpanActiveClass
                                        : navigationSpanNormalClass}
                                    href="/admin/logs"
                                    label="Logs"
                                    on:click={toggleSide}
                                >
                                    <svelte:fragment slot="icon">
                                        <img
                                            src="/images/sidebar-icons/purchase-stock.png"
                                            alt="Logs"
                                            class="h-5"
                                        />
                                    </svelte:fragment>
                                </SidebarItem>
                            </button>
                        </div>
                    {/if}

                    <div
                        class="mb-2 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"
                    ></div>
                </div>

                {#if dev || $loggedInUser?.email === "<EMAIL>"}
                    <!-- Debug Section -->
                    <div class="mb-4">
                        <button
                            on:click={() => toggleSection("debug")}
                            class="flex w-full items-center justify-between rounded-lg px-3 py-2 text-white transition-all duration-200 {!expandedSections.debug &&
                            sectionContainsActiveUrl('debug')
                                ? 'bg-white/20 border border-white/30 shadow-lg hover:bg-white/30 hover:shadow-xl'
                                : 'hover:bg-white/10'}"
                        >
                            <span
                                class="font-primary text-sm font-medium tracking-wide {!expandedSections.debug &&
                                sectionContainsActiveUrl('debug')
                                    ? 'text-yellow-200'
                                    : ''}"
                            >
                                ⚙️ Debug
                            </span>
                            <svg
                                class="h-4 w-4 transform transition-transform duration-200 {expandedSections.debug
                                    ? 'rotate-180'
                                    : ''} {!expandedSections.debug &&
                                sectionContainsActiveUrl('debug')
                                    ? 'text-yellow-200'
                                    : ''}"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M19 9l-7 7-7-7"
                                />
                            </svg>
                        </button>

                        {#if expandedSections.debug}
                            <div
                                class="ml-2 mt-2 space-y-1"
                                transition:slide={{ duration: 300, easing: quintOut }}
                            >
                                <button
                                    class="smooth group relative w-full {activeUrl.includes(
                                        'admin/debug/problematic-stocks'
                                    )
                                        ? '!bg-gray-200 !rounded-md'
                                        : ''}"
                                    transition:slide={{
                                        delay: 50,
                                        duration: 300,
                                        easing: quintOut,
                                        axis: "y",
                                    }}
                                >
                                    <SidebarItem
                                        spanClass={activeUrl.includes(
                                            "admin/debug/problematic-stocks"
                                        )
                                            ? navigationSpanActiveClass
                                            : navigationSpanNormalClass}
                                        href="/admin/debug/problematic-stocks"
                                        label="Current Stock"
                                        on:click={toggleSide}
                                    >
                                        <svelte:fragment slot="icon">
                                            <img
                                                src="/images/sidebar-icons/stock-in.png"
                                                alt="debug-current-stock"
                                                class="h-5"
                                            />
                                        </svelte:fragment>
                                    </SidebarItem>
                                </button>
                            </div>

                            <div
                                class="ml-2 mt-2 space-y-1"
                                transition:slide={{ duration: 300, easing: quintOut }}
                            >
                                <button
                                    class="smooth group relative w-full {activeUrl.includes(
                                        'admin/debug/morgan-logs'
                                    )
                                        ? '!bg-gray-200 !rounded-md'
                                        : ''}"
                                    transition:slide={{
                                        delay: 50,
                                        duration: 300,
                                        easing: quintOut,
                                        axis: "y",
                                    }}
                                >
                                    <SidebarItem
                                        spanClass={activeUrl.includes("admin/debug/morgan-logs")
                                            ? navigationSpanActiveClass
                                            : navigationSpanNormalClass}
                                        href="/admin/debug/morgan-logs"
                                        label="API Logs"
                                        on:click={toggleSide}
                                    >
                                        <svelte:fragment slot="icon">
                                            <img
                                                src="/images/sidebar-icons/stock-in.png"
                                                alt="debug-current-stock"
                                                class="h-5"
                                            />
                                        </svelte:fragment>
                                    </SidebarItem>

                                    <!-- <SidebarItem
                                        spanClass={activeUrl.includes("/admin/excel-upload/upload")
                                            ? navigationSpanActiveClass
                                            : navigationSpanNormalClass}
                                        href={"/admin/excel-upload/upload"}
                                        label="Excel Upload"
                                        on:click={toggleSide}
                                    >
                                        <svelte:fragment slot="icon">
                                            <img
                                                src="/images/sidebar-icons/purchase-stock.png"
                                                alt="Excel Upload"
                                                class="h-5"
                                            />
                                        </svelte:fragment>
                                    </SidebarItem> -->
                                </button>
                            </div>
                        {/if}
                        <div
                            class="mb-2 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"
                        ></div>
                    </div>
                {/if}
            </SidebarGroup>
        </SidebarWrapper>
    </Sidebar>
</Drawer>

<style>
    button.smooth:has(a:hover) {
        background-color: gray;
        border-radius: 10px;
    }

    :global(button.smooth li:hover span) {
        color: black;
    }

    :global(button.smooth a:hover ~ li span) {
        color: black;
    }

    :global(button.smooth li span) {
        color: white;
    }

    /* Enhanced hover effects */
    .smooth {
        transition: background-color 0.2s ease-in-out;
    }

    .smooth:hover {
        transform: translateX(.5px);
        background-color: rgba(255, 255, 255, 0.05);
    }

    /* Section header hover effects */
    button:hover svg {
        transform: scale(1.1);
        transition: transform 0.2s ease-in-out;
    }

    /* Gradient separators */
    .h-px {
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    }

    /* Special styling for collapsed sections with active content */
    .collapsed-with-active {
        position: relative;
        overflow: hidden;
    }

    .collapsed-with-active::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border-radius: 8px;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .collapsed-with-active:hover::before {
        opacity: 1;
    }

    /* Subtle pulse animation for collapsed sections with active content */
    @keyframes subtle-pulse {
        0%,
        100% {
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1);
        }
        50% {
            box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.05);
        }
    }

    /* Enhanced glow effect for collapsed sections with active content */
    button:has(.text-yellow-200) {
        position: relative;
        animation: subtle-pulse 4s infinite;
    }

    /* Additional visual cue - left border indicator */
    button:has(.text-yellow-200)::after {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 60%;
        background: linear-gradient(to bottom, #fbbf24, #f59e0b);
        border-radius: 0 2px 2px 0;
        opacity: 0.8;
    }
</style>
