<script lang="ts">
    import {But<PERSON>, Modal} from "flowbite-svelte";
  import CustomButton from "./CustomButton.svelte";

    export let modelOpen: boolean = false;
    export let title: string = "";
    export let handleCancel: () => void = () => {
        modelOpen = false
    };
    export let handleDelete:()=>Promise<void> | void
</script>

<Modal title="" bind:open={modelOpen} autoclose size="sm" class="w-[30%]">
    <svg class="text-gray-400 dark:text-gray-500 w-11 h-11 mb-3.5 mx-auto" aria-hidden="true" fill="currentColor"
         viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd"
              d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
              clip-rule="evenodd"/>
    </svg>
    <p class="mb-4 text-gray-500 dark:text-gray-300 text-center">Are you sure you want to delete this {title}?</p>
    <div class="flex justify-center items-center space-x-4">
        <Button color="light" class="px-6 py-2" on:click={handleCancel}>No, cancel</Button>
        <CustomButton onClick={handleDelete} title="Yes, I'm sure" />
    </div>
</Modal>