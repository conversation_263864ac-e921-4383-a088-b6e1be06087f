<script lang="ts">
    import { loggedInUser } from "$lib/common/utils/store";
    import { NavBrand, NavHamburger } from "flowbite-svelte";
    import NavContainer from "flowbite-svelte/NavContainer.svelte";
    import CustomButton from "./CustomButton.svelte";
    import { signOut } from "firebase/auth";
    import { usiFirebaseAuth } from "../../../../firebaseInit";
    import {
        closeConfirmDialog,
        openConfirmDialog,
    } from "$lib/common/utils/custom_confirmation_component_store";
    import { closeActivity, showActivity } from "$lib/common/utils/activity_store";

    export let toggleDrawer: () => void = () => {};
</script>

<header class="fixed left-0 right-0 top-0 z-[10] !mx-0">
    <NavContainer
        class="!mx-0 h-[5rem] !max-w-[100vw] !items-center !bg-primary"
    >
        <NavBrand class="!mx-0 w-[80%]">
            <button on:click={toggleDrawer} class="mx-[2%] text-white">
                <NavHamburger class="!block p-1 hover:bg-gray-900" />
            </button>
            <div class=" flex w-full items-center justify-center">
                <button
                    on:click={async () => {}}
                    class=" flex w-[65%] items-center justify-end whitespace-nowrap pl-[10%] text-xl font-semibold"
                >
                    <img
                        src="/images/logo-white.png"
                        class="mr-1 h-[3.5rem] object-contain"
                        alt="Logo"
                    />
                </button>
                <div class="flex h-full w-[35%] items-center justify-end"></div>
            </div>
        </NavBrand>
        <div class="flex w-[20%] items-center justify-end pr-[3%]">
            <span class="m-5 block truncate text-[16px] text-white">
                Hi, {$loggedInUser?.firstName}
            </span>

            <CustomButton
                title="Logout"
                onClick={async () => {
                    openConfirmDialog({
                        heading: "Logout",
                        body: "Are you sure you want to logout?",
                        positiveButtonText: "Yes",
                        negativeButtonText: "No",
                        positiveAction: async () => {
                            closeConfirmDialog();
                            showActivity("Logging out...");
                            await signOut(usiFirebaseAuth);
                            loggedInUser.update(() => null);
                            closeActivity();
                        },
                    });
                }}
            />
        </div>
    </NavContainer>
</header>
