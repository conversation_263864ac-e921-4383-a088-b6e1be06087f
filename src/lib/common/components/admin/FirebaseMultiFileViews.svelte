<script lang="ts">
    import FirebaseFileButton from "./FirebaseFileButton.svelte";

   
    export let files: string[] = [];

    export let handleDelete: (index: number) => void = () => {};
    export let delRequired = true;
</script>

<div class="w-100 flex overflow-x-auto {files.length > 0 ? 'h-40' : 'h-0'}">
    {#each files as file, index (index)}
        <FirebaseFileButton {file} {index} {handleDelete} {delRequired} />
    {/each}
</div>
