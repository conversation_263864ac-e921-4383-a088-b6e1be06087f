<script lang="ts">
    import { Spinner } from "flowbite-svelte";

    export let isLoading = false;
    export let onClick: (e: MouseEvent) => void;
    // export let uploadFn: (file: File[]) => Promise<string[] | null>;
    // export let onUploaded: (urls: string[]) => void;
    // export let onError: (error: Error) => void = () => {};
    export let title: string = "Save";
    export let cssClass: string = "bg-red-600";

    let files: File[] = [];
    // async function uploadFiles() {
    //     try {
    //         const urls = await uploadFn(files);
    //         if(urls){
    //             onUploaded(urls);
    //         }
    //     } catch (err) {
    //         onError(err as Error);
    //     }
    // }
</script>

{#if isLoading}
    <button
        
        type="button"
        class="  bg-slate-950 dark:bg-primary-900 flex transform items-center justify-center gap-2 rounded-lg px-6 py-2 font-primary text-sm font-medium capitalize tracking-wide text-white transition-colors duration-300 hover:bg-gray-700 focus:outline-none focus:ring focus:ring-gray-300 focus:ring-opacity-50 {cssClass}"
    >
        <Spinner size="5" /> Loading
    </button>
{:else}
    <button
        on:click={
            onClick
            // uploadFiles();
        }
        type="button"
        class="transform rounded-lg px-6 py-2 font-primary text-sm font-medium capitalize tracking-wide text-white transition-colors duration-300 bg-slate-950 dark:bg-primary-900 hover:bg-gray-700 focus:outline-none cursor-pointer focus:ring focus:ring-gray-300 focus:ring-opacity-50 {cssClass} "
    >
        {title}
    </button>
{/if}
