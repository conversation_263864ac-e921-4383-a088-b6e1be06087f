<script lang="ts">
    import { isEmptyString } from "$lib/common/utils/validation";
    export let items: any[] = [];
    export let value: string | number = "";
    export let stylingClass: string = "";
    export let showId = "";
    export let showVal = "";
    export let onChange: (value: string) => void = () => {};  
    export let onInput: (value: string) => void = () => {};    
</script>

<div class="">
    <select
        required
        bind:value
        class="{stylingClass} focus rounded-md text-sm text-gray-900 w-full focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:dark:bg-[#646464] dark:text-white dark:placeholder-gray-400"
        on:change={(e) => {
            onChange(e.currentTarget.value);
        }}
        on:input={(e)=>{
            onInput(e.currentTarget.value);
        }}
    >
        {#each items as item}
            <option class="border-red-500" value={isEmptyString(showId) ? item : item[`${showId}`]}>
                {isEmptyString(showVal) ? item.replace(/_/g, " ") : item[`${showVal}`]}
            </option>
        {/each}
    </select>
</div>
