<script lang="ts">
    import { capitalizeFirstWord } from "$lib/common/utils/common-utils";
    import { isEmptyString } from "$lib/common/utils/validation";
    import { Input, Label } from "flowbite-svelte";
    import { onMount } from "svelte";

    export let isLabel: boolean = true;
    export let label: string = "";
    export let searchTerm: string = "";
    export let searchInput: (event: string) => void = async (e: string) => {};

    export let selected: string | null = null;
    export let filteredData: Array<any> = [];
    export let level = "";

    export let selectedObj: any = null;

    export let selectedFunc: (selected: any) => void = () => {};
    export let loading = false;
    export let errorMap: Map<string, string> | null = null;
    export let fieldName: string = "";
    export let placeholder: string | null = null;
    export let onCrossClick: () => void = () => {};

    export let disabled: boolean = false;
    export let keepUserInput: boolean = false;

    let suggestionsContainer: HTMLElement;

    const handleClickOutside = (event: MouseEvent) => {
        if (suggestionsContainer &&
            !suggestionsContainer.contains(event.target as Node)) {
            filteredData = [];
            if(!keepUserInput){
                searchTerm = selected ? selected : "";
            }
        }
    };

    onMount(() => {
        document.addEventListener('click', handleClickOutside);
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    });
</script>

<div>
    {#if isLabel}
        <Label for={label} class=" mb-2 font-sans text-base capitalize tracking-[0px]">
            {label}
            {#if errorMap && errorMap.has(fieldName)}
                <span class="text-red-600">*</span>
            {/if}
        </Label>
    {/if}
    <div  class=" relative text-sm">
        <div class="flex w-full items-center gap-2 space-x-2">
            <div class="relative w-full">
                <Input
                disabled={disabled}
                    readonly={selected !== null || loading}
                    name={label}
                    autocomplete="off"
                    type="text"
                    placeholder={!selected || selected.length === 0
                        ? "Search " + (placeholder ?? label) + " ..."
                        : ""}
                    value={searchTerm}
                    class="w-full p-2 font-primary dark:bg-primary-700 {errorMap &&
                    errorMap.has(fieldName)
                        ? 'border-red-500'
                        : ''}"
                    on:input={(e) => {
                        // @ts-ignore
                        searchTerm = e.currentTarget.value;
                        
                        // @ts-ignore
                        searchInput(e.currentTarget?.value ?? "");
                    }}
                    on:change={(e) => {
                        // Remove or modify this event handler if you don't want to reset to selected value
                        // @ts-ignore
                        // e.currentTarget.value = selected;
                    }}
                />

                {#if selectedObj}
                    <div
                        id="chip"
                        class="absolute flex top-[50%] left-[10px]
                          translate-y-[-50%] rounded-md justify-center
                          align-middle bg-slate-800 py-0.5 pl-1 border border-transparent text-sm text-white
                           transition-all shadow-sm"
                    >
                        {capitalizeFirstWord(selected ?? "")}
                        {#if !disabled}
                            <button
                                aria-labelledby="chip"
                                class="flex items-center justify-center transition-all rounded-md text-white p-1"
                                type="button"

                                on:click={() => {
                                    selected = null; // Ensure this resets the field
                                    selectedObj = null;
                                    if (selectedFunc && typeof selectedFunc === "function"){
                                        selectedFunc(null);
                                        onCrossClick();
                                    }
                                }}
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 16 16"
                                    fill="currentColor"
                                    class="w-4 h-4"
                                >
                                    <path
                                        d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z"
                                    />
                                </svg>
                            </button>
                        {/if}
                    </div>
                {/if}
            </div>

            {#if loading}
                <div class="absolute right-2" role="status">
                    <svg
                        aria-hidden="true"
                        class="h-5 w-5 animate-spin fill-yellow-500 text-gray-200 dark:text-gray-600"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"
                        />
                    </svg>
                    <span class="sr-only">Loading...</span>
                </div>
            {/if}
        </div>

        {#if filteredData && filteredData.length > 0 && searchTerm.length > 0 && !loading}
            <ul
                bind:this={suggestionsContainer}
                class="absolute z-50 mr-3 mt-1 max-h-52 w-full overflow-y-auto rounded border border-gray-300 bg-white font-primary shadow-lg"
            >
                {#each filteredData as data}
                    <!-- svelte-ignore a11y-click-events-have-key-events -->
                    <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
                    <li
                        on:click={() => {
                            if (selectedFunc && typeof selectedFunc === "function") {
                                selectedFunc(data);
                            }
                        }}
                        class="cursor-pointer p-2 text-sm transition-colors duration-200 hover:bg-gray-100"
                    >
                        {isEmptyString(level) ? data : data[`${level}`]}
                    </li>
                {/each}
            </ul>
        {:else if !keepUserInput && searchTerm.length > 0 && !loading}
            <ul
            bind:this={suggestionsContainer}
                class="absolute z-50 mt-1 max-h-52 w-full overflow-y-auto rounded border border-gray-300 bg-white font-primary shadow-lg"
            >
                <li class="cursor-pointer p-2 font-primary hover:bg-gray-100">
                    No {label} Found
                </li>
            </ul>
        {/if}
    </div>
</div>
