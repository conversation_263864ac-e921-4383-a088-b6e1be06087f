<script lang="ts">
    import { onMount } from "svelte";
    import imageCompression from "browser-image-compression";

    export let multiple = false;
    export let accept = "image/*";
    export let maxSizeMB = 5;
    // svelte-ignore export_let_unused
        export let lazy = false;
    export let initialUrls: string[] = [];
    export let files: File[] = [];
    export let deletedImages: string[] = [];
    export let showDeleteButtons = true;
    // Compression config from parent
    export let enableCompression = true;
    export let compressionMaxSizeMB = 1;
    export let compressionMaxWidthOrHeight = 480;

    let previews: string[] = [];
    let selectedImageIndex: number | null = null;
    let showModal = false;

    let compressionStatus: string[] = [];
    let compressionComparison: string[] = [];
    let isCompressing = false;

    onMount(() => {
        previews = [...initialUrls];
    });

    function handleKeydown(e: KeyboardEvent) {
        if (e.key === "Escape") {
            closeImageModal();
        }
    }

    async function compressImage(
        file: File
    ): Promise<{ file: File; beforeSize: number; afterSize: number }> {
        if (!enableCompression) {
            return {
                file,
                beforeSize: file.size,
                afterSize: file.size,
            };
        }

        const options = {
            maxSizeMB: compressionMaxSizeMB,
            maxWidthOrHeight: compressionMaxWidthOrHeight,
            useWebWorker: true,
        };

        const beforeSize = file.size;
        try {
            const compressed = await imageCompression(file, options);
            const afterSize = compressed.size;
            return { file: compressed, beforeSize, afterSize };
        } catch (e) {
            return { file, beforeSize, afterSize: beforeSize };
        }
    }

    async function handleFileChange(e: Event) {
        const input = e.target as HTMLInputElement;
        const selected = input.files ? Array.from(input.files) : [];
        const valid = selected.filter((f) => f.size / 1024 / 1024 <= maxSizeMB);

        if (valid.length > 0) {
            isCompressing = true;
        }

        const compressedResults = await Promise.all(valid.map((file) => compressImage(file)));

        compressedResults.forEach(({ file, beforeSize, afterSize }) => {
            const index = files.length;
            files.push(file);
            compressionStatus[index] = "Done";
            compressionComparison[index] =
                `${(beforeSize / 1024).toFixed(1)}KB → ${(afterSize / 1024).toFixed(1)}KB`;

            const reader = new FileReader();
            reader.onload = () => {
                previews = [...previews, reader.result as string];
            };
            reader.readAsDataURL(file);
        });

        isCompressing = false;
        input.value = "";
    }

    function openImageModal(index: number) {
        selectedImageIndex = index;
        showModal = true;
    }

    function closeImageModal() {
        showModal = false;
        selectedImageIndex = null;
    }

    function deleteImage(index: number) {
        const deletedUrl = previews[index];

        if (initialUrls.includes(deletedUrl)) {
            deletedImages.push(deletedUrl);
            deletedImages = [...deletedImages];

            // Remove from initialUrls as well
            const initialIndex = initialUrls.indexOf(deletedUrl);
            if (initialIndex !== -1) {
                initialUrls.splice(initialIndex, 1);
                initialUrls = [...initialUrls];
            }
        }

        previews.splice(index, 1);
        previews = [...previews];

        files.splice(index, 1);
        files = [...files];

        compressionStatus.splice(index, 1);
        compressionStatus = [...compressionStatus];

        compressionComparison.splice(index, 1);
        compressionComparison = [...compressionComparison];

        // Reset compression status if no files are left
        if (files.length === 0) {
            isCompressing = false;
        }
    }
</script>

<svelte:window on:keydown={handleKeydown} />

<div class="relative">
    <input
        type="file"
        {multiple}
        {accept}
        disabled={!showDeleteButtons}
        on:change={handleFileChange}
        class="absolute inset-0 w-full h-full opacity-0 cursor-pointer{showDeleteButtons == false ? "cursor-not-allowed":""}"
        id="file-input"
    />
    <label
        for="file-input"
        class="inline-flex items-center justify-center w-full h-[45px] px-4 py-2 bg-[#F9FAFB] border border-gray-300 rounded-xl text-gray-700 font-medium hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer transition-all duration-200"
    >
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
        </svg>
        {previews.length === 0 ? "Choose Images" : "Add More Images"}
    </label>
</div>

<div class="mt-3">
    <div
        class="flex flex-wrap gap-3 w-full h-[30vh] overflow-y-auto p-2 px-4 rounded-lg bg-gray-200"
    >
        {#if previews.length > 0}
            {#each previews as url, index}
                <div class="relative group w-fit">
                    {#if showDeleteButtons}
                        <button
                            class="absolute -top-1 -right-1 z-10 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs font-bold shadow-md transition-colors duration-200"
                            on:click={() => deleteImage(index)}
                        >
                            ×
                        </button>
                    {/if}
                    <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
                    <!-- svelte-ignore a11y_img_redundant_alt -->
                    <div
                        class="relative bg-white border border-gray-200 h-fit w-fit rounded-lg overflow-hidden shadow-sm"
                    >
                        <!-- svelte-ignore a11y_click_events_have_key_events -->
                        <img
                            src={url}
                            alt="Image preview"
                            class="cursor-pointer max-h-[80px] aspect-square object-cover hover:scale-105 transition-transform duration-200"
                            on:click={() => openImageModal(index)}
                        />
                        <button
                            on:click={() => openImageModal(index)}
                            aria-label="Open image in modal"
                            class="absolute inset-0 flex items-center justify-center bg-black/0 hover:bg-black/30 text-white opacity-0 group-hover:opacity-100 transition-all duration-200"
                        >
                            <svg
                                class="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                />
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                />
                            </svg>
                        </button>

                        {#if compressionStatus[index] && compressionStatus[index] !== "Done"}
                            <div
                                class="absolute inset-0 bg-black/60 flex items-center justify-center text-white text-[10px] font-medium"
                            >
                                {compressionStatus[index]}
                            </div>
                        {/if}

                        <!-- {#if compressionComparison[index] && compressionStatus[index] === "Done"}
                            <div
                                class="absolute bottom-0 left-0 right-0 bg-black/70 text-[8px] text-white text-center py-0.5"
                            >
                                {compressionComparison[index]}
                            </div>
                        {/if} -->
                    </div>
                </div>
            {/each}
        {:else}
            <div class="flex items-center justify-center w-full h-full">
                <p class="text-gray-500">
                    {#if isCompressing}
                        <span class="flex items-center">
                            <svg
                                class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                <circle
                                    class="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    stroke-width="4"
                                ></circle>
                                <path
                                    class="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                            Optimizing Images
                            <span class="ml-1 animate-pulse">...</span>
                        </span>
                    {:else}
                        No images uploaded
                    {/if}
                </p>
            </div>
        {/if}
    </div>
</div>

{#if showModal && selectedImageIndex !== null}
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_static_element_interactions -->
    <div
        class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center"
        on:click={closeImageModal}
    >
        <div class="relative max-w-[90vw] max-h-[90vh]">
            <button
                class="absolute -top-6 -right-6 z-10 bg-red-500 hover:bg-red-700 text-white rounded-full w-10 h-10 flex items-center justify-center transition-colors duration-300 ease-in-out"
                on:click={closeImageModal}
            >
                ✕
            </button>
            <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
            <img
                src={previews[selectedImageIndex]}
                alt="Modal preview"
                class="max-w-[90vw] max-h-[90vh] object-contain"
                on:click={(e) => e.stopPropagation()}
            />
        </div>
    </div>
{/if}

<style>
    .flex-wrap {
        scrollbar-width: thin;
        -ms-overflow-style: auto;
    }

    .flex-wrap::-webkit-scrollbar {
        border-radius: 10px;
        display: block;
        width: 6px;
    }

    .flex-wrap::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .flex-wrap::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 10px;
    }

    .flex-wrap::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
</style>
