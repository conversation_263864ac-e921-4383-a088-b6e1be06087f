<script lang="ts">
    import { isEmptyString } from "$lib/common/utils/validation";
    import { Label, Input, Button } from "flowbite-svelte";
    import Loading from "./Loading.svelte";
    import { onMount } from "svelte";
    export let isLabel: boolean = true;
    export let label: string = "";
    export let searchTerm: string = "";
    export let onchange: (event: string) => void = (e: string) => {};
    export let selected: Array<any> = [];
    export let filteredData: Array<any> | null = [];
    export let level = "";
    export let selectedFunc: (selected: any) => void = () => {};
    export let loading = false;
    export let errorMap = new Map();
    export let fieldName: string = "";
    export let removeInput: (selected: any) => void = (selected) => {};
    export let isEditCase: boolean = false;
    export let showAddNewButton: boolean = false;
    let componentRef: HTMLDivElement | undefined;
    let showSuggestions = false;
    export let onAddNew: () => void = () => {};
    $: showSuggestions = searchTerm.length > 0 && Boolean(filteredData && filteredData.length >= 0);
    function handleClickOutside(event: MouseEvent) {
        if (componentRef && !componentRef.contains(event.target as Node)) {
            showSuggestions = false;
            searchTerm = "";
            filteredData = allData;
        }
    }
    onMount(() => {
        allData = filteredData;
        document.addEventListener("click", handleClickOutside);
        return () => {
            document.removeEventListener("click", handleClickOutside);
        };
    });
    function removeIn(i: number): any {
        selected.splice(i, 1);
        selected = selected;
        removeInput(selected);
    }
    let allData: Array<any> | null = [];
    function searchInput(event: string) {
        if (event.trim().length > 0) {
            onchange(event);
            if (filteredData?.length === 0) {
                filteredData = allData?.filter((data) => data.id === -1)!;
            }
        } else {
            filteredData = allData;
        }
    }
    $: {
        selected = selected;
    }

    $: {
        if (filteredData !== allData) {
            allData = filteredData;
        }
    }
</script>

<div bind:this={componentRef}>
    {#if isLabel}
        <Label for={label} class="mb-2  text-sm capitalize tracking-[0px]">
            {label}
            {#if errorMap}
                <span class={errorMap.has(fieldName) ? "text-red-500" : "text-black"}>*</span>
            {/if}
        </Label>
    {/if}
    <div class="relative text-sm">
        <div class="flex gap-2">
            <Input
                type="text"
                placeholder="Search {label}..."
                bind:value={searchTerm}
                class="border  p-2 font-poppins dark:bg-primary-700 {errorMap &&
                errorMap.has(fieldName)
                    ? 'border-red-500'
                    : ''}"
                on:input={(e) => {
                    // @ts-ignore
                    searchInput(e.currentTarget.value);
                    showSuggestions = true;
                }}
                on:change={(e) => {
                    // @ts-ignore
                    onchange(e.currentTarget.value);
                }}
                on:focus={() => {
                    if (searchTerm.length > 0) {
                        showSuggestions = true;
                    }
                }}
            />
            <button
                class="absolute right-0 top-0 p-4"
                on:click={() => {
                    if (searchTerm === "") {
                        searchTerm = " ";
                        showSuggestions = true;
                    } else {
                        searchTerm = "";
                        showSuggestions = false;
                    }
                }}
            >
                <img src="/images/dropdown.png" class="w-[15px]" alt="drop" />
            </button>
        </div>

        {#if searchTerm.length > 0 && showSuggestions && filteredData && !loading}
            <ul
                class="absolute z-50 mt-1 h-32 w-full overflow-y-auto rounded border border-gray-300 bg-white shadow-lg"
            >
                {#each filteredData as data}
                    <!-- svelte-ignore a11y-click-events-have-key-events -->
                    <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
                    <li
                        on:click={() => {
                            selectedFunc(data);
                            searchTerm = "";
                            showSuggestions = false;
                            selected = Array.from(new Set(selected));
                        }}
                        class="cursor-pointer p-2 text-sm transition-colors duration-200 hover:bg-gray-100"
                    >
                        {isEmptyString(level) ? data : data[`${level}`]}
                    </li>
                {/each}
            </ul>
            {#if showAddNewButton}
                <Button
                    class=" absolute rounded -bottom-[170px] z-50 right-0  w-full bg-red-500 p-2"
                    on:click={() => {
                        searchTerm = "";
                        showSuggestions = false;
                        onAddNew();
                    }}
                >
                    Add New
                </Button>
            {/if}
        {:else if showSuggestions && searchTerm.length > 0 && loading}
            <div
                class="absolute z-50 mt-1 w-full rounded border border-gray-300 bg-white shadow-lg"
            >
                <div class="flex items-center justify-center p-4">
                    <Loading />
                </div>
            </div>
        {:else if showSuggestions && searchTerm.length > 0 && !loading}
            <ul
                class="absolute z-10 mt-1 max-h-52 w-full overflow-y-auto rounded border border-gray-300 bg-white shadow-lg"
            >
                <li class="cursor-pointer p-2 bg-pink-700 hover:bg-gray-100">
                    No {label} Found
                </li>
            </ul>
        {/if}
    </div>
    <div class="m-1 flex flex-wrap gap-2">
        {#each selected as inputVal, i}
            <span
                id="badge-dismiss-pink-{inputVal}"
                class="mt-1 ml-0 inline-flex items-center rounded bg-gray-300 font-poppins px-2 py-1 text-sm font-medium text-black dark:bg-pink-900 dark:text-pink-300"
            >
                {isEmptyString(level) ? inputVal : inputVal[`${level}`]}

                {#if !isEditCase}
                    <button
                        on:click={() => removeIn(i)}
                        type="button"
                        class="ms-2 inline-flex items-center rounded-sm bg-transparent p-1 text-sm text-pink-400 hover:bg-pink-200 hover:text-pink-900 dark:hover:bg-pink-800 dark:hover:text-pink-300"
                        data-dismiss-target="#badge-dismiss-pink-{inputVal}"
                        aria-label="Remove"
                    >
                        <img src="/images/close.svg" alt="close" class="w-4 ml-1" />
                    </button>
                {/if}
            </span>
        {/each}
    </div>
</div>
