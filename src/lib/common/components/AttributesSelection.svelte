<script lang="ts">
    import CustomButton from "./admin/CustomButton.svelte";
    import SearchWithMultiSelect from "./SearchWithMultiSelect.svelte";
    import type {
        IItemAttribute,
        IItemAttributeValue,
    } from "$lib/item_attributes/models/IItemAttribute";
    import RedRoundCloseButton from "./RedRoundCloseButton.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { showErrorToast } from "../utils/common-utils";
    import { onMount } from "svelte";
    import { Spinner } from "flowbite-svelte";
    import CustomModal from "./CustomModal.svelte";
    import { closeActivity, showActivity } from "../utils/activity_store";

    export let onSelect = (
        data: {
            attribute: IItemAttribute;
            attributeValue: IItemAttributeValue;
        }[]
    ) => {};

    export let existingData: {
        attribute: IItemAttribute;
        attributeValue: IItemAttributeValue;
    }[];

    export let isDisabled = false;

    let currentSelectedData: {
        attribute: IItemAttribute | null;
        attributeValue: IItemAttributeValue | null;
    } = {
        attribute: null,
        attributeValue: null,
    };
    let allSelectedData: {
        attribute: IItemAttribute;
        attributeValue: IItemAttributeValue;
    }[] = [];

    let isLoadingAttributesData = true;
    let isLoadingAttributesValuesData = true;
    let attributesData: IItemAttribute[] = [];
    let attributeValuesData: IItemAttributeValue[] = [];
    let isAttributeError = false;
    let isAttributeValueError = false;
    let isAddNewAttribute = false;
    let isAddNewAttributeValue = false;
    let newAttributeName = "";
    let newAttributeValue = "";
    let isNameError = false;
    let isValueError = false;
    let filteredValues: IItemAttributeValue[] = [];

    const _removeAttribute = (selected: IItemAttribute[]) => {
        allSelectedData = allSelectedData.filter(
            (data) => !selected.some((s) => s.id === data.attribute.id)
        );
    };

    const loadData = () => {
        PresenterProvider.itemAttributesPresenter.getAllAttributes(1, 1000).then((response) => {
            if (!response.success) {
                return showErrorToast(response.message);
            }
            attributesData = response.data.data;
            attributesData = attributesData;
            isLoadingAttributesData = false;
        });
        PresenterProvider.itemAttributesPresenter
            .getAllAttributeValues(1, 1000)
            .then((response) => {
                if (!response.success) {
                    return showErrorToast(response.message);
                }
                attributeValuesData = response.data.data;
                isLoadingAttributesValuesData = false;
            });
    };

    const addNewAttribute = async () => {
        if (!newAttributeName || newAttributeName.trim().length === 0) {
            isNameError = true;
            return;
        }
        showActivity("Adding new attribute...");
        const response = await PresenterProvider.itemAttributesPresenter.createAttribute({
            name: newAttributeName.trim().toLocaleLowerCase(),
        });
        closeActivity();
        if (!response.success) {
            showErrorToast(response.message);
            return;
        }
        attributesData = [...attributesData, response.data!];
        isAddNewAttribute = false;
        newAttributeName = "";
        currentSelectedData = {
            attribute: response.data!,
            attributeValue: null,
        };
        isNameError = false;
    };
    const addNewAttributeValue = async () => {
        if (!newAttributeValue || newAttributeValue.trim().length === 0) {
            isValueError = true;
            return;
        }
        showActivity("Adding new attribute value...");
        const response = await PresenterProvider.itemAttributesPresenter.createAttributeValue({
            itemAttributeId: currentSelectedData.attribute!.id,
            itemAttributeValues: [
                {
                    title: newAttributeValue.trim().toLocaleLowerCase(),
                    value: newAttributeValue.trim().toLocaleLowerCase(),
                },
            ],
        });
        closeActivity();
        if (!response.success) {
            showErrorToast(response.message);
            return;
        }
        attributeValuesData = [...attributeValuesData, response.data![0]];
        isAddNewAttributeValue = false;
        newAttributeValue = "";
        currentSelectedData = {
            attribute: currentSelectedData.attribute,
            attributeValue: response.data![0],
        };
        isValueError = false;
    };
    const getFilteredValues = () => {
        const data = attributeValuesData.filter(
            (item) => item.itemAttributeId === currentSelectedData.attribute!.id
        );
            return data;
    };

    $: {
        if (currentSelectedData.attribute) {
            filteredValues = getFilteredValues();
        } else {
            filteredValues = [];
        }
    }

    onMount(() => {
        loadData();
        allSelectedData = existingData;
    });
</script>

{#if isLoadingAttributesData || isLoadingAttributesValuesData}
    <div class="flex h-[80vh]  w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <div class="rounded-lg border border-dashed  border-gray-500   p-[1rem]">
        <div class="flex items-center justify-between">
            <div>
                <SearchWithMultiSelect
                    showAddNewButton={true}
                    onAddNew={() => {
                        isAddNewAttribute = true;
                    }}
                    label="Select Attribute"
                    filteredData={attributesData.filter(
                        (data) =>
                            !allSelectedData.some((selected) => selected.attribute.id === data.id)
                    )}
                    fieldName={"options"}
                    level={"name"}
                    removeInput={(selected) => {
                        _removeAttribute(selected);
                    }}
                    selectedFunc={(e) => {
                        if (e.id === -1) {
                            isAddNewAttribute = true;
                            return;
                        }
                        if (
                            currentSelectedData.attribute &&
                            currentSelectedData.attribute.id === e.id
                        )
                            return;
                        currentSelectedData = {
                            attribute: e,
                            attributeValue: null,
                        };
                    }}
                />
                {#if isAttributeError}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        Please select an attribute
                    </p>
                {/if}

                {#if currentSelectedData && currentSelectedData.attribute}
                    <div class="mt-2 inline-flex items-center rounded-full bg-gray-100 px-3 py-1">
                        <span class="text-sm text-gray-800">
                            {currentSelectedData.attribute.name}
                        </span>
                        <button
                            aria-label="Remove this attribute"
                            class="ml-2 text-gray-500 hover:text-gray-700"
                            on:click={() => {
                                currentSelectedData = {
                                    attribute: null,
                                    attributeValue: null,
                                };
                            }}
                        >
                            <svg
                                class="h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"
                                />
                            </svg>
                        </button>
                    </div>
                {/if}
            </div>
            <!-- Generate dropdowns based on selected options -->
            {#if currentSelectedData.attribute}
                <div>
                    <SearchWithMultiSelect
                        label={"Select " + currentSelectedData.attribute.name}
                        level="title"
                        filteredData={filteredValues}
                        onAddNew={() => {
                        isAddNewAttributeValue = true;
                    }}
                    showAddNewButton={true}
                        removeInput={(selected) => {}}
                        selectedFunc={(e) => {
                            const value = e as IItemAttributeValue;
                            if (value.id === -1) {
                                isAddNewAttributeValue = true;
                                return;
                            }
                            if (
                                currentSelectedData.attributeValue &&
                                currentSelectedData.attributeValue.id === value.id
                            )
                                return;
                            currentSelectedData = {
                                attribute: currentSelectedData.attribute,
                                attributeValue: value,
                            };
                        }}
                    />
                    {#if isAttributeValueError}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            Please select an attribute value
                        </p>
                    {/if}

                    {#if currentSelectedData.attributeValue}
                        <div
                            class="mt-2 inline-flex items-center rounded-full bg-gray-100 px-3 py-1"
                        >
                            <span class="text-sm text-gray-800">
                                {currentSelectedData.attributeValue.title}
                            </span>
                            <button
                                aria-label="Remove this attribute value"
                                class="ml-2 text-gray-500 hover:text-gray-700"
                                on:click={() => {
                                    currentSelectedData.attributeValue = null;
                                    currentSelectedData = currentSelectedData;
                                }}
                            >
                                <svg
                                    class="h-4 w-4"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    {/if}
                </div>
                <CustomButton
                    title="Add"
                    cssClass="bg-black h-fit m-0"
                    onClick={() => {
                        if (!currentSelectedData.attribute) {
                            isAttributeError = true;
                            return;
                        }
                        if (!currentSelectedData.attributeValue) {
                            isAttributeValueError = true;
                            return;
                        }
                        allSelectedData = [
                            ...allSelectedData,
                            {
                                attribute: currentSelectedData.attribute!,
                                attributeValue: currentSelectedData.attributeValue!,
                            },
                        ];

                        onSelect(allSelectedData);
                        currentSelectedData = {
                            attribute: null,
                            attributeValue: null,
                        };
                    }}
                />
            {/if}
        </div>
        {#if allSelectedData.length > 0}
            <div class="text-lg font-semibold text-gray-700 mb-2">Selected Attributes</div>

            <div class="mt-4 grid grid-cols-6 gap-4">
                {#each allSelectedData as item}
                    <div
                        class="rounded-lg border border-gray-300 bg-white shadow-sm overflow-hidden"
                    >
                        <div class="bg-gray-100 px-4 py-3 flex justify-between items-center">
                            <span class="text-gray-800 font-semibold text-sm capitalize">
                                {item.attribute.name}
                            </span>
                            {#if !isDisabled}
                                <RedRoundCloseButton
                                    onClick={() => {
                                        allSelectedData = allSelectedData.filter(
                                            (data) =>
                                                !(
                                                    data.attribute.id === item.attribute.id &&
                                                    data.attributeValue.id ===
                                                        item.attributeValue.id
                                                )
                                        );
                                        onSelect(allSelectedData);
                                    }}
                                />
                            {/if}
                        </div>
                        <div class="p-4 flex justify-center items-center">
                            <span class="text-gray-900 text-base font-medium">
                                {item.attributeValue.title}
                            </span>
                        </div>
                    </div>
                {/each}
            </div>
        {/if}
    </div>
{/if}

<CustomModal title="Add New Attribute" bind:showModal={isAddNewAttribute}>
    <div class="p-4 w-[30rem]">
        <div class="mb-4">
            <label for="attribute-name" class="block text-sm font-medium text-gray-700">
                Attribute Name
            </label>
            <input
                id="attribute-name"
                type="text"
                bind:value={newAttributeName}
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm"
                placeholder="Enter attribute name"
            />
            {#if isNameError}
                <p class="pt-2 font-serif text-[14px] italic text-red-500">
                    Please enter attribute name
                </p>
            {/if}
        </div>
        <div class="flex justify-end">
            <CustomButton
                title="Save"
                cssClass="bg-black"
                onClick={() => {
                    addNewAttribute();
                }}
            />
        </div>
    </div>
</CustomModal>

<CustomModal title="Add New Attribute Value" bind:showModal={isAddNewAttributeValue}>
    <div class="p-4 w-[30rem]">
        <div class="mb-4">
            <label for="attribute-value" class="block text-sm font-medium text-gray-700">
                Attribute Value
            </label>
            <input
                id="attribute-value"
                type="text"
                bind:value={newAttributeValue}
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm"
                placeholder="Enter attribute value"
            />
            {#if isValueError}
                <p class="pt-2 font-serif text-[14px] italic text-red-500">
                    Please enter attribute value
                </p>
            {/if}
        </div>
        <div class="flex justify-end">
            <CustomButton
                title="Save"
                cssClass="bg-black"
                onClick={() => {
                    addNewAttributeValue();
                }}
            />
        </div>
    </div>
</CustomModal>
