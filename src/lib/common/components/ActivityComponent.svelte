<script lang="ts">
    import { Spinner } from "flowbite-svelte";
    export let show: boolean;
    export let message: string = "Please wait...";
</script>

{#if show}
 <div
    class="fixed inset-0 z-[9999] flex items-center justify-center bg-gray-900 bg-opacity-75 backdrop-blur-sm transition-opacity duration-300 animate-fadeIn"
    aria-modal="true"
    role="dialog"
    aria-labelledby="activity-dialog-heading"
>
    <div
        class="transform rounded-lg bg-white p-6 text-center shadow-2xl transition-all duration-300 dark:bg-primary-700 animate-slideIn"
        role="document"
    >
        <div class="flex flex-col items-center justify-center">
            <Spinner size="8" color="blue" class="mb-4 animate-modernSpin" />
            <h2 id="activity-dialog-heading" class="text-lg font-semibold text-gray-800 dark:text-gray-100">
                {message}
            </h2>
        </div>
    </div>
</div>

{/if}
