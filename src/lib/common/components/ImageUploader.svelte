<script lang="ts">
  export let files: File[] = [];
  let previews: string[] = [];
  let selected: string | null = null;
    //onupload ()=>IUploadResponse

    //lazy :boolean
    //upload : (()=>IUploadResponse )|null

  function handleChange(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files) return;

    files = Array.from(input.files);
    previews = files.map((f) => URL.createObjectURL(f));
  }

  function closePreview() {
    selected = null;
  }
  function removeImage(src:any) {
    previews= previews.filter((item)=>item!=src)
  }
</script>
<div class="container">
<input type="file" multiple accept="image/*" on:change={handleChange} />

{#if previews.length}
  <div class="mt-4 flex overflow-x-auto space-x-4 p-2">
    {#each previews as src}
      <img
        src={src}
        alt="Preview"
        class="w-40 h-40 object-cover rounded cursor-pointer"
        on:click={() => (selected = src)}
      />
       <button class="cursor-pointer text-red" on:click={() => removeImage(src)}>X</button>
    {/each}
  </div>
{/if}

{#if selected}
  <div class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
    <button on:click={closePreview} class="absolute top-4 right-4 text-white text-2xl">×</button>
    <img src={selected} alt="Full Preview" class="max-h-full max-w-full rounded" />
  </div>
{/if}
</div>