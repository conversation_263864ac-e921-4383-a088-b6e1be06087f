
export interface BaseModel {
    id: number;
    createdBy: string;
    createdAt: Date;
}

export interface UpdatedMetaData {
    updatedBy: string | null;
    updatedAt: Date | null;
}

export interface DeletedMetaData {
    deletedBy: string | null;
    deletedAt: Date | null;
}


export interface MetaUser {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
}

export interface ParsedMeta {
    createdBy: MetaUser;
    updatedBy: MetaUser | null;
    deletedBy: MetaUser | null;
    createdAt: Date;
    updatedAt: Date | null;
    deletedAt: Date | null;
}



export interface PaginatedBaseResponse<T> {
    totalData: number;
    currentPage: number;
    totalPages: number;
    data: T[];
}


export interface PaginatedDataWrapper<T> {
    pageSize: number;
    searchText?:string;
    pagination: PaginatedBaseResponse<T>;
    onPageChange: (page: number) => void;
}