import { browser } from '$app/environment';
import { Notyf } from 'notyf';
import 'notyf/notyf.min.css';

export let notyf: Notyf;
if (browser) {
  notyf = new Notyf({
    duration: 4000,
    position: {
      x: 'right',
      y: 'bottom',
    }
    , dismissible: true,
    types: [
      {
        type: 'error',
        duration: 2000,
        background: '#fe2e2e',
        ripple: false,
        dismissible: false,
        className: 'text-xs rounded-lg',
        position: {
          x: 'right',
          y: 'bottom',
        },

      },
      {
        type: 'success',
        duration: 2000,
        background: '#198c19',
        ripple: false,
        dismissible: false,
        className: 'text-xs rounded-lg',
        position: {
          x: 'right',
          y: 'bottom',
        },

      }

    ]
  });
}
