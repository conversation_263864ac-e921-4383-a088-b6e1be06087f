import type { IUploader } from "./IUploader";
import {  type DTO } from "$lib/common/models/BaseDTO";

import { PresenterProvider } from "$lib/PresenterProvider";
export class S3Uploader implements IUploader {

  async upload(files: File[], path: string): Promise<DTO<string[]> | null>{
    return await PresenterProvider.imageUploadPresenter.onUpload(files,path)
  }

  async delete(keys: string[]): Promise<DTO<string[]> | undefined> {
    return await PresenterProvider.imageUploadPresenter.deleteImages(keys);
}

}