import type {ICSVProvider} from "$lib/csv-provider/repositories/ICSVProvider";

export class CSVProvider<T extends object> implements ICSVProvider<T> {
    async save(data: T[], fileName: string, keyPathMap?: { [key: string]: string }): Promise<void> {
        if (!data || data.length === 0) {
            console.error("No data available for CSV export");
            return;
        }

        const csvRows: string[] = [];

        // Use keyPathMap headers if provided, otherwise use object keys
        const headers = ["Sr No.", ...(keyPathMap ? Object.keys(keyPathMap) : Object.keys(data[0]))];
        csvRows.push(headers.join(","));
        // Function to get nested values
        const getValue = (obj: any, path: string) => {
            return path.split(".").reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : ""), obj);
        };

        // Map data to CSV format
        data.forEach((row, index) => {
            const values = [
                index + 1, // Sr No.
                ...(headers.slice(1).map(header => {
                    const path = keyPathMap ? keyPathMap[header] : header;
                    const value = getValue(row, path);

                    // Format strings properly for CSV
                    return typeof value === "string" ? `"${value.replace(/"/g, '""')}"` : value;
                }))
            ];

            csvRows.push(values.join(","));
        });


        // Create CSV content
        const csvContent = csvRows.join("\n");
        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });

        // Create a download link
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", `${fileName}.csv`);
        document.body.appendChild(link);
        link.click();

        // Clean up
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
}