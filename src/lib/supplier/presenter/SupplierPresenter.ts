import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { ValidationErrors } from "$lib/common/utils/types";
import type { ISupplierPresenter } from "./ISupplierPresenter";
import type { ISupplier, ICreateSupplier } from "../models/ISupplier";
import { SupplierUtils } from "../utils/SupplierUtils";
import type { PaginatedBaseResponse, PaginatedDataWrapper } from "$lib/common/models/base_model";

export class SupplierPresenter implements ISupplierPresenter {
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<ISupplier>>> {
        return RepoProvider.supplierRepo.getAll(page, pageSize);
    }
    searchByText(text: string): Promise<DTO<PaginatedBaseResponse<ISupplier>>> {
        return RepoProvider.supplierRepo.searchByText(text);
    }
    getById(id: number): Promise<DTO<ISupplier>> {
        return RepoProvider.supplierRepo.getById(id);
    }
    onSubmit(payload: ICreateSupplier): Promise<DTO<ISupplier>> {
        return RepoProvider.supplierRepo.create(payload);
    }
    onUpdate(id: number, payload: ICreateSupplier): Promise<DTO<boolean>> {
        return RepoProvider.supplierRepo.update(id, payload);
    }
    onDelete(ids: number[]): Promise<DTO<boolean>> {
        return RepoProvider.supplierRepo.delete(ids);
    }
    onValidate(payload: ICreateSupplier): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = SupplierUtils.schema.safeParse(payload);
        console.log(result);

        if (!result.success) {
            result.error.issues.forEach((issue) => {
                if (issue.path.length > 1) {

                    errors.set(issue.path[0].toString() + "-" + issue.path[1].toString(), issue.message);
                }
                else {

                    errors.set(issue.path[0].toString(), issue.message);
                }
            });
        }

        return errors;
    }


}