import { z } from "zod";
import { SUPPLIER_STAUS, type ISupplier } from "../models/ISupplier";

export abstract class SupplierUtils {
    static getEmpty(): ISupplier {
        return {
            id: -1,
            email: null,
            name: "",
            phone: "",
            addressId: -1,
            address: {
                id: -1,
                street: "",
                city: "",
                state: "",
                postalCode: "",
                country: "",
            },

            gst: null,
            pan: null,
            status: SUPPLIER_STAUS.ACTIVE,
            createdAt: new Date(),
            updatedAt: null,
            deletedAt: null,
            createdBy: '',
            updatedBy: null,
            deletedBy: null,
        }
    }


    static schema = z.object({
        name: z.string().min(3, "Name must be at least 3 characters long").max(80, "Name must be less than 80 characters long"),
        email: z.string().email("Invalid email").nullable(),
        phone: z.string().min(10, "Phone number must be at least 10 characters long").max(15, "Phone number must be less than 15 characters long"),
        gst: z.string().length(15, "GST number must be 15 characters long").nullable(),
        pan: z.string().length(10, "PAN number must be 10 characters long").nullable(),
        address: z.object({
            street: z.string().min(3, "Street must be at least 3 characters long").max(100, "Street must be up to 100 characters long"),
            city: z.string().min(3, "City must be at least 3 characters long").max(50, "City must be up to 50 characters long"),
            state: z.string().min(3, "State must be at least 3 characters long").max(50, "State must be up to 50 characters long"),
            country: z.string().min(3, "Country must be at least 3 characters long").max(50, "Country must be up to 50 characters long"),
            postalCode: z.string().min(3, "Postal code must be at least 3 characters long").max(50, "Postal code must be up to 50 characters long"),
        }),
    });

}