<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, Select } from "flowbite-svelte";

    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import { SUPPLIER_STAUS, type ISupplier } from "../models/ISupplier";
    import { SupplierUtils } from "../utils/SupplierUtils";
    import EmptyAddressComponent from "$lib/address/components/EmptyAddressComponent.svelte";
    import { object } from "zod";
    import errorMap from "zod/locales/en.js";

    export let supplier: ISupplier = SupplierUtils.getEmpty();
    export let isInsideModal: boolean = false;
    export let onSubmitSuccess: null | ((data: ISupplier) => void) = null;

    let isDoingTask: boolean = false;

    let validationErrors: Map<string, string> = new Map();

    const handleEmail = (e: any) => {
        supplier.email = e.currentTarget.value.trim();
        if (supplier.email!.length === 0) {
            supplier.email = null;
        }
    };

    const handleGST = (e: any) => {
        supplier.gst = e.currentTarget.value.trim();
        if (supplier.gst!.length === 0) {
            supplier.gst = null;
        }
    };

    const handlePAN = (e: any) => {
        supplier.pan = e.currentTarget.value.trim();
        if (supplier.pan!.length === 0) {
            supplier.pan = null;
        }
    };

    const handleSubmit = async () => {
        supplier.phone = supplier.phone.toString();
        supplier.address.postalCode = supplier.address.postalCode.toString();

        validationErrors = PresenterProvider.supplierPresenter.onValidate(supplier);
        if (validationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        isDoingTask = true;
        let res;
        if (supplier.id > 0) {
            res = await PresenterProvider.supplierPresenter.onUpdate(supplier.id, {
                name: supplier.name,
                email: supplier.email,
                phone: supplier.phone,
                addressId: supplier.addressId,
                address: supplier.address,
                gst: supplier.gst,
                pan: supplier.pan,
                status: supplier.status,
            });
        } else {
            res = await PresenterProvider.supplierPresenter.onSubmit({
                name: supplier.name,
                email: supplier.email,
                phone: supplier.phone,
                address: supplier.address,
                gst: supplier.gst,
                pan: supplier.pan,
            });
        }

        if (res.success) {
            showSuccessToast(`Done`);
            if (onSubmitSuccess) {
                onSubmitSuccess(res.data! as ISupplier);
            } else {
                await goto("/admin/suppliers");
            }
        } else {
            showErrorToast(res.message);
            isDoingTask = false;
        }
    };
</script>

<div class="{isInsideModal ? '' : ''} flex items-center justify-center">
    <div class="{isInsideModal ? '' : ''} w-[90vw] p-2">
        {#if !isInsideModal}
            <div class="flex items-center justify-between py-2">
                <FormHeader label={supplier.id > 0 ? "Edit supplier" : "Add supplier"}></FormHeader>
                <BreadCrumbs breadCrumbData={[]} />
            </div>
            <hr class="mb-5" />
        {/if}
        <div class=" grid grid-cols-2 gap-6">
            <div>
                <Label for="name" class="mb-2 font-sans capitalize tracking-[0px]">
                    Name
                    {#if validationErrors.has("name")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>

                <Input
                    type="text"
                    id="name"
                    placeholder="Name"
                    class="uppercase dark:bg-primary-700 {validationErrors.has('name')
                        ? 'border-red-500'
                        : ''}"
                    bind:value={supplier.name}
                />
                {#if validationErrors.has("name")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("name")}
                    </p>
                {/if}
            </div>

            <div>
                <Label for="email" class="mb-2 font-sans capitalize tracking-[0px]">
                    Email (Optional)
                    {#if validationErrors.has("email")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>
                <Input
                    type="email"
                    id="email"
                    placeholder="Email (Optional)"
                    class="dark:bg-primary-700 {validationErrors.has('email')
                        ? 'border-red-500'
                        : ''}"
                    value={supplier.email}
                    on:change={handleEmail}
                />
                {#if validationErrors.has("email")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("email")}
                    </p>
                {/if}
            </div>
        </div>
        <div class="m-2"></div>

        <div class=" grid grid-cols-2 gap-6">
            <div>
                <Label for="phone" class="mb-2 font-sans capitalize tracking-[0px]">
                    Contact number
                    {#if validationErrors.has("phone")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>
                <Input
                    type="number"
                    id="phone"
                    placeholder="Contact number"
                    class="dark:bg-primary-700 {validationErrors.has('phone')
                        ? 'border-red-500'
                        : ''}"
                    bind:value={supplier.phone}
                />
                {#if validationErrors.has("phone")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("phone")}
                    </p>
                {/if}
            </div>

            {#if supplier.id > 0}
                <div>
                    <Label for="status" class="mb-2 font-sans capitalize tracking-[0px]">
                        Status
                        {#if validationErrors.has("status")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Select
                        id="type"
                        class="dark:bg-primary-700 {validationErrors.has('status')
                            ? 'border-red-500'
                            : ''}"
                        items={Object.values(SUPPLIER_STAUS).map((item) => ({
                            value: item,
                            name: item.toUpperCase(),
                        }))}
                        bind:value={supplier.status}
                    />
                    {#if validationErrors.has("status")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("status")}
                        </p>
                    {/if}
                </div>
            {/if}
        </div>
        <div class="m-2"></div>

        <div class=" grid grid-cols-2 gap-6">
            <div>
                <Label for="gst" class="mb-2 font-sans capitalize tracking-[0px]">
                    GST (Optional)
                    {#if validationErrors.has("gst")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>

                <Input
                    type="text"
                    id="gst"
                    placeholder="gst"
                    class="uppercase dark:bg-primary-700 {validationErrors.has('gst')
                        ? 'border-red-500'
                        : ''}"
                    value={supplier.gst}
                    on:input={handleGST}
                    on:change={handleGST}
                />
                {#if validationErrors.has("gst")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("gst")}
                    </p>
                {/if}
            </div>

            <div>
                <Label for="pan" class="mb-2 font-sans capitalize tracking-[0px]">
                    PAN (Optional)
                    {#if validationErrors.has("pan")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>
                <Input
                    type="text"
                    id="pan"
                    placeholder="pan (Optional)"
                    class="uppercase dark:bg-primary-700 {validationErrors.has('pan')
                        ? 'border-red-500'
                        : ''}"
                    value={supplier.pan}
                    on:change={handlePAN}
                />
                {#if validationErrors.has("pan")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("pan")}
                    </p>
                {/if}
            </div>
        </div>
        <div class="m-2"></div>

        <div class="mt-2"></div>
        <div class="mt-2"></div>
        <div>Address</div>
        <hr />

        <div class="m-2"></div>

        <EmptyAddressComponent address={supplier.address} errorMap={validationErrors} />

        <div class="mt-5 flex w-full justify-end">
            <CustomButton
                onClick={handleSubmit}
                cssClass="w-32 bg-black"
                title={"Save"}
                isLoading={isDoingTask}
            />
        </div>
    </div>
</div>
