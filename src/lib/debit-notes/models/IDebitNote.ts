import type { BaseModel, DeletedMetaData, UpdatedMetaData } from "$lib/common/models/base_model";


enum DEBIT_NOTE_STATUS {
    PAID = "PAID",
    UNPAID = "UNPAID",
}


enum DEBIT_NOTE_SOURCE {
    PRICE_MISMATCH = "price mismatch",
    REJECTED = "rejected",
    PRICE_MISMATCH_AND_REJECTED = "price mismatch and rejected",
}


interface IDebitNote {
    id: number;
    supplier: string;
    rawMaterial: string;
    qty: number;
    purchaseInvoiceId: number;
    purchaseInvoice: string;
    purchaseInvoiceDate: Date;
    actualPrice: number;
    purchasedPrice: number;
    debitAmount: number;
    status: DEBIT_NOTE_STATUS;
    source: DEBIT_NOTE_SOURCE;
    rejectionReason: string | null;
}



interface IMarkDebitNotePaid {
    purchaseInvoiceId: number;
    note: string | null;
}


interface IDebitNoteOverviewResponse {
    supplier: string;
    purchaseInvoiceId: number;
    purchaseInvoice: string;
    purchaseInvoiceDate: Date;
    source: DEBIT_NOTE_SOURCE;
    debitAmount: number;
}

export { type IDebitNote, DEBIT_NOTE_STATUS, type IMarkDebitNotePaid, type IDebitNoteOverviewResponse, DEBIT_NOTE_SOURCE };