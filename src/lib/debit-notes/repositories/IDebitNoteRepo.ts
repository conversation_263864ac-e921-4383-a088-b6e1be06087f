import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IDebitNote, IMarkDebitNotePaid } from "../models/IDebitNote";

export interface IDebitNoteRepo {
    getById(id: number): Promise<DTO<IDebitNote[]>>;
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IDebitNote>>>;
    searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IDebitNote>>>;
    markPaid(payload: IMarkDebitNotePaid): Promise<DTO<null>>;
}