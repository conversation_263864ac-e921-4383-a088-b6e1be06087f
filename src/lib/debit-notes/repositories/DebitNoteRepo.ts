import { DEBIT_NOTE_API_PATH, } from "$lib/common/configs/serverConfig";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    getUnhandledErrorDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { handleError } from "$lib/common/utils/logging";
import type { IDebitNoteRepo, } from "./IDebitNoteRepo";
import type { IDebitNote, IMarkDebitNotePaid } from "../models/IDebitNote";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class DebitNoteRepo implements IDebitNoteRepo {

    private _apiPath: string = DEBIT_NOTE_API_PATH;


    async markPaid(payload: IMarkDebitNotePaid): Promise<DTO<null>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<boolean> = await fetchData(
                this._apiPath + "/mark-paid/",
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getById(id: number): Promise<DTO<IDebitNote[]>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IDebitNote[]> = await fetchData(
                this._apiPath + "/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IDebitNote>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IDebitNote>> = await fetchData(this._apiPath + "/?page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IDebitNote>>> {
        try {
            const options = {
                method: "GET",
            };

            const res: FetchResult<PaginatedBaseResponse<IDebitNote>> = await fetchData(this._apiPath + "/searchByText?text=" + text, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
}
