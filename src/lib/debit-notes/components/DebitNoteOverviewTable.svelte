<script lang="ts">
    import {
        capitalizeFirstWord,
        debounce,
        formatDateUI,
        showErrorToast,
    } from "$lib/common/utils/common-utils";
    import { onMount } from "svelte";
    import Loader from "$lib/common/components/Loader.svelte";
    import type { IDebitNoteOverviewResponse } from "../models/IDebitNote";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";

    export let paginationData: PaginatedDataWrapper<IDebitNoteOverviewResponse>;
    export let searchTerm: string = "";

    let isLoading: boolean = false;
    let isSearching: boolean = false;
    let filteredData: IDebitNoteOverviewResponse[] = [];
    let alreadyFetchedData: IDebitNoteOverviewResponse[] = [];

    let debounceSearch = debounce(async (e: any) => {
        if (e.target.value.trim().length > 2) {
            filteredData = paginationData.pagination.data.filter((data) =>
                data.purchaseInvoice.toLowerCase().includes(searchTerm.toLowerCase())
            );
            if (filteredData.length === 0) {
                isSearching = true;

                const result = await PresenterProvider.debitNotePresenter.searchByText(
                    e.target.value.trim()
                );
                if (!result.success) {
                    return showErrorToast(result.message);
                } else {
                    filteredData = result.data.data;
                    isSearching = false;
                }
            }
        } else {
            filteredData = alreadyFetchedData;
        }
    }, 300);

    let selectedForPrint: IDebitNoteOverviewResponse | null = null;

    const print = (data: IDebitNoteOverviewResponse) => {
        selectedForPrint = data;

        setTimeout(() => {
            window.print();
            selectedForPrint = null;
        }, 200);
    };

    onMount(() => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if selectedForPrint}
    <div id="print-area">
        <h1 class="font-primary font-bold">{selectedForPrint && selectedForPrint.supplier}</h1>
    </div>
{:else}
    <h1
        class="mb-4 font-primary text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
    >
        Debit Notes
    </h1>

    <div class="relative overflow-x-auto font-primary shadow-md sm:rounded-lg">
        <div
            class="flex-column flex flex-wrap items-center justify-between space-y-4 rounded-lg bg-[#E3E3E3] p-4 text-sm focus:ring-primary-500 dark:bg-[#131313] md:flex-row md:space-y-0"
        >
            <label for="table-search" class="sr-only">Search</label>

            <div class="relative">
                <div
                    class="rtl:inset-r-0 pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
                >
                    <img src="/images/svg/search.svg" alt="l" width="15px" />
                </div>
                <input
                    type="text"
                    id="franchise-search"
                    bind:value={searchTerm}
                    on:input={debounceSearch}
                    class="block w-96 rounded-lg p-2 ps-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:bg-primary-800 dark:text-white"
                    placeholder="Search by invoice number"
                />
                {#if isSearching}
                    <div class="absolute right-0 top-0 bottom-0 flex items-center justify-center">
                        <Loader />
                    </div>
                {/if}
            </div>
            <div class="space-x-4"></div>
        </div>

        {#if isSearching}
            <div class="flex h-[30rem] w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <div class="max-h-[55vh] overflow-y-auto">
                <table class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
                    <thead
                        class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                    >
                        <tr>
                            <th scope="col" class="px-6 py-3">SR No.</th>
                            <th scope="col" class="px-6 py-3">Type</th>
                            <th scope="col" class="px-6 py-3">Supplier name</th>
                            <th scope="col" class="px-6 py-3">Invoice number</th>
                            <th scope="col" class="px-6 py-3">Invoice date</th>
                            <th scope="col" class="px-6 py-3">Source</th>
                            <th scope="col" class="px-6 py-3">Debit amount</th>
                            <!-- <th scope="col" class="px-6 py-3">Print</th> -->
                        </tr>
                    </thead>
                    <tbody>
                        {#each filteredData as row, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/debit-notes/view?id=" + row.purchaseInvoiceId}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {(paginationData.pagination.currentPage - 1) *
                                            paginationData.pageSize +
                                            index +
                                            1}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/debit-notes/view?id=" + row.purchaseInvoiceId}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {"TODO"}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/debit-notes/view?id=" + row.purchaseInvoiceId}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {capitalizeFirstWord(row.supplier)}
                                    </a>
                                </td>
    
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/debit-notes/view?id=" + row.purchaseInvoiceId}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.purchaseInvoice}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/debit-notes/view?id=" + row.purchaseInvoiceId}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {formatDateUI(row.purchaseInvoiceDate, false)}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/debit-notes/view?id=" + row.purchaseInvoiceId}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.source.toUpperCase()}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/debit-notes/view?id=" + row.purchaseInvoiceId}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        ₹ {row.debitAmount}
                                    </a>
                                </td>
    
                                <!-- <td class="px-6 py-4">
                                    <CustomButton
                                        title="Print"
                                        onClick={() => {
                                            print(row);
                                        }}
                                    />
                                </td> -->
                            </tr>
                        {/each}
                        {#if filteredData.length === 0}
                            <tr class="font-medium text-black dark:text-gray-400">
                                <td colspan="6" class="h-[50vh] text-center">Yet no debit notes</td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>
        {/if}
    </div>
    <PaginationButtons {paginationData} />
{/if}

<style>
    @media print {
        * {
            visibility: hidden; /* Hide everything else */
        }
        #print-area,
        #print-area * {
            visibility: visible; /* Show print area */
        }
        #print-area {
            position: absolute; /* Ensure it covers the page */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            padding: 20px; /* Add some padding */
            box-sizing: border-box; /* Include padding in width/height */
        }

        /* Important print styles */
        :global(body) {
            margin: 0; /* Remove default margins */
        }
        @page {
            margin: 2cm; /* Set page margins */
        }
    }
</style>
