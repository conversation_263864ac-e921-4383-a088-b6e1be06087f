import type {DTO} from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { IDashboardPresenter } from "./IDashboardPresenter";

export class DashboardPresenter implements IDashboardPresenter {
    async getLeadCountByStatus(data: { filter: string, statusType: string }): Promise<DTO<any>> {
        return await RepoProvider.dashboardRepo.getLeadCountByStatus(data);
    }

    async getAllLeadsByStatus(data: { filter: string }): Promise<DTO<any>> {
        return await RepoProvider.dashboardRepo.getAllLeadsByStatus(data);
    }

    async getLeadTimeSeries(data: { filter: string }): Promise<DTO<any>> {
        return await RepoProvider.dashboardRepo.getLeadTimeSeries(data);
    }
}
