import { ADMIN } from "$lib/common/configs/serverConfig";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { fetchData } from "$lib/fetch/utils/fetch-utils";;
import type { IDashboardRepo } from "./IDashboardRepo";

export class DashboardRepo implements IDashboardRepo {
    async getLeadCountByStatus(data: { filter: string, statusType: string }): Promise<DTO<any>> {
        try {
            const options = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            const api = ADMIN + `/analytics/leads/lead-status-by-status-type?filter=${data.filter}&statusType=${data.statusType}`;
            const res = await fetchData<any>(api, options);
            
            if (res.success) {
                const response = res.data;
                return getSuccessDTO(response)
            } else {
                return getSuccessDTO([])
            }
        } catch (error: any) {
            console.log(error);
            return getHandledErrorDTO(error)
        }
    }

    async getAllLeadsByStatus(data: { filter: string }): Promise<DTO<any>> {
        try {
            const options = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            const api = ADMIN + `/analytics/leads/lead-status?filter=${data.filter}`;
            const res = await fetchData<any>(api, options);

            if (res.success && res.data) {
                const response = res.data;
                return getSuccessDTO(response)
            } else {
                return getSuccessDTO([])
            }
        } catch (error: any) {
            console.log(error);
            return getHandledErrorDTO(error)
        }
    }

    async getLeadTimeSeries(data: { filter: string }): Promise<DTO<any>> {
        try {
            const options = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            const api = ADMIN + `/analytics/leads/lead-timeline?filter=${data.filter}`;
            const res = await fetchData<any>(api, options);

            if (res.success && res.data) {
                const response = res.data;
                return getSuccessDTO(response)
            } else {
                return getSuccessDTO([])
            }
        } catch (error: any) {
            console.log(error);
            return getHandledErrorDTO(error)
        }
    }
}
