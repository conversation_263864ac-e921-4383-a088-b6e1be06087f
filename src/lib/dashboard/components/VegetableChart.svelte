<script lang="ts">
  import ApexCharts from "apexcharts";
  import { onMount } from "svelte";
  import { PresenterProvider } from "$lib/PresenterProvider";

  const dropdownOptions = [
    "this_week",
    "last_week",
    "this_month",
    "last_month",
    "this_year",
    "last_year",
  ];

  let barChartSelected = "this_month";
  let isLoading: boolean = false;

  const options: any = {
    chart: {
      type: "bar",
      height: 350,
    },
    series: [],
    xaxis: {
      categories: [],
    },
  };

  let chart: ApexCharts | null = null;

  function renderChart() {
    if (chart) {
      chart.updateOptions(options);
    } else {
      chart = new ApexCharts(document.querySelector("#barChart"), options);
      chart.render();
    }
  }

  onMount(() => {
    fetchCardData();
    return () => {
      if (chart) {
        chart.destroy();
        chart = null;
      }
    };
  });

  const fetchCardData = async () => {
    isLoading = true;

    try {
      const res = await PresenterProvider.dashboardPresenter.getLeadTimeSeries({
        filter: barChartSelected,
      });

      // Set defaults to avoid undefined values
      const categories = res?.data?.label || [];
      const series = res?.data?.data || [];

      options.xaxis.categories = categories;
      options.series = [
        {
          name: "leads",
          data: series,
        },
      ];
      renderChart();
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      isLoading = false;
    }
  };

  function handleChange(event: any) {
    barChartSelected = event.target.value;
    fetchCardData();
  }
</script>

<div class="chart-container">
  <div class="dropdown-container ">
    <select class="statusName rounded border-[0.5px]" on:change={handleChange} bind:value={barChartSelected}>
      {#each dropdownOptions as option}
        <option value={option}>{option?.replace("_", " ")}</option>
      {/each}
    </select>
  </div>
  <div id="barChart"></div>
</div>

<style>
  .chart-container {
    border-radius: 10px;
    padding: 20px;
    margin-left: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
  }
  .chart-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  }
  .statusName {
    text-transform: capitalize;
  }
  .dropdown-container {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    padding: 20px;
  }
</style>
