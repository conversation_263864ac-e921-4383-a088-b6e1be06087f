import type { DTO } from "$lib/common/models/BaseDTO";
import type { ICreateItemUnit, IItemUnit } from "../models/IItemUnit";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export interface IItemUnitRepo {
    create(payload: ICreateItemUnit): Promise<DTO<IItemUnit>>;
    update(id: number, payload: ICreateItemUnit): Promise<DTO<boolean>>;
    delete(ids: number[]): Promise<DTO<boolean>>;
    getById(id: number): Promise<DTO<IItemUnit>>;
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemUnit>>>;
    searchByText(text:string): Promise<DTO<PaginatedBaseResponse<IItemUnit>>>;
}