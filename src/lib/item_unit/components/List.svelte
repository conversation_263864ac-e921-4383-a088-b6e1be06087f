<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { fade } from 'svelte/transition';

  export let items: { id: string | number; label: string; description?: string }[] = [];
  export let selectedId: string | number | null = null;
  export let emptyMessage: string = 'No items found';
  export let loading: boolean = false;
  export let class_name: string = '';

  const dispatch = createEventDispatcher<{
    select: { id: string | number; item: any };
  }>();

  function handleSelect(item: any) {
    selectedId = item.id;
    dispatch('select', { id: item.id, item });
  }
</script>

<div class="list-container {class_name}">
  {#if loading}
    <div class="list-loading">
      <div class="loading-spinner"></div>
      <span>Loading...</span>
    </div>
  {:else if items.length === 0}
    <div class="list-empty" transition:fade>
      {emptyMessage}
    </div>
  {:else}
    <ul class="list" role="list">
      {#each items as item (item.id)}
        <li
          class="list-item"
          class:selected={item.id === selectedId}
          role="listitem"
          on:click={() => handleSelect(item)}
          on:keydown={(e) => e.key === 'Enter' && handleSelect(item)}
          tabindex="0"
        >
          <div class="list-item-content">
            <span class="list-item-label">{item.label}</span>
            {#if item.description}
              <span class="list-item-description">{item.description}</span>
            {/if}
          </div>
        </li>
      {/each}
    </ul>
  {/if}
</div>

<style>
  .list-container {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
  }

  .list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .list-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #e2e8f0;
  }

  .list-item:last-child {
    border-bottom: none;
  }

  .list-item:hover {
    background-color: #f7fafc;
  }

  .list-item.selected {
    background-color: #ebf8ff;
  }

  .list-item:focus {
    outline: none;
    background-color: #f7fafc;
  }

  .list-item-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .list-item-label {
    font-size: 0.875rem;
    color: #1a202c;
    font-weight: 500;
  }

  .list-item-description {
    font-size: 0.75rem;
    color: #718096;
  }

  .list-empty {
    padding: 1.5rem;
    text-align: center;
    color: #718096;
    font-size: 0.875rem;
  }

  .list-loading {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    color: #718096;
    font-size: 0.875rem;
  }

  .loading-spinner {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid #e2e8f0;
    border-top-color: #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
</style> 