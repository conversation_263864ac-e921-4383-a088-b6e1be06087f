<script lang="ts">
    import { Button } from "flowbite-svelte";
    import { createEventDispatcher } from "svelte";
    import { fade, fly } from "svelte/transition";

    interface DropdownOption {
        label: string;
        value: string | number;
        disabled?: boolean;
    }

    export let options: DropdownOption[] = [];
    export let selected: string | number | null = null;
    export let placeholder: string = "Select an option";
    export let disabled: boolean = false;
    export let name: string = "";
    export let id: string = "";
    export let class_name: string = "";
    export let buttonLabel: string = "Add Label";
    export let onButtonClick: () => void = () => {};

    let isOpen = false;
    let dropdownRef: HTMLElement;

    const dispatch = createEventDispatcher<{
        change: { value: string | number };
    }>();

    function handleSelect(value: string | number) {
        selected = value;
        dispatch("change", { value });
        isOpen = false;
    }

    function handleClickOutside(event: MouseEvent) {
        if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
            isOpen = false;
        }
    }

    function toggleDropdown() {
        if (!disabled) {
            isOpen = !isOpen;
        }
    }

    // Get the label for the selected value
    $: selectedLabel = selected ? options.find((opt) => opt.value === selected)?.label : null;
</script>

<svelte:window on:click={handleClickOutside} />
<div
    class="dropdown-container relative border border-gray-300 rounded-md p-2 w-full {class_name}"
    bind:this={dropdownRef}
    role="combobox"
    aria-expanded={isOpen}
    aria-haspopup="listbox"
    aria-controls="dropdown-list"
>
    <button
        type="button"
        class="dropdown-button text-[17px] w-full flex justify-between items-center"
        class:disabled
        on:click={toggleDropdown}
        {disabled}
        {name}
        {id}
        aria-label={placeholder}
    >
        <span class="selected-text">
            {selectedLabel || placeholder}
        </span>
        <span class="dropdown-arrow" class:open={isOpen}>▼</span>
    </button>

    {#if isOpen}
        <div
            class="dropdown-list-container absolute left-0 right-0 mt-1"
            transition:fly={{ y: -10, duration: 200 }}
        >
            <div class="dropdown-list z-10 overflow-y-auto flex flex-col max-h-[150px] text-[15px]">
                {#each options as option}
                    <div
                        class="dropdown-item p-1"
                        class:selected={option.value === selected}
                        class:disabled={option.disabled}
                        role="option"
                        aria-selected={option.value === selected}
                        on:click={() => !option.disabled && handleSelect(option.value)}
                        on:keydown={(e) =>
                            e.key === "Enter" && !option.disabled && handleSelect(option.value)}
                        tabindex="0"
                    >
                        {option.label}
                    </div>
                {/each}
            </div>
            <Button class="rounded-none w-full bg-red-500 p-2 rounded-b-md" on:click={() => {
                if (onButtonClick) {
                    onButtonClick();
                }
                isOpen = false;
            }}>
                {buttonLabel}
            </Button>
        </div>
    {/if}
</div>

<style>
    .dropdown-container {
        position: relative;
        width: 100%;
    }

    .dropdown-list-container {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 0.375rem;
        box-shadow:
            0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 1;
    }
</style>
