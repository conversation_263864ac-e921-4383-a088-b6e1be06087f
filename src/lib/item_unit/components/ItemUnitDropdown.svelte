<script lang="ts">
    import { Select } from "flowbite-svelte";
    import type { IItemUnit } from "../models/IItemUnit";
    import { onMount } from "svelte";
    import { ItemUnitUtils } from "../utils/ItemUnitUtils";
    import CustomModal from "$lib/common/components/CustomModal.svelte";
    import ItemUnitForm from "./ItemUnitForm.svelte";
    import Dropdown from "./Dropdown.svelte";
    export let data: IItemUnit[] = [];
    export let id: string = "";
    export let cssClass: string = "";
    export let selectedValue: IItemUnit | null = null;
    export let onSelected: (data: IItemUnit | null) => void = () => {};

    let showAddNewModal: boolean = false;

    const pushAddNew = () => {
        const obj = ItemUnitUtils.getEmpty();
        obj.id = -1;
        obj.name = "Add new";
        data.push(obj);

        data = data;
    };

    onMount(() => {
        // pushAddNew();
    });
</script>

<!-- <Select
    {id}
    class={cssClass}
    items={data.map((item) => ({
        value: item.id,
        name: item.name.toUpperCase(),
    }))}
    value={selectedValue?.id ?? 0}
    on:change={(e: any) => {
        const value = e.currentTarget.value;

        if (value === "-1") {
            selectedValue = null;
            showAddNewModal = true;
        } else {
            selectedValue = data.find((item) => item.id === Number(value)) ?? null;
            onSelected(selectedValue);
        }
    }}
/> -->
<Dropdown
    options={data.map((item) => ({
        label: item.name,
        value: item.id,
    }))}
    selected={selectedValue?.id ?? 0}
    on:change={(e) => {
        const result = data.find((item) => item.id === e.detail.value);
        if (result) {
            onSelected(result);
        } else {
            onSelected(null);
        }
    }}
    buttonLabel="Add Unit"
    onButtonClick={() => {
        showAddNewModal = true;
    }}
/>

<CustomModal title="Add new item unit" bind:showModal={showAddNewModal}>
    <ItemUnitForm
        isInsideModal={true}
        onSubmitSuccess={(obj) => {
            data.push(obj);
            data = data.filter((item) => item.id !== -1);
            pushAddNew();
            selectedValue = obj;
            onSelected(obj);
            showAddNewModal = false;
        }}
    />
</CustomModal>
