# Dropdown Component

A reusable dropdown component with TypeScript support, accessibility features, and customizable styling.

## Features

- TypeScript support
- Keyboard navigation
- Accessibility (ARIA) support
- Customizable styling
- Disabled state support
- Individual option disabling
- Smooth animations
- Click outside to close

## Usage

```svelte
<script>
  import Dropdown from './Dropdown.svelte';

  // Basic options
  const options = [
    { label: 'Option 1', value: '1' },
    { label: 'Option 2', value: '2' },
    { label: 'Option 3', value: '3' }
  ];

  // Options with disabled items
  const optionsWithDisabled = [
    { label: 'Enabled Option', value: '1' },
    { label: 'Disabled Option', value: '2', disabled: true }
  ];

  let selected = null;
</script>

<!-- Basic usage -->
<Dropdown
  {options}
  bind:selected
  placeholder="Select an option"
  on:change={(e) => console.log('Selected:', e.detail.value)}
/>

<!-- With disabled items -->
<Dropdown
  options={optionsWithDisabled}
  bind:selected
  placeholder="Select an option"
/>

<!-- Disabled dropdown -->
<Dropdown
  {options}
  bind:selected
  disabled={true}
  placeholder="Select an option"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| options | `DropdownOption[]` | `[]` | Array of options with label and value |
| selected | `string \| number \| null` | `null` | Currently selected value |
| placeholder | `string` | `'Select an option'` | Placeholder text |
| disabled | `boolean` | `false` | Disable the entire dropdown |
| name | `string` | `''` | HTML name attribute |
| id | `string` | `''` | HTML id attribute |
| class_name | `string` | `''` | Custom CSS class |

## Events

| Event | Detail | Description |
|-------|--------|-------------|
| change | `{ value: string \| number }` | Fired when selection changes |

## Types

```typescript
interface DropdownOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}
```

## Styling

The component uses CSS classes that can be customized:

- `.dropdown-container`: Main container
- `.dropdown-button`: Button that opens the dropdown
- `.dropdown-list`: List of options
- `.dropdown-item`: Individual option
- `.selected`: Selected option
- `.disabled`: Disabled state
- `.open`: Open state for the dropdown arrow 