import type { DTO } from "$lib/common/models/BaseDTO";
import type { imageLinkAttributes, uploadImageAttributes } from "../models/IImageUpload";

export interface IImageUploadPresenter {
    getPresignedLinks(payload: imageLinkAttributes): Promise<DTO<string[]>>;
    onUpload(files: File[],path:string): Promise<DTO<string[]>|null>;
    deleteImages(keys:string[]): DTO<string[]> | PromiseLike<DTO<string[]> | undefined> 
    uploadImage(payload: uploadImageAttributes[]): Promise<DTO<uploadImageAttributes>[]>;
}