import { getSuccessDTO, type DTO } from "$lib/common/models/BaseDTO";
import { PresenterProvider } from "$lib/PresenterProvider";
import { RepoProvider } from "$lib/RepoProvider";
import type { imageLinkAttributes, uploadImageAttributes } from "../models/IImageUpload";
import type { IImageUploadPresenter } from "./IImageUploadPresenter";

export class ImageUploadPresenter implements IImageUploadPresenter {
    async getPresignedLinks(payload: imageLinkAttributes): Promise<DTO<string[]>> {
        return await RepoProvider.imageUploadRepo.getPresignedLinks(payload);
    }

    async uploadImage(payload: uploadImageAttributes[]): Promise<DTO<uploadImageAttributes>[]> {
        const res = await Promise.all(payload.map(async (item) => {

            return await RepoProvider.imageUploadRepo.uploadImage(item);
        }));
        return res;
    }

    async deleteImages (keys:string[]): Promise<DTO<string[]> | undefined> {
        return await RepoProvider.imageUploadRepo.deleteImages(keys);
    }

    async onUpload(files: File[], path:string): Promise<DTO<string[]> | null>{

        const payload: imageLinkAttributes = {
            moduleName: path,
            fileNames: files.map((file) => file.name),
        }
        const res = await PresenterProvider.imageUploadPresenter.getPresignedLinks(payload);
        if(!res.success){
            return null;
        }

            const data: string[] = res.data;
            // console.log("Uploaded Images:", data);
            const finalUrls = data.map((item) => {
                const SplitUrl = item.split("?")[0];


                return SplitUrl;
            });
            // console.log(finalUrls);
            const payload1 = files.map((file, index) => ({
                urlPath: data[index],  
                image: file,
            }));
            // console.log(typeof payload1[0].image);
            
            const resp = await PresenterProvider.imageUploadPresenter.uploadImage(payload1);

            if(!resp.every(item => item.success)){
                return null;
            }

            // console.log("Uploaded Images:", resp);
            return getSuccessDTO(finalUrls);
    }
}