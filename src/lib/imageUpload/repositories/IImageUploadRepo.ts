import type { DTO } from "$lib/common/models/BaseDTO";
import type {  imageLinkAttributes, uploadImageAttributes } from "../models/IImageUpload";

export interface IImageUploadRepo {
    deleteImages(keys:string[]): Promise<DTO<string[]>|undefined>;
    getPresignedLinks(payload: imageLinkAttributes): Promise<DTO<string[]>>;
    uploadImage(payload: uploadImageAttributes): Promise<DTO<uploadImageAttributes>>;
    // uploadImages(payload: File[], urldata: string[]): Promise<DTO<string[]> | null>;
}