import { getHandledErrorDTO, getSuccessDTO, getUnhandledErrorDTO, type D<PERSON> } from "$lib/common/models/BaseDTO";
import type { IImageUploadRepo } from "./IImageUploadRepo";
import type {  imageLinkAttributes, uploadImageAttributes } from "../models/IImageUpload";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { IMAGE_UPLOAD_API_PATH } from "$lib/common/configs/serverConfig";

export class imageUploadRepo implements IImageUploadRepo {
    private _apiPath: string = IMAGE_UPLOAD_API_PATH;

    async deleteImages(keys:string[]): Promise<DTO<string[]>|undefined> {
        try{
            const options={
                method:"DELETE",
                body:{keys},
            }
            const res: FetchResult<string[]>= await fetchData(this._apiPath + "/delete-Images",options);

            if(res.success){
                return getSuccessDTO(res.data!);
            }
            else{
                getHandledErrorDTO(res.message);
            }
        }catch(error:any){
            return getUnhandledErrorDTO(error,error);
        }
    }
    
    async getPresignedLinks(payload: imageLinkAttributes): Promise<DTO<string[]>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<string[]> = await fetchData(this._apiPath + "/get-presigned-links", options);

            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            return getUnhandledErrorDTO(error, error);
        }
    }

    async uploadImage(payload: uploadImageAttributes): Promise<DTO<uploadImageAttributes>> {
        // debugger;
        try {
            const options = {
                method: "PUT",
                body: payload.image,
                headers: {        
                    'Content-Type': payload.image.type || 'application/octet-stream',
                    // 'x-amz-acl': 'public-read',
                },
            };

            // console.log(payload.image.name);
            const res: Response = await fetch(payload.urlPath, options);


            if(!res.ok) {
                return getHandledErrorDTO(res.statusText);
            }
            
            return getSuccessDTO(payload);
            
        } catch (error: any) {
            return getUnhandledErrorDTO(error, error);
        }
    }
}