import { STORAGE_LOCATION_API_PATH, VENDOR_API_PATH } from "$lib/common/configs/serverConfig";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    getUnhandledErrorDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { handleError } from "$lib/common/utils/logging";
import type { IStorageLocationRepo, } from "./IStorageLocationRepo";
import type { ICreateStorageLocation, IStorageLocation } from "../models/IStorageLocation";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class StorageLocationRepo implements IStorageLocationRepo {
    private _apiPath: string = STORAGE_LOCATION_API_PATH;

    async create(payload: ICreateStorageLocation): Promise<DTO<IStorageLocation>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/create",
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async update(id: number, payload: ICreateStorageLocation): Promise<DTO<boolean>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<boolean> = await fetchData(
                this._apiPath + "/update/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    delete(ids: number[]): Promise<DTO<boolean>> {
        throw new Error("Method not implemented.");
    }

    async getById(id: number): Promise<DTO<IStorageLocation>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IStorageLocation> = await fetchData(
                this._apiPath + "/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IStorageLocation>> = await fetchData(this._apiPath + "/?page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IStorageLocation>> = await fetchData(this._apiPath + "/searchByText?text=" + text, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }


}
