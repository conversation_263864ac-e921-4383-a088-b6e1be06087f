import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { ICreateStorageLocation, IStorageLocation } from "../models/IStorageLocation";

export interface IStorageLocationRepo {
    create(payload: ICreateStorageLocation): Promise<DTO<IStorageLocation>>;
    update(id: number, payload: ICreateStorageLocation): Promise<DTO<boolean>>;
    delete(ids: number[]): Promise<DTO<boolean>>;
    getById(id: number): Promise<DTO<IStorageLocation>>;
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>>;
    searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>>;

}