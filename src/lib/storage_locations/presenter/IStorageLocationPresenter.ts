import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { ValidationErrors } from "../../common/utils/types";
import type { ICreateStorageLocation, IStorageLocation } from "../models/IStorageLocation";

export interface IStorageLocationPresenter {
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>>;
    searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>>;
    getById(id: number): Promise<DTO<IStorageLocation>>;
    onSubmit(payload: ICreateStorageLocation): Promise<DTO<IStorageLocation>>;
    onUpdate(id: number, payload: ICreateStorageLocation): Promise<DTO<boolean>>;
    onDelete(ids: number[]): Promise<DTO<boolean>>;
    onValidate(payload: ICreateStorageLocation): ValidationErrors;
}