import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { ValidationErrors } from "$lib/common/utils/types";
import type { IStorageLocationPresenter, } from "./IStorageLocationPresenter";
import type { IStorageLocation, ICreateStorageLocation } from "../models/IStorageLocation";
import { StorageLocationUtils } from "../utils/StorageLocationUtils";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class StorageLocationPresenter implements IStorageLocationPresenter {
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>> {
        return RepoProvider.storageLocationRepo.getAll(page, pageSize);
    }
    searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>> {
        return RepoProvider.storageLocationRepo.searchByText(text);
    }
    getById(id: number): Promise<DTO<IStorageLocation>> {
        return RepoProvider.storageLocationRepo.getById(id);
    }
    onSubmit(payload: ICreateStorageLocation): Promise<DTO<IStorageLocation>> {
        return RepoProvider.storageLocationRepo.create(payload);
    }
    onUpdate(id: number, payload: ICreateStorageLocation): Promise<DTO<boolean>> {
        return RepoProvider.storageLocationRepo.update(id, payload);
    }
    onDelete(ids: number[]): Promise<DTO<boolean>> {
        return RepoProvider.storageLocationRepo.delete(ids);
    }
    onValidate(payload: ICreateStorageLocation): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        const result = StorageLocationUtils.creationSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }


}