import { writable, derived, get } from 'svelte/store';
import { LogsApiService, type FlushLogsResponse } from '$lib/services/logsApiService';
import type { 
    IMorganLog, 
    LogStatistics, 
    PaginationInfo, 
    LogFilters
} from '$lib/types/logs';

// Core state stores
const availableDates = writable<string[]>([]);
const selectedDate = writable<string>('');
const logs = writable<IMorganLog[]>([]);
const pagination = writable<PaginationInfo | null>(null);
const statistics = writable<LogStatistics | null>(null);
const loading = writable<boolean>(false);
const error = writable<string | null>(null);
const filters = writable<LogFilters>({});
const flushing = writable<boolean>(false);

// Derived state
const hasNextPage = derived(pagination, ($pagination) => $pagination?.hasNextPage ?? false);
const hasPreviousPage = derived(pagination, ($pagination) => $pagination?.hasPreviousPage ?? false);
const currentPage = derived(pagination, ($pagination) => $pagination?.currentPage ?? 1);
const totalPages = derived(pagination, ($pagination) => $pagination?.totalPages ?? 0);
const totalData = derived(pagination, ($pagination) => $pagination?.totalData ?? 0);

// Actions
async function fetchAvailableDates() {
    loading.set(true);
    error.set(null);
    
    try {
        const response = await LogsApiService.getAvailableDates();
        availableDates.set(response.data);
        
        // Auto-select the most recent date if none is selected
        const currentDates = get(availableDates);
        const currentSelected = get(selectedDate);
        if (currentDates.length > 0 && !currentSelected) {
            const mostRecentDate = currentDates[0];
            selectedDate.set(mostRecentDate);
            // Fetch logs and stats for the auto-selected date
            await fetchLogsAndStats(mostRecentDate, 1, 50);
        }
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch available dates';
        error.set(errorMessage);
        console.error('Error fetching available dates:', err);
    } finally {
        loading.set(false);
    }
}

async function fetchPaginatedLogs(
    date: string, 
    page: number = 1, 
    pageSize: number = 50, 
    customFilters?: LogFilters
) {
    if (!date) return;
    
    loading.set(true);
    error.set(null);
    
    try {
        const activeFilters = customFilters || get(filters);
        console.log('📄 Fetching paginated logs:', { date, page, pageSize, activeFilters });
        
        const response = await LogsApiService.getPaginatedLogsByDate(date, page, pageSize, activeFilters);
        
        console.log('📄 Pagination Response:', response.data.pagination);
        
        logs.set(response.data.logs);
        pagination.set(response.data.pagination);
        
        // Update filters to reflect what was actually applied
        filters.set(activeFilters);
        
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch logs';
        error.set(errorMessage);
        console.error('Error fetching paginated logs:', err);
    } finally {
        loading.set(false);
    }
}

async function fetchStatistics(date: string) {
    if (!date) return;
    
    try {
        const response = await LogsApiService.getStatsByDate(date);
        statistics.set(response.data);
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch statistics';
        error.set(errorMessage);
        console.error('Error fetching statistics:', err);
    }
}

async function fetchLogsAndStats(
    date: string, 
    page: number = 1, 
    pageSize: number = 50, 
    customFilters?: LogFilters
) {
    if (!date) return;
    
    loading.set(true);
    error.set(null);
    
    try {
        const activeFilters = customFilters || get(filters);
        const [logsResponse, statsResponse] = await Promise.all([
            LogsApiService.getPaginatedLogsByDate(date, page, pageSize, activeFilters),
            LogsApiService.getStatsByDate(date)
        ]);
        
        console.log('📊 Stats API Response for', date, ':', statsResponse.data);
        console.log('📈 Full Statistics Object:', JSON.stringify(statsResponse.data, null, 2));
        
        logs.set(logsResponse.data.logs);
        pagination.set(logsResponse.data.pagination);
        statistics.set(statsResponse.data);
        
        // Update filters to reflect what was actually applied
        filters.set(activeFilters);
        
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch logs and statistics';
        error.set(errorMessage);
        console.error('Error fetching logs and stats:', err);
    } finally {
        loading.set(false);
    }
}

function setFilters(newFilters: LogFilters) {
    filters.set(newFilters);
    
    // Refetch logs with new filters
    const date = get(selectedDate);
    const currentPagination = get(pagination);
    if (date) {
        fetchPaginatedLogs(date, 1, currentPagination?.pageSize || 50, newFilters);
    }
}

function clearFilters() {
    filters.set({});
    
    // Refetch logs without filters
    const date = get(selectedDate);
    const currentPagination = get(pagination);
    if (date) {
        fetchPaginatedLogs(date, 1, currentPagination?.pageSize || 50, {});
    }
}

function nextPage() {
    const currentPag = get(pagination);
    const date = get(selectedDate);
    const currentFilters = get(filters);
    
    if (currentPag?.hasNextPage && date) {
        fetchPaginatedLogs(date, currentPag.currentPage + 1, currentPag.pageSize, currentFilters);
    }
}

function previousPage() {
    const currentPag = get(pagination);
    const date = get(selectedDate);
    const currentFilters = get(filters);
    
    if (currentPag?.hasPreviousPage && date) {
        fetchPaginatedLogs(date, currentPag.currentPage - 1, currentPag.pageSize, currentFilters);
    }
}

function goToPage(page: number) {
    const currentPag = get(pagination);
    const date = get(selectedDate);
    const currentFilters = get(filters);
    
    if (currentPag && date && page >= 1 && page <= currentPag.totalPages) {
        fetchPaginatedLogs(date, page, currentPag.pageSize, currentFilters);
    }
}

function setSelectedDate(date: string) {
    selectedDate.set(date);
    if (date) {
        // Reset pagination when changing dates
        pagination.set(null);
        fetchLogsAndStats(date, 1, 50);
    }
}

function changePageSize(newPageSize: number) {
    const date = get(selectedDate);
    const currentFilters = get(filters);
    if (date) {
        fetchPaginatedLogs(date, 1, newPageSize, currentFilters);
    }
}

async function flushLogs(): Promise<FlushLogsResponse | null> {
    flushing.set(true);
    error.set(null);
    
    try {
        const response = await LogsApiService.flushLogs();
        
        // After successful flush, refresh logs and stats if a date is selected
        const date = get(selectedDate);
        const currentPagination = get(pagination);
        if (date) {
            await fetchLogsAndStats(date, currentPagination?.currentPage || 1, currentPagination?.pageSize || 50);
        }
        
        return response;
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to flush logs';
        error.set(errorMessage);
        console.error('Error flushing logs:', err);
        return null;
    } finally {
        flushing.set(false);
    }
}

// Export the store and actions
export const logsStore = {
    // State
    availableDates: { subscribe: availableDates.subscribe },
    selectedDate: { subscribe: selectedDate.subscribe },
    logs: { subscribe: logs.subscribe },
    pagination: { subscribe: pagination.subscribe },
    statistics: { subscribe: statistics.subscribe },
    loading: { subscribe: loading.subscribe },
    error: { subscribe: error.subscribe },
    filters: { subscribe: filters.subscribe },
    flushing: { subscribe: flushing.subscribe },
    
    // Derived state
    hasNextPage: { subscribe: hasNextPage.subscribe },
    hasPreviousPage: { subscribe: hasPreviousPage.subscribe },
    currentPage: { subscribe: currentPage.subscribe },
    totalPages: { subscribe: totalPages.subscribe },
    totalData: { subscribe: totalData.subscribe },
    
    // Actions
    fetchAvailableDates,
    fetchPaginatedLogs,
    fetchStatistics,
    fetchLogsAndStats,
    setFilters,
    clearFilters,
    nextPage,
    previousPage,
    goToPage,
    setSelectedDate,
    changePageSize,
    flushLogs,
}; 