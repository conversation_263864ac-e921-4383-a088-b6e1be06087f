import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IItemAttribute, IItemAttributeValue, TItemAttributeCreateRequest, TItemAttributeValueCreateRequest } from "../models/IItemAttribute";

export interface IItemAttributesRepo {  
    getAllAttributes(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemAttribute>>>;
    getAllAttributeValues(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemAttributeValue>>>;
     createAttribute(payload: TItemAttributeCreateRequest): Promise<DTO<IItemAttribute>>;
    createAttributeValue(payload: TItemAttributeValueCreateRequest): Promise<DTO<IItemAttributeValue[]>>;
}