import { ITEM_ATTRIBUTES_API_PATH, ITEM_ATTRIBUTES_VALUES_API_PATH, PURCHASE_ORDER_API_PATH } from "$lib/common/configs/serverConfig";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import { getHandledErrorDTO, getSuccessDTO, getUnhandledErrorDTO, type DTO } from "$lib/common/models/BaseDTO";
import { handleError } from "$lib/common/utils/logging";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { IItemAttribute, IItemAttributeValue, TItemAttributeCreateRequest, TItemAttributeValueCreateRequest } from "../models/IItemAttribute";
import type { IItemAttributesRepo } from "./IItemAttributesRepo";

export class ItemAttributesRepo implements IItemAttributesRepo {
    private _apiPath: string = ITEM_ATTRIBUTES_API_PATH;
    private _apiPathValues: string = ITEM_ATTRIBUTES_VALUES_API_PATH;

    async getAllAttributes(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemAttribute>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IItemAttribute>> = await fetchData(this._apiPath + "/?page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getAllAttributeValues(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemAttributeValue>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IItemAttributeValue>> = await fetchData(this._apiPathValues + "/?page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async createAttribute(payload: TItemAttributeCreateRequest): Promise<DTO<IItemAttribute>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<IItemAttribute> = await fetchData(
                this._apiPath + "/",
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async createAttributeValue(payload: TItemAttributeValueCreateRequest): Promise<DTO<IItemAttributeValue[]>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<IItemAttributeValue[]> = await fetchData(
                this._apiPathValues + "/",
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
}