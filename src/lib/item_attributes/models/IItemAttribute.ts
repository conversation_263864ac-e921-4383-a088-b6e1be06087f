import { z } from "zod";

interface IItemAttribute {
  id: number;
  name: string;
}

interface IItemAttributeValue {
  id: number;
  itemAttributeId: number;
  value: string;
  title: string;
}



const ItemAttributeCreationSchema =
  z.object({
    name: z.string().min(3, "Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
  });

type TItemAttributeCreateRequest = z.infer<typeof ItemAttributeCreationSchema>;

const ItemAttributeValueCreationSchema =
  z.object({
    itemAttributeId: z.number().int().positive("Item attribute id must be a positive number"),
    itemAttributeValues: z.array(
      z.object({
        title: z.string()
          .min(1, "Title must be at least 1 character long")
          .max(100, "Title must be up to 100 characters long"),
        value: z.string()
          .min(1, "Value must be at least 1 character long")
          .max(100, "Value must be up to 100 characters long"),
      })
    ).min(1, "At least one item attribute value is required")
  });

type TItemAttributeValueCreateRequest = z.infer<typeof ItemAttributeValueCreationSchema>;

export {
  type IItemAttribute,
  type IItemAttributeValue,
  type TItemAttributeCreateRequest,
  type TItemAttributeValueCreateRequest
};