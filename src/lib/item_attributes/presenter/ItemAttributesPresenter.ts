import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { IItemAttribute, IItemAttributeValue, TItemAttributeCreateRequest, TItemAttributeValueCreateRequest } from "../models/IItemAttribute";
import type { IItemAttributesPresenter } from "./IItemAttributesPresenter";

export class ItemAttributesPresenter implements IItemAttributesPresenter {
    getAllAttributes(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemAttribute>>> {
        return RepoProvider.itemAttributesRepo.getAllAttributes(page, pageSize);
    }
    getAllAttributeValues(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemAttributeValue>>> {
        return RepoProvider.itemAttributesRepo.getAllAttributeValues(page, pageSize);
    }
    createAttribute(payload: TItemAttributeCreateRequest): Promise<DTO<IItemAttribute>> {
        return RepoProvider.itemAttributesRepo.createAttribute(payload);
    }
    createAttributeValue(payload: TItemAttributeValueCreateRequest): Promise<DTO<IItemAttributeValue[]>> {
        return RepoProvider.itemAttributesRepo.createAttributeValue(payload);
    }
}