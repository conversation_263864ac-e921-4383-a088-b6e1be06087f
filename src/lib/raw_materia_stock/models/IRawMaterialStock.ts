import type { BaseModel, DeletedMetaData, UpdatedMetaData } from "$lib/common/models/base_model";
import type { IUser } from "$lib/users/models/User";

interface IRawMaterialStock extends BaseModel, DeletedMetaData, UpdatedMetaData {
    id: number;
    rawMaterialId: number;
    rawMaterialName: string;
    msq: number;
    totalStock: number;
    usableStock: number;
    assignedStock: number;
}

interface IReceiveRawMaterialStock {
    rawMaterialId: number;
    qty: number;
    supplierId: number;
    unitCost: number;
    storageLocationId: number;
    factoryGateId: number;
}

interface IReceivedRawMaterialStockDetails extends BaseModel, DeletedMetaData, UpdatedMetaData {
    rawMaterialId: number;
    rawMaterial: string;
    qty: number;
    supplierId: number;
    supplier: string;
    unitCost: number;
    storageLocationId: number;
    storageLocation: string;
    factoryGateId: number;
    factoryGate: string;
}


interface IRawMaterialStockInDetails extends BaseModel, DeletedMetaData, UpdatedMetaData {
    rawMaterialId: number;
    rawMaterial: string;
    rawMaterialUnit: string;
    categoryname:string;
    categoryName: string;
    excessQty:number;
    replaceableQty:number;
    totalQty: number;
    rejectedQty: number;
    holdQty: number;
    supplierId: number;
    supplier: string;
    price: number;
    storageLocationId: number;
    storageLocation: string;
    factoryGateId: number;
    factoryGate: string;
    poNumber: string;
    purchaseInvoiceNumber: string;
    purchasedBy: string;
    receivedAt: Date;
}


interface IRawMaterialStockUpdateRequest {
    rawMaterialId: number;
    qty: number;
}


interface IAssignStorageToStock {
    id: number;
    storageLocationId: number;
}

interface IRawMaterialStockIssuance {
    entryId: string;
    soNumber: string;
    issuedToUserId: number;
    notes: string|null;
    rawMaterials: RawMaterials[]; 
    issuedAt: Date;
    issuedTo: IUser | null;
    issuedBy: IUser;
}

interface RawMaterials{
    rawMaterialId: number;
    name: string;
    qty: number
}

interface IRawMaterialStockIssuanceResponse extends BaseModel, DeletedMetaData, UpdatedMetaData {
    entryId: string;
    soNumber: string;
    notes:string|null;
    issuedTo: IUser | null;
    issuedBy: IUser;
    rawMaterials: {
        rawMaterial: IRawMaterialDetails;
        qty: number;
    }[];
    issuedAt: Date;
}

interface IRawMaterialDetails {
    id: number;
    name: string;
    unitName: string;
    categoryName: string;
    priceData: IRawMaterialPriceDetails[];
}

interface IRawMaterialPriceDetails {
    price: number;
    moq: number;
    supplierId: bigint;
    supplier: string;
    rawMaterialId: bigint;
    rawMaterial: string;
}

enum TABLE_FIELDS {
    PO_NUMBER = "PO_Number",
    INVOICE_NUMBER = "Invoice_Number",
    PURCHASED_BY = "Purchased_By",
    RAW_MATERIAL = "Raw_Material",
    CATEGORY = "Category",
    SUPPLIER = "Supplier",
    TOTAL_QTY = "Total_Qty",
    EXCESS_QTY = "Excess_Qty",
    REPLACEABLE_QTY = "Replaceable_Qty",
    REJECTED_QTY = "Rejected_Qty",
    HOLD_QTY = "Hold_Qty",
    STOCK_IN_QTY = "Stock_In_Qty",
    UNIT_PRICE = "Unit_Price",
    PRICE = "Price",
    STORAGE_LOCATION = "Storage_Location",
    FACTORY_GATE = "Factory_Gate",
    RECEIVED_ON = "Received_On",
    CREATED_AT = "Created_At",
}



export { TABLE_FIELDS, type IRawMaterialStock, type IReceiveRawMaterialStock, type IReceivedRawMaterialStockDetails, type IRawMaterialStockInDetails, type IRawMaterialStockUpdateRequest, type IAssignStorageToStock, type IRawMaterialStockIssuance, type IRawMaterialStockIssuanceResponse, type RawMaterials };