<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { isNumeric, showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, Select, CloseButton } from "flowbite-svelte";

    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import { onMount } from "svelte";

    import { RawMaterialStockUtils } from "../utils/RawMaterialStockUtils";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import type { IStorageLocation } from "$lib/storage_locations/models/IStorageLocation";
    import type { IFactoryGate } from "$lib/factory_gates/models/IFactoryGate";
    import type { IReceivedRawMaterialStockDetails } from "../models/IRawMaterialStock";
    import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import Loading from "$lib/common/components/Loading.svelte";

    export let obj: IReceivedRawMaterialStockDetails = RawMaterialStockUtils.getEmpty();

    let isDoingTask: boolean = false;
    let isLoadingFactoryGates: boolean = true;
    let isLoadingStorageLocations: boolean = true;
    let isLoadingSuppliers: boolean = true;
    let isLoadingRawMaterial: boolean = true;

    let validationErrors: Map<string, string> = new Map();

    let suppliers: ISupplier[] = [];
    let storageLocations: IStorageLocation[] = [];
    let factoryGates: IFactoryGate[] = [];
    let rawMaterial: IRawMaterialVariation;
    let selectedSupplier: ISupplier | null = null;

    const handleQty = (e: any) => {
        try {
            obj.qty = parseFloat(e.target.value);
        } catch (error) {
            obj.qty = 0;
        }
    };

    const handleUnitCost = (e: any) => {
        try {
            obj.unitCost = parseFloat(e.target.value);
        } catch (error) {
            obj.unitCost = 0;
        }
    };

    const handleSubmit = async () => {
        validationErrors = PresenterProvider.rawMaterialStockPresenter.onValidate(obj);
        if (validationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        isDoingTask = true;
        let res;

        res = await PresenterProvider.rawMaterialStockPresenter.onSubmit(obj);

        if (res.success) {
            showSuccessToast(`Done`);
            await goto("/admin/raw-materials-stock");
        } else {
            showErrorToast(res.message);
            isDoingTask = false;
        }
    };

    let dataArray: number[] = [1, 2];

    onMount(() => {
        isLoadingRawMaterial =
            isLoadingFactoryGates =
            isLoadingStorageLocations =
            isLoadingSuppliers =
                false;
    });
</script>

{#if isLoadingRawMaterial || isLoadingFactoryGates || isLoadingStorageLocations || isLoadingSuppliers}
    <Loading />
{:else}
    <div class="flex items-center justify-center">
        <div class=" w-[55rem] p-2">
            <div class=" flex items-center justify-between py-2">
                <FormHeader label={"Purchase stock"}></FormHeader>
                <BreadCrumbs breadCrumbData={[]} />
            </div>

            <hr class="mb-5" />

            <div class=" grid grid-cols-2 gap-4 mt-5">
                <div>
                    <Label for="invoiceNoId" class="mb-2 font-sans capitalize tracking-[0px]">
                        Invoice No.
                        {#if validationErrors.has("invoiceNoId")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="invoiceNoId"
                        placeholder="Invoice No."
                        class="dark:bg-primary-700 {validationErrors.has('invoiceNoId')
                            ? 'border-red-500'
                            : ''}"
                    />
                </div>

                <div>
                    <Label for="invoiceDate" class="mb-2 font-sans capitalize tracking-[0px]">
                        Invoice Date
                        {#if validationErrors.has("invoiceDate")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="date"
                        id="invoiceDate"
                        placeholder="Invoice No."
                        class="dark:bg-primary-700 {validationErrors.has('invoiceDate')
                            ? 'border-red-500'
                            : ''}"
                    />
                </div>
            </div>
            <div class="m-2"></div>

            <div class=" grid grid-cols-2 gap-6">
                <div>
                    <Label for="factoryGateId" class="mb-2 font-sans capitalize tracking-[0px]">
                        Factory Gate
                        {#if validationErrors.has("factoryGateId")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Select
                        id="factoryGateId"
                        class="dark:bg-primary-700 {validationErrors.has('factoryGateIdId')
                            ? 'border-red-500'
                            : ''}"
                        items={factoryGates.map((item) => ({
                            value: item.id,
                            name: item.name.toUpperCase(),
                        }))}
                        bind:value={obj.factoryGateId}
                    />
                    {#if validationErrors.has("factoryGateIdId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("factoryGateId")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="storageLocationId" class="mb-2 font-sans capitalize tracking-[0px]">
                        Storage Location
                        {#if validationErrors.has("storageLocationId")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Select
                        id="storageLocationId"
                        class="dark:bg-primary-700 {validationErrors.has('storageLocationIdId')
                            ? 'border-red-500'
                            : ''}"
                        items={storageLocations.map((item) => ({
                            value: item.id,
                            name: item.name.toUpperCase(),
                        }))}
                        bind:value={obj.storageLocationId}
                    />
                    {#if validationErrors.has("storageLocationIdId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("storageLocationId")}
                        </p>
                    {/if}
                </div>
            </div>
            <div class="m-2"></div>

            <div class=" grid grid-cols-1 mt-5">
                <div>
                    <SupplierSearch
                        selected={selectedSupplier}
                        onSelected={(data) => {
                            if (data) {
                                selectedSupplier = data;
                                obj.supplierId = data.id;
                            } else {
                                obj.supplierId = -1;
                            }
                        }}
                    />

                    {#if validationErrors.has("supplierId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("supplierId")}
                        </p>
                    {/if}
                </div>
            </div>

            <div class="m-2"></div>
            <span class="text-sm italic">Items</span>
            <div class="m-2"></div>

            <table
                class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right border"
            >
                <thead
                    class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                >
                    <tr>
                        <th scope="col" class="px-6 py-3">SR No.</th>
                        <th scope="col" class="px-6 py-3">Item</th>
                        <th scope="col" class="px-6 py-3">Qty</th>
                        <th scope="col" class="px-6 py-3">Price</th>
                        <th scope="col" class="px-6 py-3">Remove</th>
                    </tr>
                </thead>
                <tbody>
                    {#each dataArray as item, index}
                        <tr
                            class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                        >
                            <td class="px-6 py-4">
                                {index + 1}
                            </td>
                            <td class="px-6 py-4">
                                <!-- {capitalizeFirstWord(item.supplier.title)} -->
                                <Input placeholder={"Item"} />
                            </td>

                            <td class="px-6 py-4">
                                <Input type="number" placeholder={"Qty"} />
                            </td>
                            <td class="px-6 py-4">
                                <Input type="number" placeholder={"Price"} />
                            </td>

                            <td class="px-6 py-4">
                                {#if dataArray.length > 1}
                                    <CloseButton
                                        class="w-content"
                                        on:click={() => {
                                            dataArray.pop();
                                            dataArray = dataArray;
                                        }}
                                    />
                                {/if}
                            </td>
                        </tr>
                    {/each}
                </tbody>
            </table>

            <div class="m-2"></div>

            <div class="mt-5 flex w-full justify-between">
                <CustomButton
                    onClick={() => {
                        dataArray.push(1);
                        dataArray = dataArray;
                    }}
                    cssClass=" bg-black"
                    title={"Add new item"}
                    isLoading={isDoingTask}
                />
                <CustomButton
                    onClick={handleSubmit}
                    cssClass="w-32 bg-black"
                    title={"Save"}
                    isLoading={isDoingTask}
                />
            </div>
        </div>
    </div>
{/if}
