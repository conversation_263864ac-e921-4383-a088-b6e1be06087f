<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { isNumeric, showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, Select } from "flowbite-svelte";

    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import { onMount } from "svelte";

    import { RawMaterialStockUtils } from "../utils/RawMaterialStockUtils";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import type { IStorageLocation } from "$lib/storage_locations/models/IStorageLocation";
    import type { IFactoryGate } from "$lib/factory_gates/models/IFactoryGate";
    import type { IReceivedRawMaterialStockDetails } from "../models/IRawMaterialStock";
    import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
    import Loading from "$lib/common/components/Loading.svelte";

    export let obj: IReceivedRawMaterialStockDetails = RawMaterialStockUtils.getEmpty();

    let isDoingTask: boolean = false;
    let isLoadingFactoryGates: boolean = true;
    let isLoadingStorageLocations: boolean = true;
    let isLoadingSuppliers: boolean = true;
    let isLoadingRawMaterial: boolean = true;

    let validationErrors: Map<string, string> = new Map();

    let suppliers: ISupplier[] = [];
    let storageLocations: IStorageLocation[] = [];
    let factoryGates: IFactoryGate[] = [];
    let rawMaterial: IRawMaterialVariation;
    let selectedSupplier: ISupplier | null = null;

    const handleQty = (e: any) => {
        try {
            obj.qty = parseFloat(e.target.value);
        } catch (error) {
            obj.qty = 0;
        }
    };

    const handleUnitCost = (e: any) => {
        try {
            obj.unitCost = parseFloat(e.target.value);
        } catch (error) {
            obj.unitCost = 0;
        }
    };

    const handleSubmit = async () => {
        validationErrors = PresenterProvider.rawMaterialStockPresenter.onValidate(obj);
        if (validationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        isDoingTask = true;
        let res;

        res = await PresenterProvider.rawMaterialStockPresenter.onSubmit(obj);

        if (res.success) {
            showSuccessToast(`Done`);
            await goto("/admin/raw-materials-stock");
        } else {
            showErrorToast(res.message);
            isDoingTask = false;
        }
    };
    const _loadSuppliers = async () => {
        const response = await PresenterProvider.rawMaterialStockPresenter.getSuppliers(1,500);
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            suppliers = response.data.data;
            if (suppliers.length === 0) {
                showErrorToast("Please add at least one supplier.", 6);

                return goto("/admin/suppliers/add");
            }
            obj.supplierId = suppliers[0].id;

            isLoadingSuppliers = false;
        }
    };

    const _loadFactoryGates = async () => {
        const response = await PresenterProvider.rawMaterialStockPresenter.getFactoryGates(1,500);
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            factoryGates = response.data.data;
            if (factoryGates.length === 0) {
                showErrorToast("Please add at least one factory gate to add a raw material", 6);

                return goto("/admin/factory-gates/add");
            }
            obj.factoryGateId = factoryGates[0].id;

            isLoadingFactoryGates = false;
        }
    };

    const _loadStorageLocations = async () => {
        const response = await PresenterProvider.rawMaterialStockPresenter.getStorageLocations(1,500);
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            storageLocations = response.data.data;
            if (storageLocations.length === 0) {
                showErrorToast("Please add at least one storage location to add a raw material", 6);

                return goto("/admin/storage-locations/add");
            }
            obj.storageLocationId = storageLocations[0].id;

            isLoadingStorageLocations = false;
        }
    };

    const _loadRawMaterial = async () => {
        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        if (!id || !isNumeric(id)) {
            return goto("/");
        }

        const response = await PresenterProvider.rawMaterialStockPresenter.getRawMaterialDetails(
            Number(id)
        );
        if (!response.success) {
            showErrorToast(response.message);
            goto("/");
        } else {
            rawMaterial = response.data;
            obj.rawMaterialId = rawMaterial.id;

            isLoadingRawMaterial = false;
        }
    };

    onMount(() => {
        _loadRawMaterial();
        _loadFactoryGates();
        _loadStorageLocations();
        _loadSuppliers();
    });
</script>

{#if isLoadingRawMaterial || isLoadingFactoryGates || isLoadingStorageLocations || isLoadingSuppliers}
    <Loading />
{:else}
    <div class="mt-10 flex items-center justify-center">
        <div class="mt-5 w-[55rem] p-2">
            <div class="mb-5 mt-5 flex items-center justify-between py-2">
                <FormHeader label={"Receive raw material"}></FormHeader>
                <BreadCrumbs breadCrumbData={[]} />
            </div>

            <!-- raw material -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Details of Raw Material</h2>
                <hr />

                <div class="grid grid-cols-2 gap-4 mt-3">
                    <div>
                        <p class="font-medium text-gray-700">Name</p>
                        <p class="text-gray-900">{rawMaterial.name}</p>
                    </div>
                    <div>
                        <!-- <p class="font-medium text-gray-700">SKU</p>
                        <p class="text-gray-900">{rawMaterial.sku}</p> -->
                    </div>
                </div>
            </div>

            <div class=" grid grid-cols-3 gap-4 mt-5">
                <div>
                    <!-- <SupplierSearch
                        selected={selectedSupplier}
                        onselectedFunc={(data) => {
                            selectedSupplier = data;
                            obj.supplierId = data.id;
                        }}
                        onChange={() => {
                            obj.supplierId = -1;
                        }}
                    />

                    {#if validationErrors.has("supplierId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("supplierId")}
                        </p>
                    {/if} -->
                    <Label for="supplierId" class="mb-2 font-sans capitalize tracking-[0px]">
                        Supplier
                        {#if validationErrors.has("supplierId")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Select
                        id="supplierId"
                        class="dark:bg-primary-700 {validationErrors.has('supplierIdId')
                            ? 'border-red-500'
                            : ''}"
                        items={suppliers.map((item) => ({
                            value: item.id,
                            name: item.name.toUpperCase(),
                        }))}
                        bind:value={obj.supplierId}
                    />
                    {#if validationErrors.has("supplierIdId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("supplierId")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="qty" class="mb-2 font-sans capitalize tracking-[0px]">
                        Qty
                        {#if validationErrors.has("qty")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="qty"
                        placeholder="qty"
                        class="dark:bg-primary-700 {validationErrors.has('qty')
                            ? 'border-red-500'
                            : ''}"
                        value={obj.qty}
                        on:change={handleQty}
                    />
                    {#if validationErrors.has("qty")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("qty")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="cost" class="mb-2 font-sans capitalize tracking-[0px]">
                        Unit cost
                        {#if validationErrors.has("unitCost")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="cost"
                        placeholder="cost"
                        class="dark:bg-primary-700 {validationErrors.has('unitCost')
                            ? 'border-red-500'
                            : ''}"
                        value={obj.unitCost}
                        on:change={handleUnitCost}
                    />
                    {#if validationErrors.has("unitCost")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("unitCost")}
                        </p>
                    {/if}
                </div>
            </div>

            <div class="m-2"></div>

            <div class=" grid grid-cols-2 gap-6">
                <div>
                    <Label for="factoryGateId" class="mb-2 font-sans capitalize tracking-[0px]">
                        Factory Gate
                        {#if validationErrors.has("factoryGateId")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Select
                        id="factoryGateId"
                        class="dark:bg-primary-700 {validationErrors.has('factoryGateIdId')
                            ? 'border-red-500'
                            : ''}"
                        items={factoryGates.map((item) => ({
                            value: item.id,
                            name: item.name.toUpperCase(),
                        }))}
                        bind:value={obj.factoryGateId}
                    />
                    {#if validationErrors.has("factoryGateIdId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("factoryGateId")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="storageLocationId" class="mb-2 font-sans capitalize tracking-[0px]">
                        Storage Location
                        {#if validationErrors.has("storageLocationId")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Select
                        id="storageLocationId"
                        class="dark:bg-primary-700 {validationErrors.has('storageLocationIdId')
                            ? 'border-red-500'
                            : ''}"
                        items={storageLocations.map((item) => ({
                            value: item.id,
                            name: item.name.toUpperCase(),
                        }))}
                        bind:value={obj.storageLocationId}
                    />
                    {#if validationErrors.has("storageLocationIdId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("storageLocationId")}
                        </p>
                    {/if}
                </div>
            </div>
            <div class="m-2"></div>

            <div class="mt-5 flex w-full justify-end">
                <CustomButton
                    onClick={handleSubmit}
                    cssClass="w-32 bg-black"
                    title={"Save"}
                    isLoading={isDoingTask}
                />
            </div>
        </div>
    </div>
{/if}
