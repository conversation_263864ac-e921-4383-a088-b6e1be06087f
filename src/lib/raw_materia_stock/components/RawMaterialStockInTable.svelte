<script lang="ts">
import {
    Spinner
} from "flowbite-svelte";
import {
    capitalizeFirstWord,
    commaSeparateNumber,
    debounce,
    formatDateUI,
    getDateDifference,
    getFutureDate,
    getPastDate,
    separateSnakecase,
    showErrorToast,
} from "$lib/common/utils/common-utils";
import {
    onMount
} from "svelte";
import Loader from "$lib/common/components/Loader.svelte";
import {
    TABLE_FIELDS,
    type IRawMaterialStockInDetails
} from "../models/IRawMaterialStock";
import {
    goto
} from "$app/navigation";
import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
import type {
    PaginatedDataWrapper
} from "$lib/common/models/base_model";
import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
import {
    PresenterProvider
} from "$lib/PresenterProvider";
import SearchWithDrop from "$lib/common/components/SearchWithDrop.svelte";
import SearchDropWithCheckbox from "$lib/common/components/SearchDropWithCheckbox.svelte";
import {
    downloadAllCsv,
    downloadCsv
} from "../utils/RawMaterialStockUtils";

export let paginationData: PaginatedDataWrapper < IRawMaterialStockInDetails > ;
export let searchTerm: string = "";
export let showFilters: boolean = false;
export let onSearchClear: () => void = () => {};
export let selectedRowsMap: Map < number, IRawMaterialStockInDetails > = new Map();
export let withoutStorageLocation = false;

export let onDateFilterChange: (
    filter: {
        startDate: string;
        endDate: string;
        currentFilter: "today" | "week" | "month";
    } | null
) => void;

export let currentFilter: "today" | "week" | "month" | undefined;

let badLogic = "";
let isLoading: boolean = false;
let isSearching: boolean = false;
let filteredData: IRawMaterialStockInDetails[] = [];
let allCheckBoxArray: Array < string > = Object.values(TABLE_FIELDS);
let checkedCheckBoxArray: Array < string > ;
let selectAll = false;
let debounceSearch = debounce(async (e: any) => {
    if (searchTerm.trim().length === 0 && !currentFilter) {
        onSearchClear();
        return;
    }

    if (e.target.value.trim().length > 2) {
        isSearching = true;

        let result;

        if (withoutStorageLocation) {
            result =
                await PresenterProvider.rawMaterialStockPresenter.searchStockInByTextWithoutStorage(
                    e.target.value.trim()
                );
        } else {
            paginationData.searchText = e.target.value.trim();

            result = await PresenterProvider.rawMaterialStockPresenter.getStockInEntries(
                paginationData.pagination.currentPage,
                paginationData.pageSize,
                paginationData.searchText
            );
        }
        if (!result.success) {
            return showErrorToast(result.message);
        } else {
            filteredData = result.data.data;

            paginationData.pagination = result.data;

            isSearching = false;
        }
    }
}, 300);

function toggleSelectAll() {
    if (selectAll) {
        filteredData.forEach((rmData) => {
            selectedRowsMap.set(rmData.id, rmData);
        });
    } else {
        selectedRowsMap.clear();
        selectAll = false;
    }
    selectedRowsMap = selectedRowsMap;
}

function toggleRowSelection(row: IRawMaterialStockInDetails, id: number) {
    if (selectedRowsMap.has(id)) {
        selectedRowsMap.delete(id);
    } else {
        selectedRowsMap.set(id, row);
    }
}
const getDateOrMonthFormat = (num: number) => {
    return (num > 9 ? num : "0" + num).toString();
};
const exportAllDate: {
    startDate: Date;
    endDate: Date;
    loading: boolean;
    exportAllLoading: boolean;
} = {
    startDate: getPastDate(new Date(), 365),
    endDate: new Date(),
    loading: false,
    exportAllLoading: false,
};
const weekFilter = () => {
    const now = new Date();
    let currentDayOfWeek = now.getDay();

    const diffToSunday = now.getDate() - currentDayOfWeek;
    const startDate = new Date(now.setDate(diffToSunday));
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);

    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);

    return {
        startDate: startDate.getFullYear() +
            "-" +
            getDateOrMonthFormat(startDate.getMonth() + 1) +
            "-" +
            getDateOrMonthFormat(startDate.getDate()),
        endDate: endDate.getFullYear() +
            "-" +
            getDateOrMonthFormat(endDate.getMonth() + 1) +
            "-" +
            getDateOrMonthFormat(endDate.getDate() + 1),
    };
};

onMount(() => {
    filteredData = paginationData.pagination.data;

    searchTerm = paginationData.searchText?? "";
});
</script>

{#if isLoading}
<div class="flex h-[80vh] w-[100%] items-center justify-center">
    <Spinner size={"10"} color="gray" />
</div>
<!-- svelte-ignore a11y_no_static_element_interactions -->
{:else}
<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<div
    on:click={() => {
    badLogic = "";
    }}
    >
    <h1
        class="mb-4 font-primary text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
        >
        Raw Materials Stock In Entries
    </h1>
    <div>
        <div class="flex my-3 px-1 flex-col md:flex-row items-center gap-4 justify-end">
            <p class="text-sm italic text-red-500">* Date Range cannot be more than 1 year</p>
        </div>

        <div class="flex my-3 px-1 flex-col md:flex-row items-center gap-4 justify-end">
            <!-- Start Date Input -->
            <div class="flex flex-col md:flex-row md:items-center gap-2">
                <label for="start-date" class="font-medium text-gray-700">Start Date</label>
                <input
                    type="date"
                    id="start-date"
                    class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                    value={exportAllDate.startDate.toISOString().split("T")[0]}
                    on:input={(e) => {
                //  @ts-ignore
                const dd = new Date(e.target.value);
                const number = getDateDifference(dd, exportAllDate.endDate);
                if (number > 365) {
                exportAllDate.endDate = getFutureDate(dd, 365);
                exportAllDate.startDate = dd;
                } else if (number < 1) {
                exportAllDate.startDate = dd;
                exportAllDate.endDate = getFutureDate(exportAllDate.startDate, 1);
                }
                }}
                />
            </div>

            <!-- End Date Input -->
            <div class="flex flex-col md:flex-row md:items-center gap-2">
                <label for="end-date" class="font-medium text-gray-700">End Date</label>
                <input
                    type="date"
                    id="end-date"
                    class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                    value={exportAllDate.endDate.toISOString().split("T")[0]}
                    on:input={(e) => {
                //  @ts-ignore
                const dd = new Date(e.target.value);
                const number = getDateDifference(exportAllDate.startDate, dd);

                if (number > 365) {
                exportAllDate.startDate = getPastDate(dd, 365);
                exportAllDate.endDate = dd;
                } else if (number < 1) {
                exportAllDate.endDate = dd;
                exportAllDate.startDate = getPastDate(dd, 1);
                }
                }}
                />
            </div>

            <!-- Export Button -->
            <div class="h-full flex items-center">
                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class="inline-flex items-center justify-center rounded-lg border border-gray-300 text-gray-600 px-4 py-2 text-sm font-medium hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-red-300"
                    type="button"
                    disabled={exportAllDate.exportAllLoading}
                    on:click={async () => {
                    exportAllDate.exportAllLoading = true;
                    await downloadAllCsv(
                    exportAllDate.startDate,
                    exportAllDate.endDate,
                    searchTerm
                    );
                    exportAllDate.exportAllLoading = false;
                    }}
                    >
                    {#if exportAllDate.exportAllLoading}
                    <div class="flex gap-2 items-center justify-center">
                        Loading <Spinner class="size-5" />
                    </div>
                    {:else}
                    Export All
                    {/if}
                </button>
            </div>
        </div>
    </div>
    <div class="relative overflow-x-auto font-primary shadow-md sm:rounded-lg">
        <div
            class="flex-column flex flex-wrap items-center justify-between space-y-4 rounded-lg bg-[#E3E3E3] p-4 text-sm focus:ring-primary-500 dark:bg-[#131313] md:flex-row md:space-y-0"
            >
            <label for="table-search" class="sr-only">Search</label>

            <div class="relative">
                <div
                    class="rtl:inset-r-0 pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
                    >
                    <img src="/images/svg/search.svg" alt="l" width="15px" />
                </div>
                <input
                    type="text"
                    id="franchise-search"
                    bind:value={searchTerm}
                    on:input={debounceSearch}
                    class="block w-96 rounded-lg p-2 ps-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:bg-primary-800 dark:text-white"
                    placeholder="Search by raw material name, PO No. or Invoice No."
                    />

                {#if isSearching}
                <div
                    class="absolute right-0 top-0 bottom-0 flex items-center justify-center"
                    >
                    <Loader />
                </div>
                {/if}
            </div>
            <div class="space-x-4 flex">
                <div
                    on:click={(e) => {
                    e.stopPropagation();
                    badLogic = "1";
                    }}
                    >
                    <SearchDropWithCheckbox
                        searchTerm={badLogic}
                        bind:checkedCheckBoxArray
                        label={"Table Fields"}
                        isLabel={false}
                        filteredData={allCheckBoxArray}
                        />
                        </div>
                        <button
                            id="dropdownActionButton"
                            data-dropdown-toggle="dropdownAction"
                            class="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                            type="button"
                            on:click={() => {
                            goto("/admin/purchase-invoices/add");
                            }}
                            >
                            Add New
                        </button>
                        <!-- Export Button -->
                        <div class="h-full flex items-center">
                            <button
                                id="dropdownActionButton"
                                data-dropdown-toggle="dropdownAction"
                                class="inline-flex items-center justify-center rounded-lg border border-gray-300 text-gray-600 px-4 py-2 text-sm font-medium hover:bg-gray-100 focus:outline-none bg-gray-50 focus:ring-4 focus:ring-red-300"
                                type="button"
                                disabled={exportAllDate.loading}
                                on:click={async () => {
                                exportAllDate.loading = true;
                                await downloadCsv(
                                [...selectedRowsMap.values()],
                                "Raw Material Stock-In"
                                );
                                exportAllDate.loading = false;
                                }}
                                >
                                {#if exportAllDate.loading}
                                <div class="flex gap-2 items-center justify-center">
                                    Loading <Spinner class="size-5" />
                                </div>
                                {:else}
                                Export
                                {/if}
                            </button>
                        </div>
                        </div>
                        </div>

                        {#if showFilters}
                        <div class="my-6">
                            <span class="mx-3">Filter By Date Range</span>

                            <CustomButton
                                title="Today"
                                cssClass={currentFilter === "today" ? "!bg-primary-500" : "!bg-gray-400"}
                                onClick={() => {
                                if (currentFilter === "today") {
                                onDateFilterChange(null);
                                return;
                                }
                                const today = new Date();

                                onDateFilterChange({
                                startDate:
                                today.getFullYear() +
                                "-" +
                                getDateOrMonthFormat(today.getMonth() + 1) +
                                "-" +
                                getDateOrMonthFormat(today.getDate()),
                                endDate:
                                today.getFullYear() +
                                "-" +
                                getDateOrMonthFormat(today.getMonth() + 1) +
                                "-" +
                                getDateOrMonthFormat(today.getDate() + 1),
                                currentFilter: "today",
                                });
                                }}
                                />

                                <CustomButton
                                    title="This Week"
                                    cssClass={currentFilter === "week" ? "!bg-primary-500" : "!bg-gray-400"}
                                    onClick={() => {
                                    if (currentFilter === "week") {
                                    onDateFilterChange(null);
                                    return;
                                    }

                                    onDateFilterChange({
                                    ...weekFilter(),
                                    currentFilter: "week",
                                    });
                                    }}
                                    />

                                    <CustomButton
                                        title="This Month"
                                        cssClass={currentFilter === "month" ? "!bg-primary-500" : "!bg-gray-400"}
                                        onClick={() => {
                                        if (currentFilter === "month") {
                                        onDateFilterChange(null);
                                        return;
                                        }
                                        const today = new Date();

                                        onDateFilterChange({
                                        startDate:
                                        today.getFullYear() +
                                        "-" +
                                        getDateOrMonthFormat(today.getMonth() + 1) +
                                        "-" +
                                        "01",
                                        endDate:
                                        today.getFullYear() +
                                        "-" +
                                        getDateOrMonthFormat(today.getMonth() + 1) +
                                        "-" +
                                        31,
                                        currentFilter: "month",
                                        });
                                        }}
                                        />
                                        </div>
                                        {/if}
                                        {#if isSearching}
                                        <div class="flex h-[30rem] w-full items-center justify-center">
                                            <Loader />
                                        </div>
                                        {:else}
                                        <div class="max-h-[50vh] overflow-y-auto">
                                            <table
                                                class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right"
                                                >
                                                <thead
                                                    class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400 sticky top-0 z-10"
                                                    >
                                                    <tr class="!bg-primary text-white">
                                                        <th class="px-4 py-3">
                                                            <input
                                                                type="checkbox"
                                                                bind:checked={selectAll}
                                                                on:change={toggleSelectAll}
                                                                />
                                                        </th>
                                                        <th scope="col" class="px-6 py-3 whitespace-nowrap capitalize">
                                                            Sr. No.
                                                        </th>
                                                        {#if checkedCheckBoxArray.length > 0}
                                                        {#each checkedCheckBoxArray as checkboxData}
                                                        <th
                                                            scope="col"
                                                            class="px-6 py-3 whitespace-nowrap capitalize"
                                                            >
                                                            {separateSnakecase(checkboxData)}
                                                        </th>
                                                        {/each}
                                                        {/if}
                                                    </tr>
                                                </thead>
                                            </table>
                                            <table
                                                class="w-full h-fit text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right"
                                                >
                                                <tbody>
                                                    {#each filteredData as row, index}
                                                    <tr
                                                        class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                                                        >
                                                        <td class="px-4 py-4">
                                                            <input
                                                                type="checkbox"
                                                                checked={selectedRowsMap.has(row.id)}
                                                                on:change={() => toggleRowSelection(row, row.id)}
                                                            />
                                                        </td>
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {(paginationData.pagination.currentPage - 1) *
                                                                paginationData.pageSize +
                                                                index +
                                                                1}
                                                            </a>
                                                        </td>
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.PO_NUMBER)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.poNumber}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.INVOICE_NUMBER)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.purchaseInvoiceNumber}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.PURCHASED_BY)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.purchasedBy}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.RAW_MATERIAL)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {capitalizeFirstWord(row.rawMaterial)}
                                                            </a>
                                                        </td>
                                                        {/if}

                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.CATEGORY)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {capitalizeFirstWord(row.categoryName)}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.SUPPLIER)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {capitalizeFirstWord(row.supplier)}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.TOTAL_QTY)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.totalQty}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.EXCESS_QTY)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.excessQty}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.REPLACEABLE_QTY)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.replaceableQty}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.REJECTED_QTY)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.rejectedQty}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.HOLD_QTY)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.holdQty}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.STOCK_IN_QTY)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {(
                                                                row.totalQty -
                                                                (row.rejectedQty + row.holdQty)
                                                                ).toFixed(2)}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.UNIT_PRICE)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {commaSeparateNumber(row.price.toString())}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.PRICE)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {commaSeparateNumber(
                                                                (row.totalQty * row.price).toFixed(2)
                                                                )}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.STORAGE_LOCATION)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.storageLocation}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.FACTORY_GATE)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {row.factoryGate}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.RECEIVED_ON)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {formatDateUI(row.receivedAt, false)}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                        {#if checkedCheckBoxArray.includes(TABLE_FIELDS.CREATED_AT)}
                                                        <td class="px-6 py-4">
                                                            <a
                                                                href={row.storageLocation != null
                                                                ? "#"
                                                                : "/admin/assign-storage-to-raw-material/assign?id=" +
                                                                row.id}
                                                                class="block h-full w-full whitespace-nowrap font-medium"
                                                                >
                                                                {formatDateUI(row.createdAt)}
                                                            </a>
                                                        </td>
                                                        {/if}
                                                    </tr>
                                                    {/each}
                                                    {#if filteredData.length === 0}
                                                    <tr class="font-medium text-black dark:text-gray-400">
                                                        <td colspan="6" class="h-[50vh] text-center">
                                                            {searchTerm.trim().length > 0
                                                            ? "No results found"
                                                            : "Yet to receive"}
                                                        </td>
                                                    </tr>
                                                    {/if}
                                                </tbody>
                                            </table>
                                        </div>
                                        {/if}
                                        </div>
                                        <PaginationButtons {paginationData} />
                                        </div>
                                        {/if}
