import { z } from "zod";
import type { IRawMaterialStockInDetails, IRawMaterialStockIssuance, IRawMaterialStockIssuanceResponse, IReceivedRawMaterialStockDetails, RawMaterials } from "../models/IRawMaterialStock";
import { PresenterProvider } from "$lib/PresenterProvider";
import { CSVProvider } from "$lib/csv-provider/repositories/CSVProvider";
import { showErrorToast } from "$lib/common/utils/common-utils";
import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
import type { IUser } from "$lib/users/models/User";
import type {IRawMaterialStock} from "../models/IRawMaterialStock";

export abstract class RawMaterialStockUtils {
    static getEmptyRawMaterialStockIssuance(): IRawMaterialStockIssuance {
        return {
            soNumber: "",
            issuedToUserId: 0,
            notes: null,
            rawMaterials: [],
            issuedAt: new Date(),
            issuedTo: null,
            issuedBy: {
                id: 0,
                firstName: "",
                lastName: ""
            } as IUser,
            entryId: ""
        }
    }

    static getEmpty(): IReceivedRawMaterialStockDetails {
        return {
            id: -1,
            supplierId: -1,
            supplier: "",
            rawMaterialId: 1,
            rawMaterial: "",
            qty: 0,
            unitCost: 0,
            storageLocationId: -1,
            storageLocation: "",
            factoryGateId: -1,
            factoryGate: "",
            createdAt: new Date(),
            updatedAt: null,
            deletedAt: null,
            createdBy: '',
            updatedBy: null,
            deletedBy: null,
        }
    }


    static creationSchema = z.object({
        supplierId: z.number().int().positive('Supplier is invalid'),
        rawMaterialId: z.number().int().positive('Raw Material is invalid'),
        qty: z.number().int().positive('Qty must be positive'),
        unitCost: z.number().int().positive('Unit Cost must be positive'),
        storageLocationId: z.number().int().positive('Storage Location is invalid'),
        factoryGateId: z.number().int().positive('Factory Gate is invalid'),
    });

    static updateSchema = z.object({
        rawMaterialId: z.number().int().positive('Raw Material is invalid'),
        qty: z.number().positive('Qty must be positive'),
    });

    static assignStorageLocationSchema = z.object({
        id: z.number().int().positive('Raw Material is invalid'),
        storageLocationId: z.number().int().positive('Storage Location is invalid'),
    });

    static stockIssuanceSchema = z.object({
        issuedToUserId: z.number().gt(0, { message: "Issued to cannt be empty!" }),
        soNumber: z.string().min(3, 'SO Number is required!'),
    });
}



let keyMap: { [key: string]: string } = {
    "PO Number": "poNumber",
    "Invoice Number": "purchaseInvoiceNumber",
    "Purchased By": "purchasedBy",
    "Category": "categoryName",
    "Supplier": "supplier",
    "Raw Material": "rawMaterial",
    "Unit Price": "price",
    "Total Qty": "totalQty",
    "Rejected Qty": "rejectedQty",
    "Hold Qty": "holdQty",
    "Received On": "receivedAt",
    "Created At": "createdAt",
}

export const validateStockIssuanceSchema = (payload: RawMaterials[]): Map<string, string> => {
    let errorMap = new Map();

    payload.forEach((item, index) => {
        if (item.rawMaterialId <= 0) {
            errorMap.set("rawMaterialId" + index, "Item can't be empty!")
        }

        // if(item.qty < 0){
        //     errorMap.set("qty"+index,"Quantity can't be empty!");
        // }
    })
    return errorMap;
}

export const downloadCsv = async (data: IRawMaterialStockInDetails[], fileName: string) => {
    if (data.length === 0) {
        showErrorToast("Please choose data")
        return;
    }
    await new CSVProvider().save(data, fileName, keyMap);
}

export const downloadAllCsv = async (startDate: Date, endDate: Date, text?: string) => {
    const response = await PresenterProvider.rawMaterialStockPresenter.getStockInEntries(1, -1, text, startDate, endDate);
    if (response.success && response.data.data.length > 0) {
        await new CSVProvider().save(response.data.data, "Raw Material Stock-In", keyMap);
    } else {
        showErrorToast("No data found")
    }
}

export const parseCreateStockIssuance = (formData: IRawMaterialStockIssuance) => {
    let data = {
        soNumber: formData.soNumber,
        issuedToUserId: formData.issuedToUserId,
        notes: formData.notes,
        rawMaterials: formData.rawMaterials.map((item) => {
            let dd = {
                rawMaterialId: item.rawMaterialId,
                qty: item.qty
            }
            return dd;
        })
    }
    return data;
}




let keyMapForCurrentStock: { [key: string]: string } = {
    "Category": "categoryName",
    "Raw  Material": "rawMaterialName",
    "Average Price": "price",
    "Total Stock": "totalStock",
    "Usable Stock": "usableStock",
}

export const exportCurrentStock = async (category: IdTitle) => {
    const response: any = await PresenterProvider.rawMaterialStockPresenter.exportCurrentStock(category.id);
    console.log(response);

    if (response.success && response.data.length > 0) {
        await new CSVProvider().save(response.data, "Current Stock for " + category.title.toUpperCase(), keyMapForCurrentStock);
    } else {
        showErrorToast("No data found")
    }
}

export const exportSelectedStock = async (selectedRowsMap: Map<number, IRawMaterialStock>) => {
    let keyMapForSelectedCurrentStock: { [key: string]: string } = {
        "Category": "Category",
        "Raw  Material": "Raw Material",
        "Total Stock": "Total Stock",
        "Usable Stock": "Usable Stock",
    }
    const exportData: any[] = Array.from(selectedRowsMap.values()).map(
        (item: any) => ({
            Category: item.categoryName,
            "Raw Material": item.rawMaterialName,
            "Total Stock": item.totalStock,
            "Usable Stock": item.usableStock,
        })
    );
    await new CSVProvider().save(exportData, "Selected Stock", keyMapForSelectedCurrentStock);
}