import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IFactoryGate } from "$lib/factory_gates/models/IFactoryGate";
import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
import type { IStorageLocation } from "$lib/storage_locations/models/IStorageLocation";
import type { ISupplier } from "$lib/supplier/models/ISupplier";
import type { IUser } from "$lib/users/models/User";
import type { ValidationErrors } from "../../common/utils/types";
import type { IReceiveRawMaterialStock, IRawMaterialStock, IRawMaterialStockInDetails, IRawMaterialStockUpdateRequest, IAssignStorageToStock, IRawMaterialStockIssuance, IRawMaterialStockIssuanceResponse } from "../models/IRawMaterialStock";

export interface IRawMaterialStockPresenter {
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStock>>>;

    searchStockInByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;

    searchStockInByTextWithoutStorage(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;

    searchByRawMaterial(rawMaterialName: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStock>>>;

    onSubmit(payload: IReceiveRawMaterialStock): Promise<DTO<null>>;
    onValidate(payload: IReceiveRawMaterialStock): ValidationErrors;
    validateUpdate(payload: IRawMaterialStockUpdateRequest): ValidationErrors;
    getSuppliers(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<ISupplier>>>;
    getStorageLocations(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IStorageLocation>>>;
    getFactoryGates(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IFactoryGate>>>;
    getRawMaterialDetails(id: number): Promise<DTO<IRawMaterialVariation>>;
    getStockInEntries(page: number, pageSize: number,text?:string,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;

    getStockInEntriesByDate(
        startDate: string, endDate: string,
        page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;

    getUsers(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IUser>>>;

    update(payload: IRawMaterialStockUpdateRequest): Promise<DTO<null>>;

    getById(id: number): Promise<DTO<IRawMaterialStock>>;

    getByRawMaterialId(rawMaterialId: number): Promise<DTO<IRawMaterialStock>>;

    getStockInEntriesWithoutStorageLocation(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;


    getStockInEntryById(id: number): Promise<DTO<IRawMaterialStockInDetails>>;

    validateAssignStorageLocation(payload: IAssignStorageToStock): ValidationErrors;
    assignStorageLocation(payload: IAssignStorageToStock): Promise<DTO<null>>;

    validateStockIssuance(payload: IRawMaterialStockIssuance): ValidationErrors;

    issueStock(payload: IRawMaterialStockIssuance): Promise<DTO<null>>;

    getAllIssuedStock(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>>>;

    getStockIssuanceByEntryId(id:string): Promise<DTO<IRawMaterialStockIssuanceResponse>>;

    searchIssuedByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>>>;

    exportCurrentStock(categoryId:number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;
}