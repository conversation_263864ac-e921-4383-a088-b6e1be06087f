import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { ValidationErrors } from "$lib/common/utils/types";
import type { IUser, IUserCreateRequest, IUserUpdateRequest, ResetPasswordPayload } from "../models/User";

export interface IUserPresenter {
  getById(itemId: number): Promise<DTO<IUser>>;
  getAll(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IUser>>>;
  searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IUser>>>;
  onSubmit(payload: IUserCreateRequest): Promise<DTO<IUser>>;
  onUpdate(payload: IUserUpdateRequest): Promise<DTO<null>>;
  getByFirebaseToken(): Promise<DTO<IUser | null>>;
  onValidateCreate(user: IUserCreateRequest): ValidationErrors;
  onValidateUpdate(user: IUserUpdateRequest): ValidationErrors;

  onResetPassword(payload: ResetPasswordPayload): Promise<DTO<null>>;
  onResetPasswordPayloadValidate(payload: ResetPasswordPayload): ValidationErrors
}
