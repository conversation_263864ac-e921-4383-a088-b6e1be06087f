import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import { type DTO } from "$lib/common/models/BaseDTO";
import type { ValidationErrors } from "$lib/common/utils/types";
import { RepoProvider } from "$lib/RepoProvider";
import type { IUser, IUserCreateRequest, IUserUpdateRequest, ResetPasswordPayload } from "../models/User";
import { USIUserUtils } from "../utils/USIUserUtils";
import type { IUserPresenter } from "./IUserPresenter";

export class UserPresenter implements IUserPresenter {

  onValidateCreate(user: IUserCreateRequest): ValidationErrors {
    const errors: ValidationErrors = new Map();
    /* validate */
    const result = USIUserUtils.createSchema.safeParse(user);
    console.log(result);

    if (!result.success) {
      result.error.issues.forEach((issue) => {
        if (issue.path.length > 1) {

          errors.set(issue.path[0].toString() + "-" + issue.path[1].toString(), issue.message);
        }
        else {

          errors.set(issue.path[0].toString(), issue.message);
        }
      });
    }

    return errors;
  }

  onValidateUpdate(user: IUserUpdateRequest): ValidationErrors {
    const errors: ValidationErrors = new Map();
    /* validate */
    const result = USIUserUtils.updateSchema.safeParse(user);
    console.log("here", result);
    if (!result.success) {
      result.error.issues.forEach((issue) => {
        if (issue.path.length > 1) {

          errors.set(issue.path[0].toString() + "-" + issue.path[1].toString(), issue.message);
        }
        else {

          errors.set(issue.path[0].toString(), issue.message);
        }
      });
    }

    return errors;
  }

  onResetPasswordPayloadValidate(payload: ResetPasswordPayload): ValidationErrors {
    const errors: ValidationErrors = new Map();
    /* validate */
    const result = USIUserUtils.resetPasswordSchema.safeParse(payload);
    if (!result.success) {
      result.error.issues.forEach((issue) => {
        if (issue.path.length > 1) {

          errors.set(issue.path[0].toString() + "-" + issue.path[1].toString(), issue.message);
        }
        else {
          errors.set(issue.path[0].toString(), issue.message);
        }
      });
    }

    return errors;
  }


  getById(itemId: number): Promise<DTO<IUser>> {
    return RepoProvider.userRepo.getById(itemId);
  }

  onSubmit(payload: IUserCreateRequest): Promise<DTO<IUser>> {
    return RepoProvider.userRepo.saveUser(payload);
  }

  onUpdate(payload: IUserUpdateRequest): Promise<DTO<null>> {
    return RepoProvider.userRepo.updateUser(payload);
  }


  getAll(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IUser>>> {
    return RepoProvider.userRepo.getAll(page, pageSize, text);
  }
  searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IUser>>> {
    return RepoProvider.userRepo.searchByText(text);
  }

  getByFirebaseToken(): Promise<DTO<IUser>> {
    return RepoProvider.userRepo.getByFirebaseToken();
  }

  onResetPassword(payload: ResetPasswordPayload): Promise<DTO<null>>{
    return RepoProvider.userRepo.resetPassword(payload);
  }

}
