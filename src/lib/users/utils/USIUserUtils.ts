import { z } from "zod";
import { USI_USER_STAUS, type IUser, type IUserCreateRequest, type IUserUpdateRequest } from "../models/User";


export abstract class USIUserUtils {
    static getEmpty(): IUserCreateRequest & IUserUpdateRequest {
        return {
            id: -1,
            email: "",
            password: "",
            firstName: "",
            lastName: "",
            mobile: "",
            address: {
                id: -1,
                street: "",
                city: "",
                state: "",
                postalCode: "",
                country: "",
            },
            roleId: -1,
            status: USI_USER_STAUS.ACTIVE,
            coreUserId: -1,

        }
    }

    static addressSchema = z.object({
        street: z.string().min(3, "Street must be at least 3 characters long").max(100, "Street must be up to 100 characters long"),
        city: z.string().min(3, "City must be at least 3 characters long").max(50, "City must be up to 50 characters long"),
        state: z.string().min(3, "State must be at least 3 characters long").max(50, "State must be up to 50 characters long"),
        country: z.string().min(3, "Country must be at least 3 characters long").max(50, "Country must be up to 50 characters long"),
        postalCode: z.string().min(3, "Postal code must be at least 3 characters long").max(50, "Postal code must be up to 50 characters long"),
    });


    static createSchema = z.object({
        firstName: z.string().min(3, "firstName must be at least 3 characters").max(255, "firstName must be upto 255 characters"),
        lastName: z.string().min(3, "lastName must be at least 3 characters").max(255, "lastName must be upto 255 characters"),
        email: z.string().email("email is invalid"),
        password: z.string().min(6, "Password must be at least 6 characters long."),
        mobile: z.string().length(10, "mobile must be 10 characters"),
        address: USIUserUtils.addressSchema,
        roleId: z.number().int().positive("Invalid role"),
    });

    static updateSchema = z.object({
        firstName: z.string().min(3, "firstName must be at least 3 characters").max(255, "firstName must be upto 255 characters"),
        lastName: z.string().min(3, "lastName must be at least 3 characters").max(255, "lastName must be upto 255 characters"),
        mobile: z.string().length(10, "mobile must be 10 characters"),
        address: USIUserUtils.addressSchema,
        roleId: z.number().int().positive("Invalid role"),

    });

    static resetPasswordSchema =
        z.object({
            userId: z.number().positive("Invalid user Id"),
            password: z.string().min(6,"Password must be at least 6 characters"),
        });

}