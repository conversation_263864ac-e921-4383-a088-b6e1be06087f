<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, Select } from "flowbite-svelte";

    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import EmptyAddressComponent from "$lib/address/components/EmptyAddressComponent.svelte";
    import {
        USI_USER_STAUS,
        type IUser,
        type IUserCreateRequest,
        type IUserUpdateRequest,
        type ResetPasswordPayload,
    } from "$lib/users/models/User";
    import { USIUserUtils } from "$lib/users/utils/USIUserUtils";
    import type { IUserRole } from "$lib/user_role/models/IUserRole";
    import { onMount } from "svelte";
    import UserRoleDropdown from "$lib/user_role/components/UserRoleDropdown.svelte";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { RepoProvider } from "$lib/RepoProvider";
    import { userPermissions } from "$lib/common/utils/store";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";

    export let user: IUserCreateRequest | IUserUpdateRequest = USIUserUtils.getEmpty();
    export let isInsideModal: boolean = false;
    export let onSubmitSuccess: null | ((data: IUser) => void) = null;
    export let isEditCase: boolean = false;

    let isDoingTask: boolean = false;
    let isLoading: boolean = true;
    let isResettingPassword: boolean = false;
    let password: string = "";
    let showPassword = false;

    let validationErrors: Map<string, string> = new Map();
    let resetPasswordValidationErrors: Map<string, string> = new Map();
    let userRoles: IUserRole[] = [];

    const handleEmail = (e: any) => {
        if ("email" in user) {
            user.email = e.currentTarget.value.trim();
        }
    };

    const _loadUserRoles = async () => {
        const response = await PresenterProvider.userRolePresenter.getAll(1, 5000);
        if (!response.success) {
            return showErrorToast(response.message);
        }
        userRoles = response.data.data;
        // if ("id" in user && user.id <= 0) {
        //     user.roleId = userRoles[0].id;
        // }
        isLoading = false;
    };

    const handleForgotPassword = async () => {
        isLoading = true;
        if ("email" in user) {
            const res = await RepoProvider.authProvider.sendForgotPasswordLink(user.email);

            if (res.success) {
                showSuccessToast(
                    "You will recieve an email to reset password as registered email address!"
                );
            } else {
                showErrorToast(res.message ?? "Failed to send Email!");
            }
        } else {
            showErrorToast("Email not found!");
            return;
        }

        isLoading = false;
    };

    const handleResetPassword = async () => {
        try {
            isResettingPassword = true;

            if ("id" in user) {
                const resetPasswordPayload: ResetPasswordPayload = {
                    userId: user.coreUserId,
                    password: password,
                };

                resetPasswordValidationErrors =
                    PresenterProvider.userPresenter.onResetPasswordPayloadValidate(
                        resetPasswordPayload
                    );

                if (resetPasswordValidationErrors.size !== 0) {
                    return;
                }

                const res =
                    await PresenterProvider.userPresenter.onResetPassword(resetPasswordPayload);

                debugger;
                if (res.success) {
                    showSuccessToast("Password has been reset successfully.");
                } else {
                    resetPasswordValidationErrors;
                    showErrorToast(
                        res.message ??
                            "There was an issue with resetting your password. Please try again later."
                    );
                }
            } else {
                showErrorToast("User information is missing. Unable to reset your password!.");
            }
        } catch (error) {
            showErrorToast(
                "An unexpected error occurred while processing your request. Please try again."
            );
            console.error("Error resetting password:", error);
        } finally {
            isResettingPassword = false;
        }
    };

    const handleSubmit = async () => {
        // Ensure mobile and postalCode are strings
        user.mobile = user.mobile.toString();
        user.address.postalCode = user.address.postalCode.toString();

        isDoingTask = true;
        let res;

        // Validation and submission logic
        if ("id" in user && user.id > 0) {
            // Update flow
            validationErrors = PresenterProvider.userPresenter.onValidateUpdate({
                id: user.coreUserId,
                status: user.status,
                firstName: user.firstName,
                lastName: user.lastName,
                address: user.address,
                mobile: user.mobile,
                roleId: user.roleId,
                coreUserId: user.coreUserId,
            });

            if (validationErrors.size !== 0) {
                isDoingTask = false;
                showErrorToast("Please fill the required fields correctly");
                return;
            }

            res = await PresenterProvider.userPresenter.onUpdate({
                id: user.coreUserId,
                firstName: user.firstName,
                lastName: user.lastName,
                address: user.address,
                mobile: user.mobile,
                roleId: user.roleId,
                status: user.status,
                coreUserId: user.coreUserId,
            });
        } else {
            // Create flow
            validationErrors = PresenterProvider.userPresenter.onValidateCreate({
                firstName: user.firstName,
                lastName: user.lastName,
                email: "email" in user ? user.email : "",
                address: user.address,
                mobile: user.mobile,
                password: "password" in user ? user.password : "",
                roleId: user.roleId,
            });

            if (validationErrors.size !== 0) {
                isDoingTask = false;
                showErrorToast("Please fill the required fields correctly");
                return;
            }

            res = await PresenterProvider.userPresenter.onSubmit({
                firstName: user.firstName,
                lastName: user.lastName,
                email: "email" in user ? user.email : "",
                address: user.address,
                mobile: user.mobile,
                password: "password" in user ? user.password : "",
                roleId: user.roleId,
            });
        }

        if (res.success) {
            showSuccessToast(`Done`);
            if (onSubmitSuccess) {
                onSubmitSuccess(res.data! as IUser);
            } else {
                await goto("/admin/users");
            }
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };

    const togglePasswordVisibility = () => {
        showPassword = !showPassword;
    };

    onMount(() => {
        if ("id" in user && user.id > 0) {
            user.address.id = user.address.id;
        }
        _loadUserRoles();
    });
</script>

{#if isLoading}
    <PageLoader />
{:else}
    <div class="{isInsideModal ? '' : ''} flex items-center justify-center">
        <div class="{isInsideModal ? '' : ''} w-[90vw] p-2">
            {#if !isInsideModal}
                <div class="flex items-center justify-between py-2">
                    <FormHeader label={"id" in user && user.id > 0 ? "Edit User" : "Add User"}
                    ></FormHeader>
                    <BreadCrumbs breadCrumbData={[]} />
                </div>

                <hr class="mb-5" />
            {/if}
            <div class=" grid grid-cols-4 gap-6">
                <div>
                    <Label for="firstName" class="mb-2 font-sans capitalize tracking-[0px]">
                        First name
                        <span class="text-red-600">*</span>
                    </Label>

                    <Input
                        type="text"
                        id="firstName"
                        placeholder="firstName"
                        class="uppercase dark:bg-primary-700 {validationErrors.has('firstName')
                            ? 'border-red-500'
                            : ''}"
                        bind:value={user.firstName}
                    />
                    {#if validationErrors.has("firstName")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("firstName")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="lastName" class="mb-2 font-sans capitalize tracking-[0px]">
                        Last name
                        <span class="text-red-600">*</span>
                    </Label>

                    <Input
                        type="text"
                        id="lastName"
                        placeholder="lastName"
                        class="uppercase dark:bg-primary-700 {validationErrors.has('lastName')
                            ? 'border-red-500'
                            : ''}"
                        bind:value={user.lastName}
                    />
                    {#if validationErrors.has("lastName")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("lastName")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="mobile" class="mb-2 font-sans capitalize tracking-[0px]">
                        Contact number
                        <span class="text-red-600">*</span>
                    </Label>
                    <Input
                        type="number"
                        id="mobile"
                        placeholder="Contact number"
                        class="dark:bg-primary-700 {validationErrors.has('mobile')
                            ? 'border-red-500'
                            : ''}"
                        bind:value={user.mobile}
                    />
                    {#if validationErrors.has("mobile")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("mobile")}
                        </p>
                    {/if}
                </div>
                <div>
                    <Label for="roleId" class="mb-2 font-sans capitalize tracking-[0px]">
                        Role
                        <span class="text-red-600">*</span>
                    </Label>
                    <UserRoleDropdown
                        data={userRoles}
                        id="roleId"
                        cssClass="dark:bg-primary-700 {validationErrors.has('roleId')
                            ? 'border-red-500'
                            : ''}"
                        selectedValue={user.roleId}
                        onSelected={(data) => {
                            user.roleId = data;
                        }}
                    />
                </div>
            </div>
            <div class="m-2"></div>

            <div class=" grid grid-cols-4 gap-6">
                {#if !("id" in user && user.id > 0)}
                    <div>
                        <Label for="email" class="mb-2 font-sans capitalize tracking-[0px]">
                            Email
                            <span class="text-red-600">*</span>
                        </Label>
                        <Input
                            type="email"
                            id="email"
                            placeholder="Email"
                            class="lowercase dark:bg-primary-700 {validationErrors.has('email')
                                ? 'border-red-500'
                                : ''}"
                            value={"email" in user ? user.email : ""}
                            on:change={handleEmail}
                        />
                        {#if validationErrors.has("email")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("email")}
                            </p>
                        {/if}
                    </div>
                {/if}

                {#if !("id" in user && user.id > 0) && "password" in user}
                    <div>
                        <Label for="password" class="mb-2 font-sans capitalize tracking-[0px]">
                            Password
                            <span class="text-red-600">*</span>
                        </Label>
                        <div class="relative w-full">
                            <Input
                                type={showPassword ? "text" : "password"}
                                id="password"
                                placeholder="password"
                                class="dark:bg-primary-700 w-full pr-14 {validationErrors.has('password') ? 'border-red-500' : ''}"
                                bind:value={user.password}
                            />
                            <button
                                type="button"
                                on:click={togglePasswordVisibility}
                                class="absolute top-1/2 right-4 -translate-y-1/2 flex items-center text-gray-500 dark:text-gray-400 z-10"
                                tabindex="-1"
                            >
                                {#if showPassword}
                                    <img src="/images/openEye.png" class="h-5" alt="show" />
                                {:else}
                                    <img src="/images/closedEye.png" class="h-5" alt="hide" />
                                {/if}
                            </button>
                        </div>
                        {#if validationErrors.has("password")}
                            <span class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("password")}
                            </span>
                        {/if}
                    </div>
                {/if}
                {#if "id" in user && user.id > 0}
                    <div>
                        <Label for="status" class="mb-2 font-sans capitalize tracking-[0px]">
                            Status
                            <span class="text-red-600">*</span>
                        </Label>
                        <Select
                            id="type"
                            class="dark:bg-primary-700 {validationErrors.has('status')
                                ? 'border-red-500'
                                : ''}"
                            items={Object.values(USI_USER_STAUS).map((item) => ({
                                value: item,
                                name: item.toUpperCase(),
                            }))}
                            bind:value={user.status}
                        />
                        {#if validationErrors.has("status")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("status")}
                            </p>
                        {/if}
                    </div>
                {/if}
            </div>

            <div class="m-2"></div>

            <div class="mt-2"></div>
            <div class="mt-2"></div>
            <div>Address</div>
            <hr />

            <div class="m-2"></div>

            <EmptyAddressComponent address={user.address} errorMap={validationErrors} />

            <div class="mt-5 flex w-full gap-5 justify-end">
                <!-- {#if editCase}
                <CustomButton
                onClick={handleForgotPassword}
                cssClass="w-40 bg-black"
                title={"Reset Password"}
                        isLoading={isDoingTask}
                    />
                {/if} -->
                <CustomButton
                    onClick={handleSubmit}
                    cssClass="w-32 bg-black"
                    title={"Save"}
                    isLoading={isDoingTask}
                />
            </div>
        </div>
    </div>

    {#if isEditCase && $userPermissions.includes(AppPermissions.USER.PASSWORD_RESET)}
        <hr />
        <div class=" grid grid-cols-3 gap-6 mt-4">
            <div class="relative">
                <Label for="password" class="mb-2 font-sans capitalize tracking-[0px]">
                    Reset Password
                    <span class="text-red-600">*</span>
                </Label>
                <Input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    placeholder="Reset Password"
                    class="dark:bg-primary-700 {resetPasswordValidationErrors.has('password')
                        ? 'border-red-500'
                        : ''}"
                    bind:value={password}
                />
                {#if resetPasswordValidationErrors.has("password")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {resetPasswordValidationErrors.get("password")}
                    </p>
                {/if}

                <button
                    type="button"
                    on:click={togglePasswordVisibility}
                    class="absolute top-[55%] right-3 flex items-center text-gray-500 dark:text-gray-400"
                >
                    {#if showPassword}
                        <img src="/images/openEye.png" class="h-5" alt="show" />
                    {:else}
                        <img src="/images/closedEye.png" class="h-5" alt="hide" />
                    {/if}
                </button>
            </div>

            <CustomButton
                onClick={handleResetPassword}
                cssClass="w-40 bg-black !h-fit mt-8"
                title={"Reset Password"}
                isLoading={isResettingPassword}
            />
        </div>
        <div class="m-2"></div>
    {/if}
{/if}
