<script lang="ts">
    import { Select } from "flowbite-svelte";
    import { afterUpdate, onMount } from "svelte";
    import CustomModal from "$lib/common/components/CustomModal.svelte";
    import { USIUserUtils } from "$lib/users/utils/USIUserUtils";
    import type { IUser } from "$lib/users/models/User";
    import UserForm from "./UserForm.svelte";

    export let data: IUser[] = [];
    export let id: string = "";
    export let cssClass: string = "";
    export let selectedValue: number = 0;
    export let onSelected: (data: any) => void = () => {};
    export let disabled: boolean = false;

    let showAddNewModal: boolean = false;

    const pushAddNew = () => {
        const obj = USIUserUtils.getEmpty();
        obj.id = -1;
        obj.firstName = "Add new";
        data.push(obj as unknown as IUser);
        data = data;

        console.log("pushAddNew", data);
    };

    onMount(() => {
        for (const item of data) {
            item.id = item.id;
        }
        setTimeout(() => {
            pushAddNew();
        }, 800);
    });
</script>

<Select
    {id}
    {disabled}
    class={cssClass}
    items={data.map((item) => ({
        value: item.coreUserId.toString(),
        name: item.firstName.toUpperCase(),
    }))}
    value={selectedValue.toString()}
    on:change={(e) => {
        // @ts-ignore
        const value = e.currentTarget.value;

        if (value === "-1") {
            selectedValue = 1;
            showAddNewModal = true;
        } else {
            onSelected(value);
        }
    }}
/>

<CustomModal title="Add new user" bind:showModal={showAddNewModal}>
    <UserForm
        isInsideModal={true}
        onSubmitSuccess={(obj) => {
            data.push(obj);
            data = data.filter((item) => item.id.toString() !== "-1");
            pushAddNew();
            const id = obj.id;
            selectedValue = id;
            onSelected(id);
            showAddNewModal = false;
        }}
    />
</CustomModal>
