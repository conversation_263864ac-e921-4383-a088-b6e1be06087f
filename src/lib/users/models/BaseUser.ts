import type { BaseModel, DeletedMetaData, UpdatedMetaData } from "$lib/common/models/base_model";
import type { BaseUser } from "./UserDetails";

export interface AllUser extends BaseUser, BaseModel, UpdatedMetaData, DeletedMetaData {
    cart: any[];
}

export enum USER_STATUS {
    "ACTIVE" = "active",
    "INACTIVE" = "inactive",
    "DELETED" = "deleted",
}
 
export enum USER_TYPE {
    ADMIN = "admin",
    FRANSHISE = "franchise",
    SUPER_FRANSHISE = "super_franchise",
    GUEST = "guest",
    AFFILIATE = "affiliate",
    PROSPECT = "prospect",
}


