import { USI_USERS_API_PATH } from "$lib/common/configs/serverConfig";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import { getHandledErrorDTO, getSuccessDTO, type DTO } from "$lib/common/models/BaseDTO";
import { handleError } from "$lib/common/utils/logging";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { IUser, IUserCreateRequest, IUserUpdateRequest, ResetPasswordPayload } from "../models/User";
import type { IUserRepo } from "./IUserRepo";

export class UserRepo implements IUserRepo {


    async saveUser(user: IUserCreateRequest): Promise<DTO<IUser>> {
        try {
            const options = {
                method: "POST",
                body: user,
            };
            const res = await fetchData<FetchResult<IUser>>(USI_USERS_API_PATH + "/create", options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            console.log(error);

            return getHandledErrorDTO(error.message);
        }
    }
    async updateUser(user: IUserUpdateRequest): Promise<DTO<null>> {
        try {
            const options = {
                method: "PUT",
                body: user,
            };
            const res = await fetchData<FetchResult<null>>(USI_USERS_API_PATH + "/update/" + user.id, options);
            if (res.success) {
                return getSuccessDTO(res.data);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            console.log(error);

            return getHandledErrorDTO(error.message);
        }
    }



    async getById(id: number, token?: string): Promise<DTO<IUser>> {
        try {
            const option = token
                ? { headers: { Authorization: `Bearer ${token}`, method: "GET" } }
                : { method: "GET" };

            console.log(option);
            const res = await fetchData<FetchResult<IUser>>(
                `${USI_USERS_API_PATH}/${id}`,
                option
            );
            console.log(res);

            if (res.success && res.data) {
                return getSuccessDTO(res.data);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getHandledErrorDTO(error.message);
        }
    }

    async getAll(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IUser>>> {
        try {
            const options = {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            };
            const api = USI_USERS_API_PATH;

            let query = "?page=" + page + "&pageSize=" + pageSize;
            if (text) {
                query += "&text=" + text;
            }


            const res: FetchResult<PaginatedBaseResponse<IUser>> = await fetchData(api + query, options);

            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            console.log(error);
            return getHandledErrorDTO(error);
        }
    }
    async searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IUser>>> {
        try {
            const options = {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            };
            const api = USI_USERS_API_PATH;

            const res: FetchResult<PaginatedBaseResponse<IUser>> = await fetchData(api + "/searchByText?text=" + text, options);

            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            console.log(error);
            return getHandledErrorDTO(error);
        }
    }

    async getByFirebaseToken(): Promise<DTO<IUser>> {
        try {
            const options = {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            };
            const api = USI_USERS_API_PATH + "/getByFirebaseToken";

            const res = await fetchData<any>(api, options);

            if (res.success && res.data) {

                return getSuccessDTO(res.data);
            } else {
                return getHandledErrorDTO("Invalid token");
            }
        } catch (error: any) {
            console.log(error);
            return getHandledErrorDTO(error);
        }
    }

    async resetPassword(payload: ResetPasswordPayload): Promise<DTO<null>> {
        try {
            const options = {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: payload,

            };

            console.log("payload =>", payload);

            const res = await fetchData<FetchResult<null>>(
                `${USI_USERS_API_PATH}/reset-password`,
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getHandledErrorDTO(error.message);
        }
    }

}
