import { ADMIN, TAX } from "$lib/common/configs/serverConfig";
import { getHandledErrorDTO, getSuccessDTO, type DTO } from "$lib/common/models/BaseDTO";
import { handleError } from "$lib/common/utils/logging";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import { parseTax } from "../utils/tax-utils";
import type { ITaxRepo } from "./ITaxRepo";
import { type ITax, type TaxRatePayload } from "../models/Tax";
import type { FetchResultAll } from "$lib/fetch/models/Fetch";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class TaxRepo implements ITaxRepo {
    async saveTax(tax: TaxRatePayload): Promise<DTO<ITax>> {
        try {
            const options = {
                method: "POST",
                body: {
                    title: tax.title,
                    value: tax.value,
                },
            };
            const res = await fetchData<DTO<ITax>>(TAX + "/create", options);
            if (res.success) {
                return getSuccessDTO(res.data || tax as ITax);
            } else {
                console.error("API call failed:", res.message);
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            console.error("Exception in saveTax:", error);
            return getHandledErrorDTO(error.message);
        }
    }
    getTaxByTaxName(tax: string): Promise<ITax | null> {
        throw new Error("Method not implemented.");
    }
    getByEmail(email: string): Promise<DTO<ITax>> {
        throw new Error("Method not implemented.");
    }
    checkEmailExists(email: string): Promise<boolean> {
        throw new Error("Method not implemented.");
    }
    checkTaxNameExists(tax: string): Promise<boolean> {
        throw new Error("Method not implemented.");
    }
    save(object: ITax): Promise<DTO<ITax>> {
        throw new Error("Method not implemented.");
    }
    async delete(id: number): Promise<DTO<boolean>> {
        try {
            const options = {
                method: "DELETE",
            };
            const res = await fetchData<FetchResultAll<boolean>>(TAX + `/delete/${id}`, options);
            if (res.success) {
                return getSuccessDTO(true);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            console.log(error);

            return getHandledErrorDTO(error.message);
        }
    }
    async update(tax: ITax): Promise<DTO<ITax>> {
        try {
            const options = {
                method: "PUT",
                body: {
                    title: tax.title,
                    value: tax.value,
                },
            };

            const res = await fetchData<DTO<ITax>>(TAX + "/update/" + tax.id, options);
            if (res.success) {
                return getSuccessDTO(tax);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            console.log(error);

            return getHandledErrorDTO(error.message);
        }
    }
    async getById(id: number, token?: string): Promise<DTO<ITax>> {
        try {
            const option = token
                ? { headers: { Authorization: `Bearer ${token}`, method: "GET" } }
                : { method: "GET" };
            const res = await fetchData<DTO<ITax>>(TAX + `/get/${id}`, option);
            // console.log(res);

            if (res.success && res.data) {
                return getSuccessDTO(parseTax(res.data));
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getHandledErrorDTO(error.message);
        }
    }
    async getAll(page: number, pageSize: number, searchText?: string): Promise<DTO<PaginatedBaseResponse<ITax>>> {
        try {
            const options = {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            };
            const query = `?page=${page}&pageSize=${pageSize}${searchText ? `&search=${searchText}` : ``}`;
            const res = await fetchData<DTO<PaginatedBaseResponse<ITax>>>(TAX+"/list" + query, options);

            if (res.success && res.data) {
                return getSuccessDTO(res.data);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            console.log(error);
            handleError(error)
            return getHandledErrorDTO(error);
        }
    }

    saveSeriesDocument(item: ITax): Promise<DTO<ITax>> {
        throw new Error("Method not implemented.");
    }

    async searchTax(queryText: string): Promise<DTO<ITax[]>> {
        try {
            const options = {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            };

            const res = await fetchData<DTO<PaginatedBaseResponse<ITax>>>(
                TAX + "/list?page=1&search=" + queryText,
                options
            );

            if (res.success && res.data) {
                const TAX = res.data.data.map((tax: ITax) => {
                    return parseTax(tax);
                });
                return getSuccessDTO(TAX);
            } else {
                return getSuccessDTO([]);
            }
        } catch (error: any) {
            console.log(error);
            handleError(error)
            return getHandledErrorDTO(error);
        }
    }
}
