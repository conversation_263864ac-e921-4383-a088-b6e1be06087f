import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { ITax, TaxRatePayload } from "../models/Tax";

export interface ITaxRepo {
    getById(id: number, token?: string): Promise<DTO<ITax>>;
    saveTax(tax: TaxRatePayload): Promise<DTO<ITax>>;
    getTaxByTaxName(tax: string): Promise<ITax | null>;
    getByEmail(email: string): Promise<DTO<ITax>>;
    checkEmailExists(email: string): Promise<boolean>;
    checkTaxNameExists(tax: string): Promise<boolean>;
    getAll(page: number, pageSize: number, searchText?: string): Promise<DTO<PaginatedBaseResponse<ITax>>>;
    update(tax: ITax): Promise<DTO<ITax>>;
    delete(id: number): Promise<DTO<boolean>>;
    searchTax(queryText: string): Promise<DTO<ITax[]>>;
}
