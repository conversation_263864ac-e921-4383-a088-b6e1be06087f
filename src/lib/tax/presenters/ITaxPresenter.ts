import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { ITax, TaxRatePayload } from "../models/Tax";

export interface ITaxPresenter {
    // add presenter methods as per your requirements
    onSubmitForm(isEditCase: boolean, afterObject: TaxRatePayload): Promise<DTO<ITax>>
    onLoad( page: number, pageSize: number,searchText?:string): Promise<DTO<PaginatedBaseResponse<ITax>>> 
    getById(itemId: number): Promise<DTO<ITax>> 
    onDelete(id: number): Promise<DTO<boolean>> 
    onSearch(searchText: string): Promise<DTO<ITax[]>>
    onValidateTax(tax: ITax): Map<string, string> 
}
