import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import { validateZodSchema } from "$lib/common/utils/common-utils";
import { RepoProvider } from "$lib/RepoProvider";
import type { ITax } from "../models/Tax";
import { TaxSchema } from "../utils/tax-utils";
import type { ITaxPresenter } from "./ITaxPresenter";

export class TaxPresenter implements ITaxPresenter {

    async onSubmitForm(isEditCase: boolean, afterObject: ITax): Promise<DTO<ITax>> {
        if (isEditCase) {
            return await RepoProvider.taxRepo.update(afterObject);
        } else {
            return await RepoProvider.taxRepo.saveTax({
                title: afterObject.title,
                value: afterObject.value,
            });
        }
    }

    async onLoad( page: number, pageSize: number,searchText?:string): Promise<DTO<PaginatedBaseResponse<ITax>>> {
        return await RepoProvider.taxRepo.getAll(page, pageSize,searchText);
    }

    async onDelete(id: number): Promise<DTO<boolean>> {
        return await RepoProvider.taxRepo.delete(id);
    }

    getById(itemId: number): Promise<DTO<ITax>> {
        return RepoProvider.taxRepo.getById(itemId);
    }

    onSearch(searchText: string) {
        return RepoProvider.taxRepo.searchTax(searchText);
    }

    onValidateTax(tax: ITax): Map<string, string> {
        return validateZodSchema(TaxSchema, tax);
    }

}
