import { z } from "zod";
import type { ITax } from "../models/Tax";

const parseTax = (tax: ITax) => {
    return tax;
};

const getEmptyTaxDetails = (): ITax => {
    return {
        id: 0,
        title: "",
        value: 0,
    };
};

const TaxSchema = z.object({
    title: z.string().min(3, "Title cannot be empty min 3 character."), // Title cannot be empty
    value: z.number().gt(0, "Value must be greater than 0."), // Ensures value is strictly greater than 0
});

export { parseTax, getEmptyTaxDetails , TaxSchema};
