<script lang="ts">
    import { Spinner } from "flowbite-svelte";
    import {
        // checkPermission,
        debounce,
        showErrorToast,
        showSuccessToast,
    } from "$lib/common/utils/common-utils";
    import DeleteModel from "$lib/common/components/admin/DeleteModel.svelte";

    import { onMount } from "svelte";

    import { PresenterProvider } from "$lib/PresenterProvider";
    import { type ITax } from "$lib/tax/models/Tax";
    // import Loader from "$lib/common/components/client/base/Loader.svelte";
    // import { permissionStore } from "$lib/common/utils/store";
    // import { TAX_PERMISSIONS } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { goto } from "$app/navigation";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import Loader from "$lib/common/components/Loader.svelte";
    import { TAX } from "$lib/common/configs/serverConfig";

    // export let taxArr: ITax[] = [];
    export let searchTerm: string = "";
    export let editCase: boolean;
    export let formData: ITax;
    export let id: number | null = null;
    // export let getPaginationItems: () => void = () => {};
    // export let currentPageNo = 1;
    export let onAddTax: () => void = () => {};
    export let onRefresh: () => void = () => {};
    let isLoading: boolean = false;
    export let searchLoading: boolean = false;
    export let filteredData: ITax[] = [];
    export let paginationData: PaginatedDataWrapper<ITax>;
    let isSearching: boolean = false;
    let alreadyFetchedData: ITax[] = [];

    let modelOpen: boolean = false;
    let deleteObj: ITax;

    const handleDelete = async () => {
        console.log("changes");
        modelOpen = false;
        isLoading = true;
        const deletingRes = await PresenterProvider.taxPresenter.onDelete(deleteObj.id);

        if (deletingRes.success) {
            showSuccessToast(deletingRes.message ?? "Tax Deleted Successfully!");
            // Call refresh function to update the data
            onRefresh();
        } else {
            showErrorToast(deletingRes.message);
        }
        isLoading = false;
    };

    let debounceSearch = debounce(async (e: any) => {
        const searchValue = e.target.value.trim();

        if (searchValue.length === 0) {
            // Reset to original data when search is cleared
            filteredData = alreadyFetchedData;
            isSearching = false;
            return;
        }

        if (searchValue.length >= 1) {
            searchLoading = true;
            isSearching = true;

            try {
                // First try to filter from existing data
                const localFiltered = alreadyFetchedData.filter((data) =>
                    data.title.toLowerCase().includes(searchValue.toLowerCase())
                );

                if (localFiltered.length > 0) {
                    filteredData = localFiltered;
                    isSearching = false;
                    searchLoading = false;
                } else {
                    // If no local results, search from server
                    const result = await PresenterProvider.taxPresenter.onSearch(searchValue);
                    if (result.success) {
                        filteredData = result.data;
                    } else {
                        showErrorToast(result.message || "Search failed");
                        filteredData = [];
                    }
                    isSearching = false;
                    searchLoading = false;
                }
            } catch (error) {
                console.error("Search error:", error);
                showErrorToast("Search failed. Please try again.");
                filteredData = alreadyFetchedData;
                isSearching = false;
                searchLoading = false;
            }
        }
    }, 300);

    let permission = {
        edit: true,
        delete: true,
        add: true,
    };

    const updateFormData = (item: ITax) => {
        editCase = true;
        id = item.id;
        formData = item;
    };

    onMount(() => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;
    });
</script>

{#if isLoading}
    <div
        class="flex h-[80vh] w-[100%] items-center justify-center rounded-xl bg-gradient-to-br from-orange-50 to-red-100"
    >
        <div class="text-center">
            <Spinner size={"10"} color="red" />
            <p class="mt-4 font-medium text-gray-600">Loading taxes...</p>
        </div>
    </div>
{:else}
    <div class="mb-6 flex items-center justify-between">
        <div>
            <h1 class="mb-1 text-xl font-bold text-gray-900">Tax Management</h1>
            <p class="text-sm text-gray-600">Manage tax rates and configurations</p>
        </div>
    </div>

    <div class="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-lg">
        <!-- Enhanced Header Section -->
        <div class="border-b border-gray-200 bg-cover bg-center p-6">
            <div
                class="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0"
            >
                <!-- Search Section -->
                <div class="relative">
                    <label for="table-search" class="sr-only">Search</label>
                    <div class="relative">
                        <div
                            class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
                        >
                            <svg
                                class="h-4 w-4 text-gray-500"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                ></path>
                            </svg>
                        </div>
                        <input
                            type="text"
                            id="tax-search"
                            bind:value={searchTerm}
                            on:input={debounceSearch}
                            class="block w-80 rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-3 text-sm text-gray-900 shadow-sm transition-all duration-200 focus:border-orange-500 focus:ring-2 focus:ring-orange-500"
                            placeholder="Search taxes..."
                        />
                        {#if searchLoading}
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <Spinner size="4" color="red" />
                            </div>
                        {/if}
                    </div>
                </div>

                <!-- Tax Counter -->
                <div class="flex items-center space-x-3">
                    <div
                        class="flex items-center rounded-lg bg-white/20 px-3 py-2 text-sm text-white backdrop-blur-sm"
                    >
                        <svg
                            class="mr-2 h-4 w-4 text-white"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                            ></path>
                        </svg>
                        {filteredData.length} Tax Rates
                    </div>
                    {#if permission.add}
                        <button
                            class="inline-flex transform items-center rounded-lg bg-white/90 px-3 py-2 text-sm text-gray-800 shadow-lg transition-all duration-200 hover:scale-105 hover:bg-white hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2"
                            on:click={async () => {
                                await goto("/admin/tax/add");
                            }}
                        >
                            <svg
                                class="mr-2 h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M12 4v16m8-8H4"
                                ></path>
                            </svg>
                            Add Tax Rate
                        </button>
                    {/if}
                </div>
            </div>
        </div>

        {#if searchLoading}
            <div class="flex h-[30rem] w-full items-center justify-center bg-gray-50">
                <div class="text-center">
                    <Loader />
                    <p class="mt-4 text-gray-600">Searching...</p>
                </div>
            </div>
        {:else}
            <!-- Enhanced Table -->
            <div class="max-h-[50vh] overflow-y-auto overflow-x-auto">
                <table class="w-full text-sm text-gray-600">
                    <thead
                        class="border-b border-gray-300 bg-gradient-to-r from-gray-100 to-gray-200"
                    >
                        <tr>
                            <th
                                scope="col"
                                class="w-16 px-4 py-3 text-left font-semibold uppercase tracking-wider text-gray-800"
                            >
                                <div class="flex items-center space-x-1 text-xs">
                                    <span>#</span>
                                </div>
                            </th>
                            <th
                                scope="col"
                                class="w-20 px-4 py-3 text-left font-semibold uppercase tracking-wider text-gray-800"
                            >
                                <div class="flex items-center space-x-1 text-xs">
                                    <span class="text-center w-full">ID</span>
                                </div>
                            </th>
                            <th
                                scope="col"
                                class="w-64 px-4 py-3 text-left font-semibold uppercase tracking-wider text-gray-800"
                            >
                                <div class="flex items-center space-x-1 text-xs">
                                    <span>Title</span>
                                </div>
                            </th>
                            <th
                                scope="col"
                                class="w-24 px-4 py-3 text-center font-semibold uppercase tracking-wider text-gray-800"
                            >
                                <div class="flex items-center space-x-1 text-xs w-full">
                                    <span class="text-center w-full">Value (%)</span>
                                </div>
                            </th>
                            {#if permission.delete}
                                <th
                                    scope="col"
                                    class="w-16 px-4 py-3 text-left font-semibold uppercase tracking-wider text-gray-800"
                                >
                                    <div class="flex items-center space-x-1 text-xs">
                                        <span>Action</span>
                                    </div>
                                </th>
                            {/if}
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {#each filteredData as row, index}
                            <tr
                                class="group bg-white transition-all duration-200 hover:bg-gradient-to-r hover:from-orange-50 hover:to-red-50"
                            >
                                <td class="px-4 py-3">
                                    <a
                                        href={`/admin/tax/edit/${row.id}`}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {(paginationData.pagination.currentPage - 1) *
                                            paginationData.pageSize +
                                            index +
                                            1}
                                    </a>
                                </td>
                                <td class="px-4 py-3">
                                    <button
                                        class="block h-full w-full font-medium transition-colors duration-200 group-hover:text-orange-600"
                                        on:click={() => {
                                            updateFormData(row);
                                            // permission.edit
                                            // ?
                                            goto(`/admin/tax/edit/${row.id}`);
                                            // : "";
                                        }}
                                    >
                                        <span
                                            class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs text-start font-medium text-green-800"
                                        >
                                            TX-{row.id}
                                        </span>
                                    </button>
                                </td>
                                <td class="px-4 py-3">
                                    <button
                                        class="block h-full text-left w-full font-medium text-gray-700 transition-colors duration-200 group-hover:text-orange-600"
                                        on:click={() => {
                                            updateFormData(row);
                                            // permission.edit
                                            // ?
                                            goto(`/admin/tax/edit/${row.id}`);
                                            // : "";
                                        }}
                                    >
                                        {row.title}
                                    </button>
                                </td>
                                <td class="px-4 py-3">
                                    <button
                                        class="block h-full w-full font-medium text-gray-700 transition-colors duration-200 group-hover:text-orange-600"
                                        on:click={() => {
                                            updateFormData(row);
                                            permission.edit
                                                ? goto(`/admin/tax/edit/${row.id}`)
                                                : "";
                                        }}
                                    >
                                        <span
                                            class="inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800"
                                        >
                                            {row.value}%
                                        </span>
                                    </button>
                                </td>

                                {#if permission.delete}
                                    <td class="px-4 py-3">
                                        <!-- svelte-ignore a11y_consider_explicit_label -->
                                        <button
                                            on:click={() => {
                                                deleteObj = row;
                                                modelOpen = true;
                                            }}
                                            class="inline-flex h-6 w-6 items-center justify-center rounded-full text-red-600 transition-all duration-200 hover:bg-red-100 hover:text-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                            title="Delete tax"
                                        >
                                            <svg
                                                class="h-3 w-3"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                                ></path>
                                            </svg>
                                        </button>
                                    </td>
                                {/if}
                            </tr>
                        {/each}
                        {#if filteredData.length === 0}
                            <tr>
                                <td colspan="5" class="h-[40vh] text-center">
                                    <div
                                        class="flex flex-col items-center justify-center space-y-4"
                                    >
                                        <div
                                            class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                                        >
                                            <svg
                                                class="h-8 w-8 text-gray-400"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                                                ></path>
                                            </svg>
                                        </div>
                                        <div class="text-center">
                                            <h3 class="mb-2 text-lg font-medium text-gray-900">
                                                No tax rates found
                                            </h3>
                                            <p class="mb-4 text-gray-500">
                                                Get started by creating your tax rate.
                                            </p>
                                            {#if permission.add}
                                                <button
                                                    class="inline-flex items-center rounded-lg bg-orange-600 px-4 py-2 font-medium text-white shadow-lg transition-all duration-200 hover:bg-orange-700 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                                                    on:click={onAddTax}
                                                >
                                                    <svg
                                                        class="mr-2 h-4 w-4"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 4v16m8-8H4"
                                                        ></path>
                                                    </svg>
                                                    Add Tax Rate
                                                </button>
                                            {/if}
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>
        {/if}
    </div>
{/if}

{#if modelOpen && deleteObj}
    <DeleteModel bind:modelOpen title="Tax" {handleDelete} />
{/if}
