<script lang="ts">
    import { Heading, Input, Label, Select } from "flowbite-svelte";
    // import type { IBreadCrumbData } from "$lib/common/models/breadCrumbData";
    import {
        // getArrayFromEnum,
        showErrorToast,
        showSuccessToast,
        toTitleCase,
    } from "$lib/common/utils/common-utils";
    // import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    // import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import { getEmptyTaxDetails } from "$lib/tax/utils/tax-utils";

    import type { ITax } from "$lib/tax/models/Tax";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { goto } from "$app/navigation";
    import NumberInput from "$lib/common/components/NumberInput.svelte";
    // import { getEmptyRegion } from "$lib/region/utils/region-utils";
    // import NumberInput from "$lib/common/components/client/elements/NumberInput.svelte";

    export let editCase: boolean = false;
    export let formData: ITax = getEmptyTaxDetails();
    export let setAllData = async () => {};
    // export let id: number | null = null;

    let isLoading: boolean = false;
    let errorMap = new Map();

    let taxOptionsArray: any = [
        {
            computed: "",
        },
    ];

    const handleSubmit = async () => {
        isLoading = true;
        // console.log(formData);
        errorMap = PresenterProvider.taxPresenter.onValidateTax(formData);
        if (errorMap.size === 0) {
            let optionArr: {
                label: string;
                value: string;
            }[] = [];
            if (taxOptionsArray) {
                const options = taxOptionsArray;
                options.map((option: any) => {
                    optionArr.push({
                        label: option.computed,
                        value: option.computed,
                    }); 
                });
            }
            const res = await PresenterProvider.taxPresenter.onSubmitForm(editCase, formData);
            if (res.success) {
                showSuccessToast(`Tax ${editCase ? "updated" : "created"} successfully`);
                goto("/admin/tax");
            } else {
                showErrorToast(res.message);
            }

            // await setAllData();
            // formData = getEmptyTaxDetails();
        }
        editCase = false;

        isLoading = false;
    };
</script>

<div class="bg-gradient-to-br from-gray-50 to-blue-50 py-6">
    
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <div class="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
            {#if editCase}
                            <!-- svelte-ignore a11y_consider_explicit_label -->
                            <button
                                type="button"
                                class="inline-flex float-right  items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
                                on:click={() => {
                                    goto("/admin/tax");
                                    formData = getEmptyTaxDetails();
                                    editCase = false;
                                }}
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                
                            </button>
                        {/if}
            <div class="p-8">
                <div class="space-y-8">
                    <!-- Header Section -->
                    <div class="flex items-center  justify-between border-b border-gray-200 pb-6">
                        <div class="flex items-center space-x-4">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900">{editCase ? "Edit Tax Rate" : "Add Tax Rate"}</h3>
                                <p class="text-gray-600 text-sm mt-1">Configure tax rate details and percentages</p>
                            </div>
                        </div>
                        
                        
                    </div>

                    <!-- Tax Information Section -->
                    <div class="space-y-6">
                        <div class="flex items-center space-x-3 pb-4 border-b border-gray-200">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Tax Details</h3>
                        </div>
                        
                        <div class="grid grid-cols-1  gap-6">
                            <!-- Title Field -->
                            <div class="group">
                                <Label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                    <div class="flex items-center space-x-2">
                                        <span>Tax Title</span>
                                        <span class="text-red-500">*</span>
                                    </div>
                                </Label>
                                <div class="relative">
                                    <Input
                                        type="text"
                                        id="title"
                                        placeholder="Enter tax title (e.g., GST, VAT, Sales Tax)"
                                        class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200 group-hover:shadow-md {errorMap.has('title') ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}"
                                        bind:value={formData.title}
                                        on:input={() => {
                                            formData.title = toTitleCase(formData.title);
                                            errorMap = new Map();
                                        }}
                                        on:keypress={(e) => {
                                            if (e.key === "Enter") {
                                                handleSubmit();
                                            }
                                        }}
                                    />
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <svg class="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                        </svg>
                                    </div>
                                </div>
                                {#if errorMap.has("title")}
                                    <p class="mt-2 text-sm text-red-600 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        {errorMap.get("title")}
                                    </p>
                                {/if}
                            </div>

                            <!-- Tax Value Field -->
                            <div class="group">
                                <Label for="taxValue" class="block text-sm font-medium text-gray-700 mb-2">
                                    <div class="flex items-center space-x-2">
                                        <span>Tax Percentage (%)</span>
                                        <span class="text-red-500">*</span>
                                    </div>
                                </Label>
                                <div class="relative">
                                    <NumberInput
                                        id="taxValue"
                                        bind:value={formData.value}
                                        customCss={`block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200 group-hover:shadow-md ${errorMap.has('value') ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                                        on:input={() => {
                                            errorMap = new Map();
                                        }}
                                        onEnter={handleSubmit}
                                    />
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <span class="text-gray-500 text-sm font-medium">%</span>
                                    </div>
                                </div>
                                {#if errorMap.has("value")}
                                    <p class="mt-2 text-sm text-red-600 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        {errorMap.get("value")}
                                    </p>
                                {/if}
                            </div>
                        </div>

                       
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex  items-center justify-end space-x-4 pt-8 border-t border-gray-200">
                        <CustomButton
                            onClick={() => {
                                formData = getEmptyTaxDetails();
                                editCase = false;
                                goto("/admin/tax");
                            }}
                            cssClass="px-1 py-3 bg-red-500 hover:bg-red-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 transform hover:scale-105"
                            title="Cancel"
                        />
                        <CustomButton
                            {isLoading}
                            onClick={handleSubmit}
                            cssClass="px-1 py-3 bg-[#455050] hover:bg-[#3a4444] text-white font-medium rounded-lg shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#455050] transition-all duration-200 transform hover:scale-105"
                            title={editCase ? "Update" : "Save"}
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
