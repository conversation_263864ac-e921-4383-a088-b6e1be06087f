import type { IUserPresenter } from "$lib/users/presenters/IUserPresenter";
import { UserPresenter } from "$lib/users/presenters/UserPresenter";
import type { IDashboardPresenter } from "$lib/dashboard/presenters/IDashboardPresenter";
import { DashboardPresenter } from "$lib/dashboard/presenters/DashboardPresenter";
import type { IOperationalLogsPresenter } from "./operational-logs/presenters/IOperationalLogsPresenter";
import { OperationalLogsPresenter } from "./operational-logs/presenters/OperationalLogsPresenter";
import type { ISupplierPresenter } from "./supplier/presenter/ISupplierPresenter";
import { SupplierPresenter } from "./supplier/presenter/SupplierPresenter";
import type { IStorageLocationPresenter } from "./storage_locations/presenter/IStorageLocationPresenter";
import { StorageLocationPresenter } from "./storage_locations/presenter/StorageLocationPresenter";
import type { IFactoryGatesPresenter } from "./factory_gates/presenter/IFactoryGatePresenter";
import { FactoryGatesPresenter } from "./factory_gates/presenter/FactoryGatePresenter";
import type { IItemCategoryPresenter } from "./item_category/presenter/IItemCategoryPresenter";
import { ItemCategoryPresenter } from "./item_category/presenter/ItemCategoryPresenter";
import type { IItemUnitPresenter } from "./item_unit/presenter/IItemUnitPresenter";
import { ItemUnitPresenter } from "./item_unit/presenter/ItemUnitPresenter";
import type { IRawMaterialPresenter } from "./raw_material/presenter/IRawMaterialPresenter";
import type { IFinalGoodsPresenter } from "./final_goods/presenter/IFinalGoodsPresenter";
import type { IBillOfMaterialPresenter } from "./bill_of_materials/presenter/IBillOfMaterialPresenter";

import { RawMaterialPresenter } from "./raw_material/presenter/RawMaterialPresenter";
import { FinalGoodsPresenter } from "./final_goods/presenter/FinalGoodsPresenter";
import { BillOfMaterialPresenter } from "./bill_of_materials/presenter/BillOfMaterialPresenter";
import { S3Uploader } from "./common/UploaderComponent/presenter/S3Uploader";
import type { IUploader } from "./common/UploaderComponent/presenter/IUploader";
import type { IRawMaterialStockPresenter } from "./raw_materia_stock/presenter/IRawMaterialStockPresenter";
import { RawMaterialStockPresenter } from "./raw_materia_stock/presenter/RawMaterialStockPresenter";
import type { IPurchaseInvoicePresenter } from "./purchase_invoice/presenter/IPurchaseInvoicePresenter";
import { PurchaseInvoicePresenter } from "./purchase_invoice/presenter/PurchaseInvoicePresenter";
import type { IDebitNotePresenter } from "./debit-notes/presenter/IDebitNotePresenter";
import { DebitNotePresenter } from "./debit-notes/presenter/DebitNotePresenter";
import type { IUserRolePresenter } from "./user_role/presenter/IUserRolePresenter";
import { UserRolePresenter } from "./user_role/presenter/UserRolePresenter";
import type { IOpeningStockPresenter } from "./opening-stock/presenter/IOpeningStockPresenter";
import { OpeningStockPresenter } from "./opening-stock/presenter/OpeningStockPresenter";
import type { IPurchaseOrderPresenter } from "./purchase-order/presenter/IPurchaseOrderPresenter";
import { PurchaseOrderPresenter } from "./purchase-order/presenter/PurchaseOrderPresenter";
import type { IExcelUploadPresenter } from "./excel_upload/presenter/IExcelUploadPresenter";
import { ExcelUploadPresenter } from "./excel_upload/presenter/ExcelUploadPresenter";
import type { IItemAttributesPresenter } from "./item_attributes/presenter/IItemAttributesPresenter";
import { ItemAttributesPresenter } from "./item_attributes/presenter/ItemAttributesPresenter";
import type { ITaxPresenter } from "./tax/presenters/ITaxPresenter";
import { TaxPresenter } from "./tax/presenters/TaxPresenter";
import type { IImageUploadPresenter } from "./imageUpload/presenter/IImageUploadPresenter";
import { ImageUploadPresenter } from "./imageUpload/presenter/imageUploadPresenter";

export class PresenterProvider {
    private static _userPresenter: IUserPresenter;
    private static _dashboardPresenter: IDashboardPresenter;
    private static _operationalLogsPresenter: IOperationalLogsPresenter;
    private static _supplierPresenter: ISupplierPresenter;
    private static _storageLocationPresenter: IStorageLocationPresenter;
    private static _factoryGatesPresenter: IFactoryGatesPresenter;
    private static _itemCategoryPresenter: IItemCategoryPresenter;
    private static _itemUnitPresenter: IItemUnitPresenter;
    private static _rawMaterialPresenter: IRawMaterialPresenter;
    private static _finalGoodsPresenter: IFinalGoodsPresenter;
    private static _billOfMaterialPresenter: IBillOfMaterialPresenter;
    private static _rawMaterialStockPresenter: IRawMaterialStockPresenter;
    private static _purchaseInvoicePresenter: IPurchaseInvoicePresenter;
    private static _debitNotePresenter: IDebitNotePresenter;
    private static _userRolePresenter: IUserRolePresenter;
    private static _openingStockPresenter: IOpeningStockPresenter;
    private static _purchaseOrderPresenter: IPurchaseOrderPresenter;
    private static _excelUploadPresenter: IExcelUploadPresenter;
    private static _taxPresenter: ITaxPresenter;
    private static _itemAttributesPresenter: IItemAttributesPresenter;    
    private static _imageUploadPresenter: IImageUploadPresenter;
    private static _s3Uploader:IUploader;

    static get purchaseOrderPresenter(): IPurchaseOrderPresenter {
        if (!this._purchaseOrderPresenter) this._purchaseOrderPresenter = new PurchaseOrderPresenter();
        return this._purchaseOrderPresenter;
    }

    static get userPresenter(): IUserPresenter {
        if (!this._userPresenter) this._userPresenter = new UserPresenter();
        return this._userPresenter;
    }



    static get operationalLogsPresenter(): IOperationalLogsPresenter {
        if (!this._operationalLogsPresenter) this._operationalLogsPresenter = new OperationalLogsPresenter();
        return this._operationalLogsPresenter;
    }


    static get dashboardPresenter(): IDashboardPresenter {
        if (!this._dashboardPresenter) this._dashboardPresenter = new DashboardPresenter();
        return this._dashboardPresenter;
    }


    static get supplierPresenter(): ISupplierPresenter {
        if (!this._supplierPresenter) this._supplierPresenter = new SupplierPresenter();
        return this._supplierPresenter;
    }


    static get storageLocationPresenter(): IStorageLocationPresenter {
        if (!this._storageLocationPresenter) this._storageLocationPresenter = new StorageLocationPresenter();
        return this._storageLocationPresenter;
    }

    static get factoryGatesPresenter(): IFactoryGatesPresenter {
        if (!this._factoryGatesPresenter) this._factoryGatesPresenter = new FactoryGatesPresenter();
        return this._factoryGatesPresenter;
    }

    static get itemCategoryPresenter(): IItemCategoryPresenter {
        if (!this._itemCategoryPresenter) this._itemCategoryPresenter = new ItemCategoryPresenter();
        return this._itemCategoryPresenter;
    }

    static get itemUnitPresenter(): IItemUnitPresenter {
        if (!this._itemUnitPresenter) this._itemUnitPresenter = new ItemUnitPresenter();
        return this._itemUnitPresenter;
    }

    static get rawMaterialPresenter(): IRawMaterialPresenter {
        if (!this._rawMaterialPresenter) this._rawMaterialPresenter = new RawMaterialPresenter();
        return this._rawMaterialPresenter;
    }
    static get finalGoodsPresenter(): IFinalGoodsPresenter {
        if (!this._finalGoodsPresenter) this._finalGoodsPresenter = new FinalGoodsPresenter();
        return this._finalGoodsPresenter;
    }

    static get rawMaterialStockPresenter(): IRawMaterialStockPresenter {
        if (!this._rawMaterialStockPresenter) this._rawMaterialStockPresenter = new RawMaterialStockPresenter();
        return this._rawMaterialStockPresenter;
    }

    static get purchaseInvoicePresenter(): IPurchaseInvoicePresenter {
        if (!this._purchaseInvoicePresenter) this._purchaseInvoicePresenter = new PurchaseInvoicePresenter();
        return this._purchaseInvoicePresenter;
    }

    static get debitNotePresenter(): IDebitNotePresenter {
        if (!this._debitNotePresenter) this._debitNotePresenter = new DebitNotePresenter();
        return this._debitNotePresenter;
    }
    static get userRolePresenter(): IUserRolePresenter {
        if (!this._userRolePresenter) this._userRolePresenter = new UserRolePresenter();
        return this._userRolePresenter;
    }
    static get openingStockPresenter(): IOpeningStockPresenter {
        if (!this._openingStockPresenter) this._openingStockPresenter = new OpeningStockPresenter();
        return this._openingStockPresenter;
    }

    static get excelUploadPresenter(): IExcelUploadPresenter {
        if (!this._excelUploadPresenter) this._excelUploadPresenter = new ExcelUploadPresenter();
        return this._excelUploadPresenter;
    }

    static get taxPresenter(): ITaxPresenter {
        if (!this._taxPresenter) this._taxPresenter = new TaxPresenter();
        return this._taxPresenter;
    }

    static get itemAttributesPresenter(): IItemAttributesPresenter {
        if (!this._itemAttributesPresenter) this._itemAttributesPresenter = new ItemAttributesPresenter();
        return this._itemAttributesPresenter;
    }

    static get imageUploadPresenter(): IImageUploadPresenter {
        if (!this._imageUploadPresenter) this._imageUploadPresenter = new ImageUploadPresenter();
        return this._imageUploadPresenter;
    }

    static get S3Uploader(): IUploader {
        if (!this._s3Uploader) this._s3Uploader = new S3Uploader();
        return this._s3Uploader;
    }

    static get billOfMaterialPresenter(): IBillOfMaterialPresenter {
        if (!this._billOfMaterialPresenter) this._billOfMaterialPresenter = new BillOfMaterialPresenter();
        return this._billOfMaterialPresenter;
    }


}
