import { LOGS_API_PATH } from '$lib/common/configs/serverConfig';
import { fetchData } from '$lib/fetch/utils/fetch-utils';
import { safeJsonParse, parseApiLogData } from '$lib/common/utils/common-utils';
import type { 
    AvailableLogDatesResponse, 
    PaginatedLogsByDateResponse,
    LogStatisticsResponse,
    LogFilters,
    IMorganLog
} from '$lib/types/logs';

export class LogsApiService {
    
    /**
     * Fetches all available dates that have log data
     * @returns Promise<AvailableLogDatesResponse>
     * @throws Error if the request fails
     */
    static async getAvailableDates(): Promise<AvailableLogDatesResponse> {
        try {
            const result = await fetchData<AvailableLogDatesResponse>(`${LOGS_API_PATH}/dates`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Failed to fetch available dates';
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('This endpoint is only available in production environment');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error('Logs directory not found');
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred');
                }
                throw new Error(errorMessage);
            }

            return result;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the logs server');
        }
    }

    /**
     * Fetches paginated log entries for a specific date with filtering
     * Uses the primary paginated endpoint with robust error handling
     * @param date - Date string in YYYY-MM-DD format
     * @param page - Page number (default: 1, min: 1)
     * @param pageSize - Number of logs per page (default: 50, min: 1, max: 1000)
     * @param filters - Optional filters for server-side filtering
     * @returns Promise<PaginatedLogsByDateResponse>
     * @throws Error if the request fails or parameters are invalid
     */
    static async getPaginatedLogsByDate(
        date: string, 
        page: number = 1, 
        pageSize: number = 50, 
        filters?: LogFilters
    ): Promise<PaginatedLogsByDateResponse> {
        // Validate date format
        if (!this.isValidDateFormat(date)) {
            throw new Error('Invalid date format. Use YYYY-MM-DD');
        }

        // Validate pagination parameters
        if (!Number.isInteger(page) || page < 1) {
            throw new Error('Page must be a positive integer starting from 1');
        }
        if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 1000) {
            throw new Error('Page size must be an integer between 1 and 1000');
        }

        try {
            // Build query parameters - using the primary paginated endpoint
            const params = new URLSearchParams({
                date,
                page: page.toString(),
                pageSize: pageSize.toString(),
            });

            // Add server-side filter parameters if provided
            if (filters) {
                if (filters.method && filters.method.trim()) {
                    params.append('method', filters.method.toUpperCase());
                }
                if (filters.statusCode && !isNaN(filters.statusCode)) {
                    params.append('statusCode', filters.statusCode.toString());
                }
                if (filters.hasError !== undefined) {
                    params.append('hasError', filters.hasError.toString());
                }
                if (filters.url && filters.url.trim()) {
                    params.append('url', filters.url.trim());
                }
                if (filters.ip && filters.ip.trim()) {
                    params.append('ip', filters.ip.trim());
                }
            }

            console.debug(`[LogsAPI] Fetching paginated logs: ${LOGS_API_PATH}/by-date?${params.toString()}`);

            const result = await fetchData<PaginatedLogsByDateResponse>(
                `${LOGS_API_PATH}/by-date?${params.toString()}`, 
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );

            if (!result.success) {
                const errorMessage = result.message || 'Failed to fetch logs';
                
                // Enhanced error handling for different scenarios
                if (errorMessage.includes('400') || errorMessage.includes('Bad Request')) {
                    throw new Error('Invalid parameters. Check date format and filter values.');
                }
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('Access denied. Logging endpoint requires production environment.');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error(`No log files found for date: ${date}. Check if logs exist for this date.`);
                }
                if (errorMessage.includes('422') || errorMessage.includes('Unprocessable')) {
                    throw new Error('Invalid filter values. Check method, status code, or other parameters.');
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error. The server encountered an issue processing your request.');
                }
                throw new Error(errorMessage);
            }

            // Validate response structure
            if (!result.data) {
                throw new Error('Invalid response: missing data field');
            }

            if (!result.data.pagination) {
                throw new Error('Invalid response: missing pagination information');
            }

            // Parse JSON strings in the logs if needed (handles stringified request/response data)
            if (result.data.logs && Array.isArray(result.data.logs)) {
                result.data.logs = result.data.logs.map((log: IMorganLog) => parseApiLogData(log));
            }

            console.debug(`[LogsAPI] Successfully fetched ${result.data.logs?.length || 0} logs, page ${page}/${result.data.pagination.totalPages}`);

            return result;
        } catch (error) {
            if (error instanceof Error) {
                // Re-throw our custom error messages
                throw error;
            }
            // Network or unknown errors
            throw new Error('Network error: Unable to connect to the logs server. Check your connection and try again.');
        }
    }



    /**
     * Fetches comprehensive statistics for logs on a specific date
     * @param date - Date string in YYYY-MM-DD format
     * @returns Promise<LogStatisticsResponse>
     * @throws Error if the request fails or date is invalid
     */
    static async getStatsByDate(date: string): Promise<LogStatisticsResponse> {
        // Validate date format
        if (!this.isValidDateFormat(date)) {
            throw new Error('Invalid date format. Use YYYY-MM-DD');
        }

        try {
            const result = await fetchData<LogStatisticsResponse>(`${LOGS_API_PATH}/stats-by-date?date=${encodeURIComponent(date)}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Failed to fetch statistics';
                if (errorMessage.includes('400') || errorMessage.includes('Bad Request')) {
                    throw new Error('Invalid date format or missing date parameter');
                }
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('This endpoint is only available in production environment');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error(`No log files found for date: ${date}`);
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred');
                }
                throw new Error(errorMessage);
            }

            return result;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the logs server');
        }
    }

    /**
     * Fetches both paginated logs and statistics for a date simultaneously
     * @param date - Date string in YYYY-MM-DD format
     * @param page - Page number (default: 1)
     * @param pageSize - Number of logs per page (default: 50)
     * @param filters - Optional filters for the logs
     * @returns Promise<{logs: PaginatedLogsByDateResponse, stats: LogStatisticsResponse}>
     */
    static async getPaginatedLogsAndStatsByDate(
        date: string, 
        page: number = 1, 
        pageSize: number = 50, 
        filters?: LogFilters
    ): Promise<{
        logs: PaginatedLogsByDateResponse;
        stats: LogStatisticsResponse;
    }> {
        try {
            const [logs, stats] = await Promise.all([
                this.getPaginatedLogsByDate(date, page, pageSize, filters),
                this.getStatsByDate(date)
            ]);

            return { logs, stats };
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Failed to fetch logs and statistics');
        }
    }

    /**
     * Fetches both logs and statistics for a date simultaneously (legacy method)
     * @param date - Date string in YYYY-MM-DD format
     * @returns Promise<{logs: LogsByDateResponse, stats: LogStatisticsResponse}>
     */


    /**
     * Validates date format (YYYY-MM-DD)
     * @param date - Date string to validate
     * @returns boolean - true if valid format
     */
    private static isValidDateFormat(date: string): boolean {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) {
            return false;
        }

        // Check if the date is actually valid
        const parsedDate = new Date(date);
        const [year, month, day] = date.split('-').map(Number);
        
        return (
            parsedDate.getFullYear() === year &&
            parsedDate.getMonth() === month - 1 &&
            parsedDate.getDate() === day
        );
    }

    /**
     * Checks if the logs API is available
     * @returns Promise<boolean>
     */
    static async isLogsApiAvailable(): Promise<boolean> {
        try {
            await this.getAvailableDates();
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Gets today's date in YYYY-MM-DD format
     * @returns string - Today's date
     */
    static getTodayDate(): string {
        return new Date().toISOString().split('T')[0];
    }

    /**
     * Gets yesterday's date in YYYY-MM-DD format
     * @returns string - Yesterday's date
     */
    static getYesterdayDate(): string {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return yesterday.toISOString().split('T')[0];
    }

    /**
     * Flushes the logging buffer to immediately process pending log entries
     * @returns Promise<FlushLogsResponse>
     * @throws Error if the request fails
     */
    static async flushLogs(): Promise<FlushLogsResponse> {
        try {
            const result = await fetchData<FlushLogsResponse>(`${LOGS_API_PATH.replace('/logs', '')}/health/logging/flush`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Failed to flush logs';
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('Insufficient permissions to flush logs');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error('Flush logs endpoint not found');
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred while flushing logs');
                }
                throw new Error(errorMessage);
            }

            return result;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the flush logs endpoint');
        }
    }

    /**
     * Fetches logging health status and metrics
     * @returns Promise<LoggingHealth>
     * @throws Error if the request fails
     */
    static async getLoggingHealth(): Promise<LoggingHealth> {
        try {
            const result = await fetchData<any>(`${LOGS_API_PATH.replace('/logs', '')}/health/logging`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Failed to fetch logging health';
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('Insufficient permissions to access logging health');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error('Logging health endpoint not found');
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred while fetching logging health');
                }
                throw new Error(errorMessage);
            }

            // Transform the response to match our interface
            return {
                success: result.success,
                data: result.data
            };
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the logging health endpoint');
        }
    }

    /**
     * Resets logging metrics
     * @returns Promise<ResetMetricsResponse>
     * @throws Error if the request fails
     */
    static async resetLoggingMetrics(): Promise<ResetMetricsResponse> {
        try {
            const result = await fetchData<ResetMetricsResponse>(`${LOGS_API_PATH.replace('/logs', '')}/health/logging/reset`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Failed to reset logging metrics';
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('Insufficient permissions to reset logging metrics');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error('Reset metrics endpoint not found');
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred while resetting metrics');
                }
                throw new Error(errorMessage);
            }

            return result;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the reset metrics endpoint');
        }
    }

    /**
     * Fetches current logging configuration
     * @returns Promise<LoggingConfigResponse>
     * @throws Error if the request fails
     */
    static async getLoggingConfig(): Promise<LoggingConfigResponse> {
        try {
            const result = await fetchData<LoggingConfigResponse>(`${LOGS_API_PATH.replace('/logs', '')}/health/logging/config`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Failed to fetch logging configuration';
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('Insufficient permissions to access logging configuration');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error('Logging configuration endpoint not found');
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred while fetching configuration');
                }
                throw new Error(errorMessage);
            }

            return result;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the logging configuration endpoint');
        }
    }
}

// Flush logs response interface
export interface FlushLogsResponse {
    success: boolean;
    message: string;
    data?: {
        flushTimestamp: string;
        metricsBefore?: {
            totalLogsWritten: number;
            bufferFlushes: number;
        };
        metricsAfter?: {
            totalLogsWritten: number;
            bufferFlushes: number;
        };
        logsProcessed: number;
        flushesPerformed: number;
    };
    error?: string;
    timestamp?: string;
}

// NOTE: LoggingHealthResponse interface removed - use LoggingHealth instead
// This enforces the correct API contract with only totalSizeMB field

// Reset metrics response interface
export interface ResetMetricsResponse {
    success: boolean;
    message: string;
    data?: {
        previousMetrics: {
            totalLogsWritten: number;
            totalLogErrors: number;
            bufferFlushes: number;
            averageFlushTime: number;
            errorRate: number;
        };
        newMetrics: {
            totalLogsWritten: number;
            totalLogErrors: number;
            bufferFlushes: number;
            averageFlushTime: number;
            errorRate: number;
        };
        resetTimestamp: string;
    };
    error?: string;
}

// Logging configuration response interface
export interface LoggingConfigResponse {
    success: boolean;
    message: string;
    data?: {
        bufferSize: number;
        flushInterval: number;
        maxFileSize: string;
        retentionDays: number;
        logLevel: string;
        enabledFeatures: string[];
        outputPath: string;
    };
    error?: string;
}

export interface LogFileStats {
	totalFiles: number;
	totalSizeMB: string; // Always in MB format (e.g., "0.19")
	files: Array<{
		name: string;
		sizeMB: string; // Format: "0.02"
		modified: string; // ISO date string
	}>;
}

export interface LoggingHealth {
	success: boolean;
	data: {
		status: string;
		metrics: {
			totalLogsWritten: number;
			totalLogErrors: number;
			bufferFlushes: number;
			averageFlushTime: number;
			errorRate: number;
			uptime: number;
		};
		fileStats: LogFileStats;
		isEnabled: boolean;
		timestamp: string; // ISO date string
	};
} 