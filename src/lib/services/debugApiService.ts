import { RAW_MATERIAL_STOCK_API_PATH } from '$lib/common/configs/serverConfig';
import { fetchData } from '$lib/fetch/utils/fetch-utils';
import type { ProblematicStocksResponse } from '$lib/types/debug';

export class DebugApiService {
    
    /**
     * Fetches problematic stocks from the debug endpoint
     * @returns Promise<ProblematicStocksResponse>
     * @throws Error if the request fails or if not in development environment
     */
    static async getProblematicStocks(): Promise<ProblematicStocksResponse> {
        try {
            const result = await fetchData<ProblematicStocksResponse>(`${RAW_MATERIAL_STOCK_API_PATH}/debug/problematic-stocks`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Unknown error occurred';
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('This feature is only available in development environment');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error('Debug endpoint not found. Make sure the backend service is running.');
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Server error occurred. Please try again later.');
                }
                throw new Error(errorMessage);
            }

            return {
                success: result.success,
                message: result.message,
                data: result.data
            };
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the server');
        }
    }

    /**
     * Checks if the debug endpoint is available
     * @returns Promise<boolean>
     */
    static async isDebugEndpointAvailable(): Promise<boolean> {
        try {
            await this.getProblematicStocks();
            return true;
        } catch (error) {
            return false;
        }
    }
} 