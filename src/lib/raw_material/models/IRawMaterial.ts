import type { BaseModel, DeletedMetaData, UpdatedMetaData } from "$lib/common/models/base_model";
import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
import type { IItemAttribute, IItemAttributeValue } from "$lib/item_attributes/models/IItemAttribute";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import type { ISupplier } from "$lib/supplier/models/ISupplier";
import { z } from "zod";

interface IRawMaterialAddFormState {
    rawMaterial: {
        category: IItemCategory | null;
        unit: IItemUnit | null;
        name: string;
        hsn: string;
        gstPercentage: number;
    },
    variations: IRawMaterialVariationAddState[];
}


interface IRawMaterialAddRequest {
    rawMaterial: {
        categoryId: number;
        unitId: number;
        name: string;
        hsn: string;
        gstPercentage: number;
    },
    variations: IRawMaterialVariationAddRequest[];
}

interface IRawMaterialVariationAddRequest {
    name: string;
    sku: string;
    msq: number;
    moq: number;
    attributes: {
        attributeValueId: number;
    }[];
    priceData: {
        supplierId: number;
        price: number;
    }[];
}

interface IRawMaterialVariationAddState {
    name: string;
    sku: string;
    msq: number;
    moq: number;
    attributes: {
        attributeValueId: number;
    }[];
    priceData: {
        supplier: ISupplier | null;
        price: number;
    }[];
}


interface IRawMaterialCreatePaylod {
    name: string;
    unitId: number;
    categoryId: number;
    hsn: string;
    gstPercentage: number;
    variations: ICreateVariation[];
}


interface IRawMaterialUpdatePaylod extends IRawMaterialCreatePaylod {
    id: number;
    status: RAW_MATERIAL_STAUS;
}
interface ICreateVariation {
    enabled: boolean;
    hash: string;
    name: string;
    sku: string;
    msq: number;
    moq: number;
    priceData: {
        price: number;
        supplierId: number;
    }[];
}

interface ICreateRawMaterial {
    name: string;
    unitId: number;
    categoryId: number;
    hsn: string;
    gstPercentage: number;
    status: RAW_MATERIAL_STAUS;
}

enum RAW_MATERIAL_STAUS {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    DELETED = 'deleted'
}

interface IRawMaterialPriceData {
    price: number;
    supplier: ISupplier | null;
}

interface IRawMaterialVariation {
    id: number;
    name: string;
    unit: IItemUnit;
    category: IItemCategory;
    sku: string;
    msq: number;
    hsn: string;
    gstPercentage: number;
    priceData: {
        supplier: ISupplier;
        price: number;
    }[];
}

interface ICreateRawMaterialOld extends BaseModel, DeletedMetaData, UpdatedMetaData {
    name: string;
    unitId: number;
    categoryId: number;
    sku: string;
    msq: number;
    hsn: string;
    gstPercentage: number;
    status: RAW_MATERIAL_STAUS;
}

interface IRawMaterialOverview {
    id: number;
    name: string;
    hsn: string;
    gstPercentage: number;
    unitName: string;
    categoryName: string;
}

interface IRawMaterialVariationNested {
    id: number;
    sku: string;
    name: string;
    msq: number;
    moq: number;
    attributes: {
        attribute: IItemAttribute;
        attributeValue: IItemAttributeValue;
    }[];
    priceData: {
        supplier: ISupplier;
        price: number;
    }[];
}

interface IRawMaterial {
    rawMaterial: {
        id: number;
        category: IItemCategory;
        unit: IItemUnit;
        name: string;
        hsn: string;
        gstPercentage: number;
        status: RAW_MATERIAL_STAUS;
    },
    variations: IRawMaterialVariationNested[];
}

interface IRawMaterialUpdateRequest {
    id: number;
    categoryId: number;
    unitId: number;
    name: string;
    hsn: string;
    gstPercentage: number;
}


const AddVariationRequestSchema = z.object({
    rawMaterialId: z.number().int().positive("Invalid raw material id"),
    name: z.string().min(3, "Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
    sku: z.string().min(3, "SKU must be at least 3 characters long").max(100, "SKU must be up to 100 characters long"),
    msq: z.number().nonnegative("MSQ must be a positive number"),
    moq: z.number().nonnegative("MOQ must be a positive number"),
    attributes: z.array(
        z.object({
            attributeValueId: z.number().int().positive("Invalid attribute value id"),
        })
    ).min(1, "At least one attribute is required"),
    priceData: z.array(
        z.object({
            supplierId: z.number(),
            price: z.number()
        })
    ).min(1, "At least one price data is required"),
});

type TAddVariationRequest = z.infer<typeof AddVariationRequestSchema>;

const UpdateVariationRequestSchema = AddVariationRequestSchema.extend({
    id: z.number().int().positive("Invalid variation id"),
});

type TUpdateVariationRequest = z.infer<typeof UpdateVariationRequestSchema>;

const DeleteVariationRequestSchema = z.object({
    ids: z.array(z.number().int().positive("Invalid variation ids")).min(1, "At least one variation id is required"),
});

type TDeleteVariationRequest = z.infer<typeof DeleteVariationRequestSchema> & { deletedById: number };


export {
    type IRawMaterialOverview,
    type ICreateRawMaterial, type ICreateVariation, RAW_MATERIAL_STAUS, type IRawMaterialAddFormState as IRawMaterialFormState, type IRawMaterialCreatePaylod, type IRawMaterialPriceData, type IRawMaterialUpdatePaylod,
    type IRawMaterialVariation, type ICreateRawMaterialOld,
    type IRawMaterialAddRequest,
    type IRawMaterialVariationAddRequest,
    type IRawMaterialVariationAddState,
    type IRawMaterial,
    type IRawMaterialUpdateRequest,
    type TAddVariationRequest,
    type TUpdateVariationRequest,
    type TDeleteVariationRequest,
    AddVariationRequestSchema,
    UpdateVariationRequestSchema,
    DeleteVariationRequestSchema,
    type IRawMaterialVariationNested,

};