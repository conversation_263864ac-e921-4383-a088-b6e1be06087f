import { z } from "zod";
import { RAW_MATERIAL_STAUS, type ICreateVariation, type IRawMaterialOverview, type IRawMaterialAddRequest, type IRawMaterialCreatePaylod, type IRawMaterialFormState, type IRawMaterialVariationAddRequest, type IRawMaterial, type IRawMaterialUpdateRequest, type TUpdateVariationRequest, } from "../models/IRawMaterial";
import { CSVProvider } from "$lib/csv-provider/repositories/CSVProvider";
import { showErrorToast } from "$lib/common/utils/common-utils";
import { PresenterProvider } from "$lib/PresenterProvider";
import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";

export abstract class RawMaterialUtils {

    static emptyFormState: IRawMaterialFormState = {
        rawMaterial: {
            category: null,
            unit: null,
            name: "",
            hsn: "",
            gstPercentage: 0,
        },
        variations: [],
    };

    static getEmptyVariation() {
        return {
            enabled: true,
            hash: "",
            name: "",
            sku: "",
            msq: 0,
            moq: 0,
            attributes: [],
            priceData: [
                {
                    price: 0,
                    supplier: null,
                },
            ],
        };
    }





    private static priceData = z.object({
        price: z.number().int().positive("Price must be a positive number"),
        supplierId: z.number().int().positive("Please select a valid supplier"),
    });

    private static variationSchema = z.object({
        name: z.string().min(3, "Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
        sku: z.string().min(3, "SKU must be at least 3 characters long").max(100, "SKU must be up to 100 characters long"),
        msq: z.number().nonnegative("MSQ must be positive"),
        moq: z.number().nonnegative("MOQ must be positive"),
        attributes: z.array(z.object({
            attributeValueId: z.number().int().positive("Please select a valid attribute value"),
        })).min(1, "At least one attribute is required"),
        priceData: z.array(this.priceData).min(1, "At least one supplier is required"),
    });

    static creationSchema = z.object({
        name: z.string().min(3, 'Name must be at least 3 characters long').max(100, 'Name must be up to 100 characters long'),
        categoryId: z.number().min(1, 'Category is required'),
        unitId: z.number().int().refine((val) => val !== -1, { message: "Unit is required" }),
        hsn: z.string().min(3, 'HSN must be at least 3 characters long').max(50, 'HSN must be up to 50 characters long'),
        gstPercentage: z.number().positive('GST Percentage must be positive'),
    });

    static updateSchema = this.creationSchema.extend({
        id: z.number().int().positive("ID is required"),
    });

    static validateCreate = (payload: IRawMaterialAddRequest): ValidationErrors => {
        const errors: ValidationErrors = new Map();
        const result = RawMaterialUtils.creationSchema.safeParse(payload.rawMaterial);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }

    static validateUpdate = (payload: IRawMaterialUpdateRequest): ValidationErrors => {
        const errors: ValidationErrors = new Map();
        const result = RawMaterialUtils.updateSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }

    static validateVariations = (payload: IRawMaterialVariationAddRequest[]): IndexedValidationErrors => {
        const errors: IndexedValidationErrors = new Map();

        let result;
        let item;
        for (let i = 0; i < payload.length; i++) {
            item = payload[i];

            result = this.variationSchema.safeParse(item);

            if (!result.success) {
                result.error.issues.forEach((issue) => {
                    console.log(issue);
                    
                    const fieldName = issue.path.join('.');
                    if (!errors.has(i)) {
                        errors.set(i, [{ fieldName, errorMessage: issue.message }]);
                    } else {
                        errors.get(i)!.push({ fieldName, errorMessage: issue.message });
                    }
                });

                if (item.attributes.length === 0) {
                    if (!errors.has(i)) {
                        errors.set(i, [{ fieldName: "attributes", errorMessage: "At least one attribute is required" }]);
                    } else {
                        errors.get(i)!.push({ fieldName: "attributes", errorMessage: "At least one attribute is required" });
                    }
                }
                break;

            }
        }
        return errors;
    }


    static validateUpdateVariation = (payload: TUpdateVariationRequest): ValidationErrors => {
        const errors: ValidationErrors = new Map();
        const result = this.variationSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                console.log(issue);
                // errors.set(issue.path[0].toString(), issue.message);
                errors.set(issue.path.join("."), issue.message);
            });
        }
        return errors;
    }
}

let keyMap: { [key: string]: string } = {
    'Category Name': 'categoryName',
    'Name': 'name',
    'Unit Name': 'unitName',
    'HSN': 'hsn',
    'Gst Percentage': 'gstPercentage',
}

export const downloadCsv = async (data: IRawMaterialOverview[], fileName: string) => {
    if (data.length === 0) {
        showErrorToast("Please choose data")
        return;
    }
    await new CSVProvider().save(data, fileName, keyMap);
}

export const downloadAllCsv = async (startDate: Date, endDate: Date) => {
    const response = await PresenterProvider.rawMaterialPresenter.getRawMaterials(1, -1, undefined)
    if (response.success && response.data.data && response.data.data.length > 0) {
        await new CSVProvider().save(response.data.data, "raw-materials", keyMap);
    } else {
        showErrorToast("No data found")
    }
}