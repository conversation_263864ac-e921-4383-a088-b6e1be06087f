import { type IRawMaterial, type IRawMaterialFormState, type IRawMaterialVariationAddState, type IRawMaterialUpdatePaylod, RAW_MATERIAL_STAUS, type IRawMaterialVariation, type IRawMaterialUpdateRequest } from "../models/IRawMaterial";
import type { ValidationErrors } from "$lib/common/utils/types";
import { RawMaterialUtils } from "./RawMaterialUtils";

export abstract class RawMaterialEditUtils {


    /**
     * Converts IRawMaterial data to IRawMaterialFormState for editing
     */
    static convertToFormState(rawMaterial: IRawMaterial): IRawMaterialFormState {
        const formState: IRawMaterialFormState = {
            rawMaterial: {
                category: rawMaterial.rawMaterial.category,
                unit: rawMaterial.rawMaterial.unit,
                name: rawMaterial.rawMaterial.name,
                hsn: rawMaterial.rawMaterial.hsn,
                gstPercentage: rawMaterial.rawMaterial.gstPercentage,
            },
            variations: rawMaterial.variations.map(variation => ({
                name: variation.name,
                sku: variation.sku,
                msq: variation.msq,
                moq: variation.moq,
                attributes: variation.attributes.map(attr => ({
                    attributeValueId: attr.attributeValue.id
                })),
                priceData: variation.priceData.map(price => ({
                    supplier: price.supplier,
                    price: price.price
                }))
            }))
        };

        return formState;
    }

    /**
     * Prepares update payload from form state for raw material data only
     */
    static prepareRawMaterialUpdatePayload(
        formState: IRawMaterialFormState,
        rawMaterialId: number,
        status: RAW_MATERIAL_STAUS = RAW_MATERIAL_STAUS.ACTIVE
    ): IRawMaterialUpdatePaylod {
        return {
            id: rawMaterialId,
            name: formState.rawMaterial.name,
            unitId: formState.rawMaterial.unit?.id ?? -1,
            categoryId: formState.rawMaterial.category?.id ?? -1,
            hsn: formState.rawMaterial.hsn,
            gstPercentage: formState.rawMaterial.gstPercentage,
            status: status,
            variations: [] // Empty for raw material only update
        };
    }

    /**
     * Validates raw material data for update
     */
    static validateRawMaterialUpdate(formState: IRawMaterialUpdateRequest): ValidationErrors {
        return RawMaterialUtils.validateUpdate(formState);
    }

    /**
     * Creates an empty form state for new raw material
     */
    static getEmptyFormState(): IRawMaterialFormState {
        return {
            rawMaterial: {
                category: null,
                unit: null,
                name: "",
                hsn: "",
                gstPercentage: 0,
            },
            variations: [],
        };
    }

    /**
     * Adds a new variation to the form state
     */
    static addNewVariation(formState: IRawMaterialFormState): IRawMaterialFormState {
        const newVariation = RawMaterialUtils.getEmptyVariation();
        return {
            ...formState,
            variations: [...formState.variations, newVariation]
        };
    }

    /**
     * Removes a variation from the form state
     */
    static removeVariation(formState: IRawMaterialFormState, index: number): IRawMaterialFormState {
        const newVariations = [...formState.variations];
        newVariations.splice(index, 1);
        return {
            ...formState,
            variations: newVariations
        };
    }
}
