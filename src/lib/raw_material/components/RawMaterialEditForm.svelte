<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input } from "flowbite-svelte";
    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import {
        type IRawMaterial,
        type IRawMaterialFormState,
        type IRawMaterialUpdatePaylod,
        type IRawMaterialVariationAddRequest,
        type IRawMaterialVariationAddState,
        type IRawMaterialVariationNested,
        type TAddVariationRequest,
        type TDeleteVariationRequest,
        type TUpdateVariationRequest,
    } from "../models/IRawMaterial";
    import { RawMaterialEditUtils } from "../utils/RawMaterialEditUtils";
    import { RawMaterialUtils } from "../utils/RawMaterialUtils";
    import { afterUpdate, onMount } from "svelte";
    import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import ItemUnitDropdown from "$lib/item_unit/components/ItemUnitDropdown.svelte";
    import ItemCategorySearch from "$lib/item_category/components/ItemCategorySearch.svelte";
    import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import RawMaterialVariationTable from "./RawMaterialVariationTable.svelte";
    import RawMaterialVariationRow from "./RawMaterialVariationItem.svelte";
    import { openConfirmDialog } from "$lib/common/utils/custom_confirmation_component_store";
    import { closeActivity, showActivity } from "$lib/common/utils/activity_store";

    export let rawMaterial: IRawMaterial;
    export let isInsideModal: boolean = false;
    export let onUpdateSuccess: (() => void) | null = null;
    export let addNewVariation: boolean = false;

    let formState: IRawMaterialFormState = RawMaterialEditUtils.getEmptyFormState();
    let newVariations: IRawMaterialVariationAddState[] = [];
    let isDoingTask: boolean = false;
    let isLoadingUnits: boolean = true;
    let validationErrors: ValidationErrors = new Map();
    let itemUnits: IItemUnit[] = [];
    let variationErrors: IndexedValidationErrors = new Map();
    let notifySavedState = false;

    const handleGstPercentage = (e: Event) => {
        const value = parseFloat((e.target as HTMLInputElement).value);
        formState.rawMaterial.gstPercentage = isNaN(value) ? 0 : value;
    };

    const handleUpdateRawMaterial = async () => {
        validationErrors = RawMaterialEditUtils.validateRawMaterialUpdate({
            id: rawMaterial.rawMaterial.id,
            name: formState.rawMaterial.name,
            unitId: formState.rawMaterial.unit?.id ?? -1,
            categoryId: formState.rawMaterial.category?.id ?? -1,
            hsn: formState.rawMaterial.hsn,
            gstPercentage: formState.rawMaterial.gstPercentage,
        });
        if (validationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }

        const payload: IRawMaterialUpdatePaylod =
            RawMaterialEditUtils.prepareRawMaterialUpdatePayload(
                formState,
                rawMaterial.rawMaterial.id,
                rawMaterial.rawMaterial.status
            );

        isDoingTask = true;
        showActivity("Updating raw material...");
        let res = await PresenterProvider.rawMaterialPresenter.onUpdateRawMaterial(payload);
        closeActivity();
        if (res.success) {
            showSuccessToast("Raw material updated successfully");
            if (onUpdateSuccess) {
                onUpdateSuccess();
            } else {
                await goto("/admin/raw-materials");
            }
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };

    const _loadUnits = async () => {
        const response = await PresenterProvider.rawMaterialPresenter.getItemUnits(1, 1000);
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            itemUnits = response.data.data;
            if (itemUnits.length === 0) {
                showErrorToast("Please add at least one unit to edit a raw material", 6);
                return goto("/admin/item-units/add");
            }

            isLoadingUnits = false;
        }
    };

    const deleteRawMaterial = async () => {
        isDoingTask = true;
        let res = await PresenterProvider.rawMaterialPresenter.onDeleteRawMaterial([
            rawMaterial.rawMaterial.id,
        ]);
        if (res.success) {
            showSuccessToast("Raw material deleted successfully");
            if (onUpdateSuccess) {
                onUpdateSuccess();
            } else {
                await goto("/admin/raw-materials");
            }
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };

    const handleDeleteVariation = async (variation: IRawMaterialVariationNested) => {
        const payload: TDeleteVariationRequest = {
            ids: [variation.id],
            deletedById: 1,
        };
        isDoingTask = true;
        showActivity("Deleting variation...");
        let res = await PresenterProvider.rawMaterialPresenter.deleteVariation(payload);
        closeActivity();
        if (res.success) {
            showSuccessToast("Variation deleted successfully");
            rawMaterial.variations = rawMaterial.variations.filter(
                (item) => item.id !== variation.id
            );
            rawMaterial = rawMaterial;
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };
    const handleAddNewVariation = () => {
        if (!addNewVariation) {
            addNewVariation = true;
            return;
        }
        const newVariation = RawMaterialUtils.getEmptyVariation();
        newVariations.push(newVariation);
        newVariations = newVariations;
    };
    const handleSaveNewVariations = async () => {
        const payload: IRawMaterialVariationAddRequest[] = newVariations.map((item) => {
            return {
                name: item.name,
                sku: item.sku,
                msq: item.msq,
                moq: item.moq,
                attributes: item.attributes.map((attribute) => {
                    return {
                        attributeValueId: attribute.attributeValueId,
                    };
                }),
                priceData: item.priceData.map((priceData) => {
                    return {
                        price: priceData.price,
                        supplierId: priceData.supplier?.id ?? -1,
                    };
                }),
            };
        });
        variationErrors = RawMaterialUtils.validateVariations(payload);
        if (variationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        isDoingTask = true;
        showActivity("Adding variations...");
        const savePayload: TAddVariationRequest[] = [];
        newVariations.forEach((item) => {
            savePayload.push({
                rawMaterialId: rawMaterial.rawMaterial.id,
                name: item.name,
                sku: item.sku,
                msq: item.msq,
                moq: item.moq,
                attributes: item.attributes.map((attribute) => {
                    return {
                        attributeValueId: attribute.attributeValueId,
                    };
                }),
                priceData: item.priceData.map((priceData) => {
                    return {
                        price: priceData.price,
                        supplierId: priceData.supplier?.id ?? -1,
                    };
                }),
            });
        });
        let res = await PresenterProvider.rawMaterialPresenter.addVariation(savePayload);
        closeActivity();
        if (res.success) {
            notifySavedState = false;
            showSuccessToast("Variations added successfully");
            rawMaterial.variations.push(...res.data!);
            rawMaterial.variations = rawMaterial.variations;
            newVariations = [RawMaterialUtils.getEmptyVariation()];
            rawMaterial = rawMaterial;
            notifySavedState = true;
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };
    onMount(() => {
        _loadUnits();
        formState = RawMaterialEditUtils.convertToFormState(rawMaterial);
        newVariations.push(RawMaterialUtils.getEmptyVariation());
        newVariations = newVariations;
    });
</script>

{#if isLoadingUnits}
    <PageLoader />
{:else}
    <div class="flex items-center justify-center">
        <div class=" w-[90vw] p-2">
            {#if !isInsideModal}
                <div class=" flex items-center justify-between py-2">
                    <FormHeader label={"Edit raw material"}></FormHeader>
                    <BreadCrumbs breadCrumbData={[]} />
                </div>
                <hr class="mb-5" />
            {/if}

            <!-- Raw Material Basic Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                    Raw Material Information
                </h2>

                <div class="grid grid-cols-2 gap-6">
                    <div>
                        <ItemCategorySearch
                            onSelected={(data) => {
                                if (data) {
                                    formState.rawMaterial.category = data;
                                } else {
                                    formState.rawMaterial.category = null;
                                }
                            }}
                            selected={formState.rawMaterial.category}
                        />
                        {#if validationErrors.has("categoryId")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("categoryId")}
                            </p>
                        {/if}
                    </div>
                </div>

                <div class="m-2"></div>

                <div class=" grid grid-cols-2 gap-6">
                    <div>
                        <Label for="name" class="mb-2 font-sans capitalize tracking-[0px]">
                            Name
                            {#if validationErrors.has("name")}
                                <span class="text-red-600">*</span>
                            {/if}
                        </Label>
                        <Input
                            type="text"
                            id="name"
                            placeholder="Name"
                            class="dark:bg-primary-700 {validationErrors.has('name')
                                ? 'border-red-500'
                                : ''}"
                            bind:value={formState.rawMaterial.name}
                        />
                        {#if validationErrors.has("name")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("name")}
                            </p>
                        {/if}
                    </div>

                    <div>
                        <Label for="unit" class="mb-2 font-sans capitalize tracking-[0px]">
                            Unit
                            {#if validationErrors.has("unitId")}
                                <span class="text-red-600">*</span>
                            {/if}
                        </Label>

                        <ItemUnitDropdown
                            data={itemUnits}
                            id="unit"
                            cssClass="dark:bg-primary-700 {validationErrors.has('unitId')
                                ? 'border-red-500'
                                : ''}"
                            selectedValue={formState.rawMaterial.unit}
                            onSelected={(data) => {
                                formState.rawMaterial.unit = data;
                            }}
                        />

                        {#if validationErrors.has("unitId")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("unitId")}
                            </p>
                        {/if}
                    </div>
                </div>

                <div class="m-2"></div>

                <div class=" grid grid-cols-2 gap-6">
                    <div>
                        <Label for="hsn" class="mb-2 font-sans capitalize tracking-[0px]">
                            HSN
                            {#if validationErrors.has("hsn")}
                                <span class="text-red-600">*</span>
                            {/if}
                        </Label>
                        <Input
                            type="text"
                            id="hsn"
                            placeholder="HSN"
                            class="uppercase   dark:bg-primary-700 {validationErrors.has('hsn')
                                ? 'border-red-500'
                                : ''}"
                            bind:value={formState.rawMaterial.hsn}
                        />
                        {#if validationErrors.has("hsn")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("hsn")}
                            </p>
                        {/if}
                    </div>
                    <div>
                        <Label for="gstPercentage" class="mb-2 font-sans capitalize tracking-[0px]">
                            GST Percentage
                            {#if validationErrors.has("gstPercentage")}
                                <span class="text-red-600">*</span>
                            {/if}
                        </Label>
                        <Input
                            type="number"
                            id="gstPercentage"
                            placeholder="GST Percentage"
                            class="dark:bg-primary-700 {validationErrors.has('gstPercentage')
                                ? 'border-red-500'
                                : ''}"
                            value={formState.rawMaterial.gstPercentage}
                            on:change={handleGstPercentage}
                        />
                        {#if validationErrors.has("gstPercentage")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("gstPercentage")}
                            </p>
                        {/if}
                    </div>
                </div>

                <div class="mt-5 flex justify-end gap-2">
                    <CustomButton
                        onClick={() => {
                            openConfirmDialog({
                                heading: "Delete Raw Material",
                                body: "Are you sure you want to delete this raw material?",
                                positiveButtonText: "Yes",
                                negativeButtonText: "No",
                                positiveAction: deleteRawMaterial,
                            });
                        }}
                        cssClass="w-40 !bg-red-600 hover:bg-red-700"
                        title={"Delete"}
                    />
                    <CustomButton
                        onClick={handleUpdateRawMaterial}
                        cssClass="w-40 bg-blue-600 hover:bg-blue-700"
                        title={"Update"}
                        isLoading={isDoingTask}
                    />
                </div>
            </div>
            <!-- Existing Variations Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                    Existing Variations
                </h2>
                {#if rawMaterial.variations && rawMaterial.variations.length > 0}
                    <div class="space-y-4">
                        {#each rawMaterial.variations as variation, index (variation.id)}
                            <RawMaterialVariationRow
                                totalVariations={rawMaterial.variations.length}
                                parentRawMaterialId={rawMaterial.rawMaterial.id}
                                {variation}
                                onDelete={() =>
                                    openConfirmDialog({
                                        heading: "Delete Variation!",
                                        body: "Are you sure you want to delete this variation?",
                                        positiveButtonText: "Yes",
                                        negativeButtonText: "No",
                                        positiveAction: () => handleDeleteVariation(variation),
                                    })}
                                onEditSuccess={(variation) => {
                                    rawMaterial.variations[index] = variation;
                                    rawMaterial.variations = rawMaterial.variations;
                                }}
                            />
                        {/each}
                    </div>
                {:else}
                    <p class="text-gray-500 dark:text-gray-400 text-center py-8">
                        No existing variations found.
                    </p>
                {/if}
            </div>
            <!-- New Variations Section -->
            {#if newVariations.length > 0 && addNewVariation}
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                        Add New Variations
                    </h2>

                    <div class="overflow-x-auto">
                        <RawMaterialVariationTable
                            variations={newVariations}
                            {variationErrors}
                            collapseAll={notifySavedState}
                        />
                    </div>
                </div>
            {/if}
            <!-- Add New Variation Button -->
            <div class="flex justify-between mb-6 gap-2">
                <CustomButton
                    title="Add New Variation"
                    cssClass="bg-green-600 hover:bg-green-700 text-white px-6 py-3"
                    onClick={handleAddNewVariation}
                />
                {#if addNewVariation && newVariations.length > 0}
                    <CustomButton
                        title="Save variations"
                        cssClass="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
                        onClick={handleSaveNewVariations}
                        isLoading={isDoingTask}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}
