<script lang="ts">
    import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import { Input, Label } from "flowbite-svelte";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import type {
        IRawMaterialFormState,
        IRawMaterialVariationAddState,
    } from "../models/IRawMaterial";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import AttributesSelection from "$lib/common/components/AttributesSelection.svelte";
    import RedRoundCloseButton from "$lib/common/components/RedRoundCloseButton.svelte";
    import type {
        IItemAttribute,
        IItemAttributeValue,
    } from "$lib/item_attributes/models/IItemAttribute";
    import { afterUpdate, onMount } from "svelte";

    export let variations: IRawMaterialVariationAddState[] = [];
    export let variationErrors: IndexedValidationErrors = new Map();
    export let collapseAll = false;

    // Track expanded state for each variation
    let expandedVariations: boolean[] = [];

    const handleMsq = (e: Event, index: number) => {
        if (!variations) {
            return showErrorToast("Please add variation first");
        }
        const value = parseFloat((e.target as HTMLInputElement).value);
        variations[index].msq = isNaN(value) ? 0 : value;
    };

    const handleMOQ = (e: Event, index: number) => {
        if (!variations) {
            return showErrorToast("Please add variation first");
        }
        const value = parseFloat((e.target as HTMLInputElement).value);
        variations[index].moq = isNaN(value) ? 0 : value;
    };

    const assignSupplier = (supplier: ISupplier, VariationIndex: number, index: number) => {
        if (!variations) {
            return showErrorToast("Please add variation first");
        }
        if (
            variations[VariationIndex].priceData.some((item) => item.supplier?.id === supplier.id)
        ) {
            return showErrorToast("Supplier already added");
        }
        variations[VariationIndex].priceData[index].supplier = supplier;
    };

    const handlePrice = (e: any, variationIndex: number, index: number) => {
        if (!variations) {
            return showErrorToast("Please add variation first");
        }
        const value = parseFloat((e.target as HTMLInputElement).value);
        variations[variationIndex].priceData[index].price = isNaN(value) ? 0 : value;
    };

    const handleAttributeSelection = (
        data: { attribute: IItemAttribute; attributeValue: IItemAttributeValue }[],
        variationIndex: number
    ) => {
        if (!variations) {
            return showErrorToast("Please add variation first");
        }
        variations[variationIndex].attributes = data.map((item) => ({
            attributeValueId: item.attributeValue.id,
        }));
        variations = variations;
    };

    const toggleVariation = (index: number) => {
        expandedVariations[index] = !expandedVariations[index];
        expandedVariations = expandedVariations;
    };

    $: if (variations) {
        while (expandedVariations.length < variations.length) {
            expandedVariations.push(true);
        }
        if (expandedVariations.length > variations.length) {
            expandedVariations = expandedVariations.slice(0, variations.length);
        }
    }

    afterUpdate(() => {
        if (collapseAll) {
            expandedVariations = expandedVariations.map(() => false);
            collapseAll = false;
        }
    });
</script>

<div class="space-y-6">
    {#each variations as variation, index (index)}
        <div
            class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
        >
            <!-- Header with expand/collapse and remove buttons -->
            <div
                class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700"
            >
                <div class="flex justify-between items-center gap-3 w-full">
                    <span class="text-2xl font-bold text-gray-600 dark:text-gray-400">
                        Variation {index + 1}
                    </span>
                    <button
                        type="button"
                        class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        on:click={() => toggleVariation(index)}
                        aria-label={expandedVariations[index]
                            ? "Collapse variation details"
                            : "Expand variation details"}
                        title={expandedVariations[index] ? "Collapse" : "Expand"}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 transition-transform duration-200 {expandedVariations[
                                index
                            ]
                                ? 'rotate-90'
                                : ''}"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 5l7 7-7 7"
                            />
                        </svg>
                    </button>
                </div>
                <div class="flex items-center gap-2">
                    {#if variations.length > 1}
                        <RedRoundCloseButton
                            onClick={() => {
                                variations?.splice(index, 1);
                                variations = variations; // Trigger reactivity
                            }}
                        />
                    {/if}
                </div>
            </div>

            <!-- Basic fields in 3-column grid -->
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Name -->
                    <div>
                        <Label for="name-{index}" class="mb-2">Name</Label>
                        <Input
                            type="text"
                            id="name-{index}"
                            placeholder="Name"
                            bind:value={variation.name}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "name")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "name")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- SKU -->
                    <div>
                        <Label for="sku-{index}" class="mb-2">SKU</Label>
                        <Input
                            type="text"
                            id="sku-{index}"
                            placeholder="SKU"
                            bind:value={variation.sku}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "sku")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "sku")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- MSQ -->
                    <div>
                        <Label for="msq-{index}" class="mb-2">MSQ</Label>
                        <Input
                            type="number"
                            id="msq-{index}"
                            placeholder="MSQ"
                            value={variation.msq}
                            on:input={(e) => handleMsq(e, index)}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "msq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "msq")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- MOQ -->
                    <div>
                        <Label for="moq-{index}" class="mb-2">MOQ</Label>
                        <Input
                            type="number"
                            id="moq-{index}"
                            placeholder="MOQ"
                            value={variation.moq}
                            on:change={(e) => {
                                handleMOQ(e, index);
                            }}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "moq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "moq")!.errorMessage}
                            </p>
                        {/if}
                    </div>
                </div>
            </div>

            <!-- Expanded section for attributes and supplier information -->
            {#if expandedVariations[index]}
                <div
                    class="border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-750"
                >
                    <div class="p-4">
                        <!-- Attributes section -->
                        <div class="mb-6">
                            <div class="flex items-center gap-2 mb-3">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5 text-blue-600"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                                    />
                                </svg>
                                <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300">
                                    Attributes
                                </h4>
                            </div>
                            <div
                                class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600"
                            >
                                <AttributesSelection
                                    existingData={new Array()}
                                    onSelect={(data) => {
                                        handleAttributeSelection(data, index);
                                    }}
                                />
                                {#if variationErrors.has(index) && variationErrors
                                        .get(index)!
                                        .find((item) => item.fieldName === "attributes")}
                                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                        {variationErrors
                                            .get(index)!
                                            .find((item) => item.fieldName === "attributes")!
                                            .errorMessage}
                                    </p>
                                {/if}
                            </div>
                        </div>

                        <!-- Supplier Information section -->
                        <div class="mb-4">
                            <div class="flex items-center gap-2 mb-3">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5 text-green-600"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                                    />
                                </svg>
                                <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300">
                                    Supplier Information
                                </h4>
                            </div>
                            <div
                                class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden"
                            >
                                <table class="w-full">
                                    <thead class="bg-gray-100 dark:bg-gray-700">
                                        <tr>
                                            <th
                                                class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                                            >
                                                SR No.
                                            </th>
                                            <th
                                                class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                                            >
                                                Supplier
                                            </th>
                                            <th
                                                class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                                            >
                                                Price
                                            </th>
                                            <th
                                                class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                                            >
                                                Remove
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {#if variation.priceData && variation.priceData.length > 0}
                                            {#each variation.priceData as priceItem, supplierIndex}
                                                <tr
                                                    class="border-t border-gray-200 dark:border-gray-600"
                                                >
                                                    <td class="px-4 py-3 text-sm">
                                                        <div class="mb-8">
                                                            {supplierIndex + 1}
                                                        </div>
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <div class="mb-8">
                                                            <SupplierSearch
                                                                selected={priceItem.supplier}
                                                                onSelected={(data) => {
                                                                    assignSupplier(
                                                                        data,
                                                                        index,
                                                                        supplierIndex
                                                                    );
                                                                }}
                                                                isLabel={false}
                                                            />
                                                            {#if variationErrors.has(index) && variationErrors
                                                                    .get(index)!
                                                                    .find((item) => item.fieldName === `priceData.${supplierIndex}.supplierId`)}
                                                                <p
                                                                    class="pt-1 text-xs text-red-500"
                                                                >
                                                                    {variationErrors
                                                                        .get(index)!
                                                                        .find(
                                                                            (item) =>
                                                                                item.fieldName ===
                                                                                `priceData.${supplierIndex}.supplierId`
                                                                        )!.errorMessage}
                                                                </p>
                                                            {/if}
                                                        </div>
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <div class="mb-8">
                                                            <Input
                                                                type="number"
                                                                class="w-full h-[36px]"
                                                                placeholder="Price"
                                                                value={priceItem.price}
                                                                on:change={(e) => {
                                                                    handlePrice(
                                                                        e,
                                                                        index,
                                                                        supplierIndex
                                                                    );
                                                                }}
                                                            />
                                                            {#if variationErrors.has(index) && variationErrors
                                                                    .get(index)!
                                                                    .find((item) => item.fieldName === `priceData.${supplierIndex}.price`)}
                                                                <p
                                                                    class="pt-1 text-xs text-red-500"
                                                                >
                                                                    {variationErrors
                                                                        .get(index)!
                                                                        .find(
                                                                            (item) =>
                                                                                item.fieldName ===
                                                                                `priceData.${supplierIndex}.price`
                                                                        )!.errorMessage}
                                                                </p>
                                                            {/if}
                                                        </div>
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        {#if variation.priceData && variation.priceData.length > 1}
                                                            <div class="mb-8">
                                                                <RedRoundCloseButton
                                                                    onClick={() => {
                                                                        variation.priceData?.splice(
                                                                            supplierIndex,
                                                                            1
                                                                        );
                                                                        variation.priceData =
                                                                            variation.priceData;
                                                                    }}
                                                                />
                                                            </div>
                                                        {/if}
                                                    </td>
                                                </tr>
                                            {/each}
                                        {:else}
                                            <tr>
                                                <td
                                                    colspan="4"
                                                    class="px-4 py-6 text-center text-gray-500 dark:text-gray-400"
                                                >
                                                    No suppliers added yet. Click "Add New Supplier"
                                                    below to add one.
                                                </td>
                                            </tr>
                                        {/if}
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-3 flex justify-end">
                                <button
                                    type="button"
                                    class="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors shadow-sm"
                                    on:click={() => {
                                        if (!variation.priceData) {
                                            variation.priceData = [];
                                        }
                                        variation.priceData = [
                                            ...variation.priceData,
                                            { price: 0, supplier: null },
                                        ];
                                    }}
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 mr-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M12 4v16m8-8H4"
                                        />
                                    </svg>
                                    Add New Supplier
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    {/each}
</div>
