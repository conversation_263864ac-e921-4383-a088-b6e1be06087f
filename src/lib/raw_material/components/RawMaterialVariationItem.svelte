<script lang="ts">
    import { Input, Spinner } from "flowbite-svelte";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import type {
        IRawMaterialVariationAddState,
        IRawMaterialVariationNested,
        TUpdateVariationRequest,
    } from "../models/IRawMaterial";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import AttributesSelection from "$lib/common/components/AttributesSelection.svelte";
    import RedRoundCloseButton from "$lib/common/components/RedRoundCloseButton.svelte";
    import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import { onMount } from "svelte";
    import { RawMaterialUtils } from "../utils/RawMaterialUtils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import type {
        IItemAttribute,
        IItemAttributeValue,
    } from "$lib/item_attributes/models/IItemAttribute";
    import { closeActivity, showActivity } from "$lib/common/utils/activity_store";

    export let variation: IRawMaterialVariationNested;
    export let parentRawMaterialId: number;
    export let totalVariations: number;
    export let onEditSuccess: (variation: IRawMaterialVariationNested) => void;
    export let onDelete: () => void;
    let variationState: IRawMaterialVariationAddState;
    let isPreparingData = true;
    let isExpanded: boolean = false;
    let isEditing: boolean = false;
    let isDoingTask: boolean = false;
    let validationErrors: ValidationErrors = new Map();

    const handleMsq = (e: Event) => {
        const value = parseFloat((e.target as HTMLInputElement).value);
        variationState.msq = isNaN(value) ? 0 : value;
    };

    const handleMOQ = (e: Event) => {
        const value = parseFloat((e.target as HTMLInputElement).value);
        variationState.moq = isNaN(value) ? 0 : value;
    };

    const assignSupplier = (supplier: ISupplier, supplierIndex: number) => {
        if (variationState.priceData.some((item) => item.supplier?.id === supplier.id)) {
            return showErrorToast("Supplier already added");
        }
        variationState.priceData[supplierIndex].supplier = supplier;
    };

    const handlePrice = (e: any, supplierIndex: number) => {
        const value = parseFloat((e.target as HTMLInputElement).value);
        variationState.priceData[supplierIndex].price = isNaN(value) ? 0 : value;
    };

    const handleAttributeSelection = (
        data: { attribute: IItemAttribute; attributeValue: IItemAttributeValue }[]
    ) => {
        variationState.attributes = data.map((item) => ({
            attributeValueId: item.attributeValue.id,
        }));
        variationState = variationState;
    };

    const toggleExpand = () => {
        isExpanded = !isExpanded;
    };

    const addSupplierPrice = () => {
        if (!variationState.priceData) {
            variationState.priceData = [];
        }
        variationState.priceData = [...variationState.priceData, { price: 0, supplier: null }];
    };

    const removeSupplierPrice = (supplierIndex: number) => {
        variationState.priceData?.splice(supplierIndex, 1);
        variationState.priceData = variationState.priceData; // Trigger reactivity
    };

    const updateVariation = async () => {
        const payload: TUpdateVariationRequest = {
            id: variation.id,
            rawMaterialId: parentRawMaterialId,
            name: variationState.name,
            sku: variationState.sku,
            msq: variationState.msq,
            moq: variationState.moq,
            attributes: variationState.attributes.map((item) => {
                return {
                    attributeValueId: item.attributeValueId,
                };
            }),
            priceData: variationState.priceData.map((item) => {
                return {
                    price: item.price,
                    supplierId: item.supplier?.id ?? -1,
                };
            }),
        };
        validationErrors = RawMaterialUtils.validateUpdateVariation(payload);
        console.log("validationErrors", validationErrors);
        if (validationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        isDoingTask = true;
        showActivity("Updating variation...");
        let res = await PresenterProvider.rawMaterialPresenter.updateVariation(payload);
        closeActivity();
        if (res.success) {
            isEditing = false;
            isExpanded = false;
            showSuccessToast("Variation updated successfully");
            onEditSuccess(res.data!);
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };

    onMount(() => {
        variationState = {
            name: variation.name,
            sku: variation.sku,
            msq: variation.msq,
            moq: variation.moq,
            attributes: variation.attributes.map((attr) => ({
                attributeValueId: attr.attributeValue.id,
            })),
            priceData: variation.priceData.map((price) => ({
                supplier: price.supplier,
                price: price.price,
            })),
        };
        isPreparingData = false;
    });
</script>

{#if isPreparingData}
    <div class="flex h-[80vh] w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <div
        id="variation-{variation.id}"
        class="mb-4 rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
        <div
            class="flex items-center justify-between px-6 py-4"
            class:border-b={isExpanded}
            class:border-gray-200={isExpanded}
            class:dark:border-gray-700={isExpanded}
        >
            <div class="flex items-center gap-4">
                <button
                    type="button"
                    class="flex h-8 w-8 items-center justify-center rounded-full transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
                    on:click={toggleExpand}
                    aria-label={isExpanded
                        ? "Collapse variation details"
                        : "Expand variation details"}
                    title={isExpanded ? "Collapse" : "Expand"}
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 transition-transform duration-200 {isExpanded
                            ? 'rotate-90'
                            : ''}"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 5l7 7-7 7"
                        />
                    </svg>
                </button>
                <h3 class="text-md font-semibold text-gray-700 dark:text-gray-300">
                    Variation #{variation.id}: {variationState.name || "Unnamed"}
                </h3>
            </div>
            <div class="flex items-center gap-2">
                {#if isEditing}
                    <button
                        type="button"
                        class="rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
                        on:click={updateVariation}
                    >
                        Update
                    </button>
                    <button
                        type="button"
                        class="rounded-md bg-gray-300 px-3 py-1.5 text-sm font-medium text-gray-800 shadow-sm hover:bg-gray-400"
                        on:click={() => {
                            isEditing = false;
                            isExpanded = false;
                            variationState = {
                                name: variation.name,
                                sku: variation.sku,
                                msq: variation.msq,
                                moq: variation.moq,
                                attributes: variation.attributes.map((attr) => ({
                                    attributeValueId: attr.attributeValue.id,
                                })),
                                priceData: variation.priceData.map((price) => ({
                                    supplier: price.supplier,
                                    price: price.price,
                                })),
                            };
                        }}
                    >
                        Cancel
                    </button>
                {:else}
                    <button
                        type="button"
                        class="rounded-md bg-blue-500 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-blue-600"
                        on:click={() => {
                            isEditing = true;
                            isExpanded = true;
                        }}
                    >
                        Edit
                    </button>
                {/if}

                {#if totalVariations > 1}
                    <button
                        type="button"
                        class="rounded-md bg-red-500 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-red-600"
                        on:click={() => onDelete()}
                    >
                        Delete
                    </button>
                {/if}
            </div>
        </div>

        {#if isExpanded}
            <div class="p-6">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <div>
                        <label
                            for="name-{variation.id}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            Name
                        </label>
                        <Input
                            type="text"
                            id="name-{variation.id}"
                            placeholder="Name"
                            bind:value={variationState.name}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("name")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("name")}
                            </p>
                        {/if}
                    </div>

                    <div>
                        <label
                            for="sku-{variation.id}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            SKU
                        </label>
                        <Input
                            type="text"
                            id="sku-{variation.id}"
                            placeholder="SKU"
                            bind:value={variationState.sku}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("sku")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("sku")}
                            </p>
                        {/if}
                    </div>

                    <div>
                        <label
                            for="msq-{variation.id}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            MSQ
                        </label>
                        <Input
                            type="number"
                            id="msq-{variation.id}"
                            placeholder="MSQ"
                            value={variationState.msq}
                            on:input={handleMsq}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("msq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("msq")}
                            </p>
                        {/if}
                    </div>

                    <div>
                        <label
                            for="moq-{variation.id}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            MOQ
                        </label>
                        <Input
                            type="number"
                            id="moq-{variation.id}"
                            placeholder="MOQ"
                            value={variationState.moq}
                            on:input={handleMOQ}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("moq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("moq")}
                            </p>
                        {/if}
                    </div>
                </div>

                <div class="mt-6 border-t border-gray-200 pt-6 dark:border-gray-700">
                    <div class="mb-3 flex items-center gap-2">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 text-blue-600"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                            />
                        </svg>
                        <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300">
                            Attributes
                        </h4>
                    </div>
                    <div
                        class="rounded-lg border border-gray-200 bg-white p-3 dark:border-gray-600 dark:bg-gray-800"
                    >
                        <AttributesSelection
                            onSelect={handleAttributeSelection}
                            existingData={variation.attributes.map((attr) => ({
                                attribute: attr.attribute,
                                attributeValue: attr.attributeValue,
                            }))}
                            isDisabled={!isEditing}
                        />
                        {#if validationErrors.has("attributes")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("attributes")}
                            </p>
                        {/if}
                    </div>
                </div>

                <div class="mt-6 border-t border-gray-200 pt-6 dark:border-gray-700">
                    <div class="mb-3 flex items-center gap-2">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 text-green-600"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                        </svg>
                        <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300">
                            Supplier Information
                        </h4>
                    </div>
                    <div
                        class="overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-600 dark:bg-gray-800"
                    >
                        <table class="w-full">
                            <thead class="bg-gray-100 dark:bg-gray-700">
                                <tr>
                                    <th
                                        class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                                    >
                                        SR No.
                                    </th>
                                    <th
                                        class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                                    >
                                        Supplier
                                    </th>
                                    <th
                                        class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                                    >
                                        Price
                                    </th>
                                    <th
                                        class="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                                    >
                                        Remove
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {#if variationState.priceData && variationState.priceData.length > 0}
                                    {#each variationState.priceData as priceItem, supplierIndex}
                                        <tr class="border-t border-gray-200 dark:border-gray-600">
                                            <td class="px-4 py-3 text-sm">
                                                <div class="mb-4">{supplierIndex + 1}</div>
                                            </td>
                                            <td class="px-4 py-3">
                                                <div class="mb-4">
                                                    <SupplierSearch
                                                        selected={priceItem.supplier}
                                                        onSelected={(data) => {
                                                            assignSupplier(data, supplierIndex);
                                                        }}
                                                        isLabel={false}
                                                        disabled={!isEditing}
                                                    />

                                                    {#if validationErrors.has(`priceData.${supplierIndex}.supplierId`)}
                                                        <p class="pt-1 text-xs text-red-500">
                                                            {validationErrors.get(
                                                                `priceData.${supplierIndex}.supplierId`
                                                            )}
                                                        </p>
                                                    {/if}
                                                </div>
                                            </td>
                                            <td class="px-4 py-3">
                                                <div class="mb-4">
                                                    <Input
                                                        type="number"
                                                        class="h-[36px] w-full"
                                                        placeholder="Price"
                                                        value={priceItem.price}
                                                        on:input={(e) => {
                                                            handlePrice(e, supplierIndex);
                                                        }}
                                                        disabled={!isEditing}
                                                    />
                                                    {#if validationErrors.has(`priceData.${supplierIndex}.price`)}
                                                        <p class="pt-1 text-xs text-red-500">
                                                            {validationErrors.get(
                                                                `priceData.${supplierIndex}.price`
                                                            )}
                                                        </p>
                                                    {/if}
                                                </div>
                                            </td>
                                            <td class="px-4 py-3">
                                                {#if isEditing && variationState.priceData.length > 1}
                                                    <div class="mb-4">
                                                        <RedRoundCloseButton
                                                            onClick={() => {
                                                                removeSupplierPrice(supplierIndex);
                                                            }}
                                                        />
                                                    </div>
                                                {/if}
                                            </td>
                                        </tr>
                                    {/each}
                                {:else}
                                    <tr>
                                        <td
                                            colspan="4"
                                            class="px-4 py-6 text-center text-gray-500 dark:text-gray-400"
                                        >
                                            No suppliers added yet. Click "Add New Supplier" below
                                            to add one.
                                        </td>
                                    </tr>
                                {/if}
                            </tbody>
                        </table>
                    </div>

                    {#if isEditing}
                        <div class="mt-3 flex justify-end">
                            <button
                                type="button"
                                class="flex items-center rounded-md bg-green-600 px-4 py-2 text-white shadow-sm transition-colors hover:bg-green-700"
                                on:click={addSupplierPrice}
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="mr-2 h-4 w-4"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M12 4v16m8-8H4"
                                    />
                                </svg>
                                Add New Supplier
                            </button>
                        </div>
                    {/if}
                </div>
            </div>
        {/if}
    </div>
{/if}
