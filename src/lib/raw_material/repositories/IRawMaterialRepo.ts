import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type {  IRawMaterialVariation, IRawMaterialUpdatePaylod, IRawMaterialAddRequest, IRawMaterialOverview, IRawMaterial, TAddVariationRequest, TDeleteVariationRequest, TUpdateVariationRequest, IRawMaterialVariationNested } from "../models/IRawMaterial";

export interface IRawMaterialRepo {
    create(payload: IRawMaterialAddRequest): Promise<DTO<null>>;
    updateRawMaterial(payload: IRawMaterialUpdatePaylod): Promise<DTO<null>>;
    deleteRawMaterial(ids: number[]): Promise<DTO<null>>;
    getRawMaterialById(id: number): Promise<DTO<IRawMaterial>>;
    getRawMaterialVariationById(id: number): Promise<DTO<IRawMaterialVariation>>;
    getRawMaterials(page: number, pageSize: number,text?:string): Promise<DTO<PaginatedBaseResponse<IRawMaterialOverview>>>;
    addVariation(payload: TAddVariationRequest[]): Promise<DTO<IRawMaterialVariationNested[] | null>>;
    updateVariation(payload: TUpdateVariationRequest): Promise<DTO<IRawMaterialVariationNested | null>>;
    deleteVariation(payload: TDeleteVariationRequest): Promise<DTO<null>>;
    getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialVariation>>>;
}