import { RAW_MATERIAL_API_PATH, RAW_MATERIAL_VARIATION_API_PATH } from "$lib/common/configs/serverConfig";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    getUnhandledErrorDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { handleError } from "$lib/common/utils/logging";
import type { IRawMaterialRepo, } from "./IRawMaterialRepo";
import type { IRawMaterialVariation,  IRawMaterialUpdatePaylod, IRawMaterialAddRequest, IRawMaterialOverview, IRawMaterial, IRawMaterialVariationNested, TAddVariationRequest, TDeleteVariationRequest, TUpdateVariationRequest } from "../models/IRawMaterial";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class RawMaterialRepo implements IRawMaterialRepo {
    private _apiPath: string = RAW_MATERIAL_API_PATH;
    private _apiPathVariation: string = RAW_MATERIAL_VARIATION_API_PATH;

    async create(payload: IRawMaterialAddRequest): Promise<DTO<null>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/create",
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async updateRawMaterial(payload: IRawMaterialUpdatePaylod): Promise<DTO<null>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/" + payload.id,
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async deleteRawMaterial(ids: number[]): Promise<DTO<null>> {
        try {
            const options = {
                method: "DELETE",
                body: {
                    ids: ids,
                },
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/",
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getRawMaterialById(id: number): Promise<DTO<IRawMaterial>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IRawMaterial> = await fetchData(
                this._apiPath + "/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async getRawMaterialVariationById(id: number): Promise<DTO<IRawMaterialVariation>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IRawMaterialVariation> = await fetchData(
                this._apiPath + "/variation/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getRawMaterials(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialOverview>>> {
        try {
            const options = {
                method: "GET",
            };
            let query = "?page=" + page + "&pageSize=" + pageSize;
            if (text) {
                query = query + "&text=" + text;
            }
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialOverview>> = await fetchData(this._apiPath + query, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialVariation>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialVariation>> = await fetchData(this._apiPath + "/searchByText?text=" + text, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

   
    async getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialVariation>>> {
        try {
            const options = {
                method: "GET",
            };
            let query = "?page=" + page + "&pageSize=" + pageSize;
            if (text) {
                query = query + "&text=" + text;
            }
            if (supplierId) {
                query = query + "&supplierId=" + supplierId;
            }

            let url = this._apiPathVariation + "/"+query;
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialVariation>> = await fetchData(url, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async addVariation(payload: TAddVariationRequest[]): Promise<DTO<IRawMaterialVariationNested[] | null>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<IRawMaterialVariationNested[] | null> = await fetchData(
                this._apiPathVariation,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async updateVariation(payload: TUpdateVariationRequest): Promise<DTO<IRawMaterialVariationNested | null>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<IRawMaterialVariationNested | null> = await fetchData(
                this._apiPathVariation + "/" + payload.id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async deleteVariation(payload: TDeleteVariationRequest): Promise<DTO<null>> {
        try {
            const options = {
                method: "DELETE",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPathVariation,
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }


}
