import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
import type { IRawMaterialPresenter, } from "./IRawMaterialPresenter";
import type { IRawMaterialVariation, ICreateRawMaterial, IRawMaterialCreatePaylod, IRawMaterialUpdatePaylod, IRawMaterialAddRequest, IRawMaterialOverview, IRawMaterial, IRawMaterialVariationNested, TAddVariationRequest, TDeleteVariationRequest, TUpdateVariationRequest } from "../models/IRawMaterial";
import { RawMaterialUtils } from "../utils/RawMaterialUtils";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class RawMaterialPresenter implements IRawMaterialPresenter {

   
    getRawMaterials(page: number, pageSize: number, text?:string): Promise<DTO<PaginatedBaseResponse<IRawMaterialOverview>>> {
        return RepoProvider.rawMaterialRepo.getRawMaterials(page, pageSize,text);
    }

    getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialVariation>>> {
        return RepoProvider.rawMaterialRepo.getVariations(page, pageSize, text, supplierId);
    }
    getRawMaterialById(id: number): Promise<DTO<IRawMaterial>> {
        return RepoProvider.rawMaterialRepo.getRawMaterialById(id);
    }
    onSubmit(payload: IRawMaterialAddRequest): Promise<DTO<null>> {
        return RepoProvider.rawMaterialRepo.create(payload);
    }
    onUpdateRawMaterial(payload: IRawMaterialUpdatePaylod): Promise<DTO<null>> {
        return RepoProvider.rawMaterialRepo.updateRawMaterial(payload);
    }
    onDeleteRawMaterial(ids: number[]): Promise<DTO<null>> {
        return RepoProvider.rawMaterialRepo.deleteRawMaterial(ids);
    }
    onValidate(payload: IRawMaterialVariation): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        console.log(payload);

        const result = RawMaterialUtils.creationSchema.safeParse(payload);
        console.log(result);

        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }

        return errors;
    }


    getItemUnits(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemUnit>>> {
        return RepoProvider.itemUnitRepo.getAll(page, pageSize);
    }
    getItemCategories(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemCategory>>> {
        return RepoProvider.itemCategoryRepo.getAll(page, pageSize);
    }
    addVariation(payload: TAddVariationRequest[]): Promise<DTO<IRawMaterialVariationNested[] | null>> {
        return RepoProvider.rawMaterialRepo.addVariation(payload);
    }
    updateVariation(payload: TUpdateVariationRequest): Promise<DTO<IRawMaterialVariationNested | null>> {
        return RepoProvider.rawMaterialRepo.updateVariation(payload);
    }
    deleteVariation(payload: TDeleteVariationRequest): Promise<DTO<null>> {
        return RepoProvider.rawMaterialRepo.deleteVariation(payload);
    }
    
}