import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import type { IRawMaterialVariation, IRawMaterialAddRequest, IRawMaterialCreatePaylod, IRawMaterialUpdatePaylod, IRawMaterialOverview, IRawMaterial, IRawMaterialVariationNested, TAddVariationRequest, TDeleteVariationRequest, TUpdateVariationRequest } from "../models/IRawMaterial";

export interface IRawMaterialPresenter {
    getRawMaterials(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialOverview>>>;
    getRawMaterialById(id: number): Promise<DTO<IRawMaterial>>;
    onSubmit(payload: IRawMaterialAddRequest): Promise<DTO<null>>;
    onUpdateRawMaterial(payload: IRawMaterialUpdatePaylod): Promise<DTO<null>>;
    onDeleteRawMaterial(ids: number[]): Promise<DTO<null>>;
    getItemUnits(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemUnit>>>;
    getItemCategories(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemCategory>>>;
    addVariation(payload: TAddVariationRequest[]): Promise<DTO<IRawMaterialVariationNested[] | null>>;
    updateVariation(payload: TUpdateVariationRequest): Promise<DTO<IRawMaterialVariationNested | null>>;
    deleteVariation(payload: TDeleteVariationRequest): Promise<DTO<null>>;
    getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialVariation>>>;
}