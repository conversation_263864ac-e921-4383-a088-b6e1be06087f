export interface FetchSuccess<T> {
  success: true;
  message: string;
  data: T | null;
}

export interface FetchError {
  success: false;
  error: true;
  message: string;
  data?: null;
}

export type FetchResult<T> = FetchSuccess<T> | FetchError;

export interface FetchSuccessAll<T> {
  success: boolean;
  message: string;
  data: {
    total: number;
    data: T[];
  };
}

export type FetchResultAll<T> = FetchSuccessAll<T> | FetchError;
