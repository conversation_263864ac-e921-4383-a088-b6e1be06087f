<script lang="ts">
    import { onMount } from 'svelte';
    import type { ProblematicStock, ProblemType } from '$lib/types/debug';
    import { DebugApiService } from '$lib/services/debugApiService';
    import { getProblemTypes, getProblemSeverity } from '$lib/types/debug';

    // Component props
    export let onRefresh: (() => void) | undefined = undefined;

    // Component state
    let stocks: ProblematicStock[] = [];
    let loading = false;
    let error: string | null = null;
    let searchTerm = '';
    let sortColumn: string = '';
    let sortDirection: 'asc' | 'desc' = 'asc';

    // Reactive filtered and sorted stocks
    $: filteredStocks = stocks.filter(stock => 
        stock.rawMaterialName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stock.categoryName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stock.sku.toLowerCase().includes(searchTerm.toLowerCase())
    );

    $: sortedStocks = [...filteredStocks].sort((a, b) => {
        if (!sortColumn) return 0;
        
        const aValue = a[sortColumn as keyof ProblematicStock];
        const bValue = b[sortColumn as keyof ProblematicStock];
        
        if (typeof aValue === 'number' && typeof bValue === 'number') {
            return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
        }
        
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        
        if (sortDirection === 'asc') {
            return aStr < bStr ? -1 : aStr > bStr ? 1 : 0;
        } else {
            return aStr > bStr ? -1 : aStr < bStr ? 1 : 0;
        }
    });

    async function loadProblematicStocks() {
        loading = true;
        error = null;
        
        try {
            const response = await DebugApiService.getProblematicStocks();
            if (response.success && response.data) {
                stocks = response.data;
                if (onRefresh) onRefresh();
            } else {
                error = response.message || 'Failed to load data';
            }
        } catch (err) {
            error = err instanceof Error ? err.message : 'Unknown error occurred';
        } finally {
            loading = false;
        }
    }

    function handleSort(column: string) {
        if (sortColumn === column) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortColumn = column;
            sortDirection = 'asc';
        }
    }

    function getProblemClass(stock: ProblematicStock): string {
        const problems = getProblemTypes(stock);
        const severity = getProblemSeverity(problems);
        
        switch (severity) {
            case 'critical':
                return 'bg-red-50 border-red-200 hover:bg-red-100';
            case 'warning':
                return 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100';
            default:
                return 'bg-white border-gray-200 hover:bg-gray-50';
        }
    }

    function getProblemIcon(problems: ProblemType[]): string {
        if (problems.includes('Negative Total Stock' as ProblemType) || 
            problems.includes('Negative Usable Stock' as ProblemType)) {
            return '❌';
        }
        if (problems.includes('Stock Calculation Mismatch' as ProblemType)) {
            return '⚠️';
        }
        if (problems.includes('Negative Assigned Stock' as ProblemType)) {
            return '🔻';
        }
        return '📊';
    }

    function formatDate(dateString: string | null): string {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function getStockDifference(stock: ProblematicStock): number {
        return stock.totalStock - stock.usableStock - stock.assignedStock;
    }

    onMount(() => {
        loadProblematicStocks();
    });
</script>

<div class="problematic-stocks-container p-6 max-w-7xl mx-auto">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Problematic Stocks Debug</h2>
            <p class="text-sm text-gray-600 mt-1">
                Identify and analyze raw material stock inconsistencies
            </p>
        </div>
        <button 
            on:click={loadProblematicStocks}
            disabled={loading}
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center gap-2"
        >
            {#if loading}
                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Loading...
            {:else}
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            {/if}
        </button>
    </div>

    <!-- Search and Stats -->
    <div class="mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div class="flex-1">
            <input
                bind:value={searchTerm}
                placeholder="Search by material name, category, or SKU..."
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
            />
        </div>
        {#if stocks.length > 0}
            <div class="text-sm text-gray-600 whitespace-nowrap">
                {filteredStocks.length} of {stocks.length} stocks
            </div>
        {/if}
    </div>

    <!-- Content -->
    {#if loading}
        <div class="text-center py-12">
            <div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p class="text-gray-600">Loading problematic stocks...</p>
        </div>
    {:else if error}
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <div class="flex items-center gap-2">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <strong>Error:</strong> {error}
            </div>
        </div>
    {:else if sortedStocks.length === 0}
        <div class="text-center py-12 bg-gray-50 rounded-lg">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">
                {stocks.length === 0 ? 'No problematic stocks found!' : 'No stocks match your search'}
            </h3>
            <p class="text-gray-600">
                {stocks.length === 0 
                    ? 'All stock calculations appear to be consistent.' 
                    : 'Try adjusting your search terms to find specific stocks.'}
            </p>
        </div>
    {:else}
        <!-- Table -->
        <div class="overflow-x-auto bg-white rounded-lg shadow border border-gray-200">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Problems
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" on:click={() => handleSort('rawMaterialName')}>
                            <div class="flex items-center gap-1">
                                Raw Material
                                {#if sortColumn === 'rawMaterialName'}
                                    <span class="text-blue-500">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                {/if}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" on:click={() => handleSort('sku')}>
                            <div class="flex items-center gap-1">
                                SKU
                                {#if sortColumn === 'sku'}
                                    <span class="text-blue-500">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                {/if}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" on:click={() => handleSort('categoryName')}>
                            <div class="flex items-center gap-1">
                                Category
                                {#if sortColumn === 'categoryName'}
                                    <span class="text-blue-500">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                {/if}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" on:click={() => handleSort('totalStock')}>
                            <div class="flex items-center gap-1">
                                Total Stock
                                {#if sortColumn === 'totalStock'}
                                    <span class="text-blue-500">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                {/if}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" on:click={() => handleSort('usableStock')}>
                            <div class="flex items-center gap-1">
                                Usable Stock
                                {#if sortColumn === 'usableStock'}
                                    <span class="text-blue-500">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                {/if}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" on:click={() => handleSort('assignedStock')}>
                            <div class="flex items-center gap-1">
                                Assigned Stock
                                {#if sortColumn === 'assignedStock'}
                                    <span class="text-blue-500">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                {/if}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Stock Difference
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" on:click={() => handleSort('updatedAt')}>
                            <div class="flex items-center gap-1">
                                Last Updated
                                {#if sortColumn === 'updatedAt'}
                                    <span class="text-blue-500">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                {/if}
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {#each sortedStocks as stock (stock.id)}
                        {@const problems = getProblemTypes(stock)}
                        {@const stockDifference = getStockDifference(stock)}
                        <tr class="transition-colors duration-200 {getProblemClass(stock)}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center gap-2">
                                    <span class="text-lg" title={problems.join(', ')}>
                                        {getProblemIcon(problems)}
                                    </span>
                                    <div class="text-xs">
                                        {#each problems as problem}
                                            <div class="text-red-600 font-medium">{problem}</div>
                                        {/each}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{stock.rawMaterialName}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-mono text-gray-900">{stock.sku}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{stock.categoryName}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm font-mono {stock.totalStock < 0 ? 'text-red-600 font-bold' : 'text-gray-900'}">
                                    {stock.totalStock}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm font-mono {stock.usableStock < 0 ? 'text-red-600 font-bold' : 'text-gray-900'}">
                                    {stock.usableStock}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm font-mono {stock.assignedStock < 0 ? 'text-red-600 font-bold' : 'text-gray-900'}">
                                    {stock.assignedStock}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm font-mono {stockDifference !== 0 ? 'text-orange-600 font-bold' : 'text-gray-900'}">
                                    {stockDifference}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {formatDate(stock.updatedAt)}
                            </td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        </div>

        <!-- Legend -->
        <div class="mt-6 bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Problem Types Legend:</h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                <div class="flex items-center gap-2">
                    <span class="text-lg">❌</span>
                    <span class="text-gray-700">Negative Stock Values</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="text-lg">⚠️</span>
                    <span class="text-gray-700">Stock Calculation Mismatch</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="text-lg">🔻</span>
                    <span class="text-gray-700">Negative Assigned Stock</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="text-lg">📊</span>
                    <span class="text-gray-700">General Stock Issues</span>
                </div>
            </div>
        </div>
    {/if}
</div> 