<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import type { LogFilters } from '$lib/types/logs';

    export let filters: LogFilters = {};
    export let loading: boolean = false;
    export let expanded: boolean = false;

    const dispatch = createEventDispatcher<{
        filtersChanged: LogFilters;
        clear: void;
    }>();

    // Local filter state
    let localFilters: LogFilters = { ...filters };

    // Watch for external filter changes
    $: {
        localFilters = { ...filters };
    }

    function handleFilterChange() {
        dispatch('filtersChanged', localFilters);
    }

    function clearAllFilters() {
        localFilters = {};
        dispatch('clear');
    }

    function handleMethodChange(event: Event) {
        const select = event.target as HTMLSelectElement;
        localFilters.method = select.value || undefined;
        handleFilterChange();
    }

    function handleStatusCodeChange(event: Event) {
        const input = event.target as HTMLInputElement;
        localFilters.statusCode = input.value ? parseInt(input.value) : undefined;
        handleFilterChange();
    }

    function handleHasErrorChange(event: Event) {
        const select = event.target as HTMLSelectElement;
        localFilters.hasError = select.value === '' ? undefined : select.value === 'true';
        handleFilterChange();
    }

    function handleUrlChange(event: Event) {
        const input = event.target as HTMLInputElement;
        localFilters.url = input.value || undefined;
        handleFilterChange();
    }

    function handleIpChange(event: Event) {
        const input = event.target as HTMLInputElement;
        localFilters.ip = input.value || undefined;
        handleFilterChange();
    }

    // Check if any filters are active
    $: hasActiveFilters = Object.values(localFilters).some(value => value !== undefined && value !== '');

    // Predefined status codes for quick selection
    const commonStatusCodes = [200, 201, 204, 301, 302, 400, 401, 403, 404, 422, 500, 502, 503];
</script>

<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <!-- Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-100">
        <div class="flex items-center gap-3">
            <h3 class="text-lg font-medium text-gray-900">Filters</h3>
            {#if hasActiveFilters}
                <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                    {Object.values(localFilters).filter(v => v !== undefined && v !== '').length} active
                </span>
            {/if}
        </div>
        
        <div class="flex items-center gap-2">
            {#if hasActiveFilters}
                <button
                    on:click={clearAllFilters}
                    disabled={loading}
                    class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50"
                >
                    Clear all
                </button>
            {/if}
            
            <button
                on:click={() => expanded = !expanded}
                class="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded transition-colors duration-200"
            >
                {expanded ? 'Hide' : 'Show'} filters
                <svg 
                    class="w-4 h-4 transition-transform duration-200 {expanded ? 'rotate-180' : ''}" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Filters Content -->
    {#if expanded}
        <div class="p-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                <!-- HTTP Method Filter -->
                <div class="space-y-2">
                    <label for="method-filter" class="block text-sm font-medium text-gray-700">
                        HTTP Method
                    </label>
                    <select
                        id="method-filter"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                        value={localFilters.method || ''}
                        on:change={handleMethodChange}
                        disabled={loading}
                    >
                        <option value="">All methods</option>
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                        <option value="PATCH">PATCH</option>
                        <option value="HEAD">HEAD</option>
                        <option value="OPTIONS">OPTIONS</option>
                    </select>
                </div>

                <!-- Status Code Filter -->
                <div class="space-y-2">
                    <label for="status-filter" class="block text-sm font-medium text-gray-700">
                        Status Code
                    </label>
                    <div class="relative">
                        <input
                            id="status-filter"
                            type="number"
                            min="100"
                            max="599"
                            placeholder="e.g., 200, 404, 500"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                            value={localFilters.statusCode || ''}
                            on:input={handleStatusCodeChange}
                            disabled={loading}
                        />
                    </div>
                    
                    <!-- Quick status code buttons -->
                    <div class="flex flex-wrap gap-1 mt-2">
                        {#each commonStatusCodes as code}
                            <button
                                on:click={() => {
                                    localFilters.statusCode = code;
                                    handleFilterChange();
                                }}
                                disabled={loading}
                                class="px-2 py-1 text-xs border border-gray-200 rounded hover:bg-gray-50 transition-colors duration-200 {localFilters.statusCode === code ? 'bg-blue-100 border-blue-300 text-blue-700' : 'text-gray-600'}"
                            >
                                {code}
                            </button>
                        {/each}
                    </div>
                </div>

                <!-- Error Status Filter -->
                <div class="space-y-2">
                    <label for="error-filter" class="block text-sm font-medium text-gray-700">
                        Request Status
                    </label>
                    <select
                        id="error-filter"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                        value={localFilters.hasError === undefined ? '' : localFilters.hasError.toString()}
                        on:change={handleHasErrorChange}
                        disabled={loading}
                    >
                        <option value="">All requests</option>
                        <option value="false">Success only</option>
                        <option value="true">Errors only</option>
                    </select>
                </div>

                <!-- URL Filter -->
                <div class="space-y-2">
                    <label for="url-filter" class="block text-sm font-medium text-gray-700">
                        URL Contains
                    </label>
                    <input
                        id="url-filter"
                        type="text"
                        placeholder="e.g., /api/users, auth"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                        value={localFilters.url || ''}
                        on:input={handleUrlChange}
                        disabled={loading}
                    />
                </div>

                <!-- IP Address Filter -->
                <div class="space-y-2">
                    <label for="ip-filter" class="block text-sm font-medium text-gray-700">
                        IP Address
                    </label>
                    <input
                        id="ip-filter"
                        type="text"
                        placeholder="e.g., 192.168, 127.0.0.1"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                        value={localFilters.ip || ''}
                        on:input={handleIpChange}
                        disabled={loading}
                    />
                </div>
            </div>

            <!-- Active Filters Summary -->
            {#if hasActiveFilters}
                <div class="pt-4 border-t border-gray-100">
                    <div class="flex items-center gap-2 text-sm text-gray-600">
                        <span class="font-medium">Active filters:</span>
                        <div class="flex flex-wrap gap-2">
                            {#if localFilters.method}
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                    Method: {localFilters.method}
                                </span>
                            {/if}
                            {#if localFilters.statusCode}
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                                    Status: {localFilters.statusCode}
                                </span>
                            {/if}
                            {#if localFilters.hasError !== undefined}
                                <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                                    {localFilters.hasError ? 'Errors only' : 'Success only'}
                                </span>
                            {/if}
                            {#if localFilters.url}
                                <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                                    URL: {localFilters.url}
                                </span>
                            {/if}
                            {#if localFilters.ip}
                                <span class="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs">
                                    IP: {localFilters.ip}
                                </span>
                            {/if}
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    {/if}
</div> 