<script lang="ts">
    import type { PaginationInfo } from '$lib/types/logs';

    export let pagination: PaginationInfo | null = null;
    export let onPageChange: (page: number) => void = () => {};
    export let onPageSizeChange: (pageSize: number) => void = () => {};
    export let loading: boolean = false;

    const pageSizeOptions = [10, 25, 50, 100, 250];

    function goToPage(page: number) {
        if (pagination && page >= 1 && page <= pagination.totalPages && !loading) {
            onPageChange(page);
        }
    }

    function goToFirstPage() {
        if (pagination && pagination.currentPage > 1 && !loading) {
            onPageChange(1);
        }
    }

    function goToLastPage() {
        if (pagination && pagination.currentPage < pagination.totalPages && !loading) {
            onPageChange(pagination.totalPages);
        }
    }

    function previousPage() {
        if (pagination?.hasPreviousPage && !loading) {
            onPageChange(pagination.currentPage - 1);
        }
    }

    function nextPage() {
        if (pagination?.hasNextPage && !loading) {
            onPageChange(pagination.currentPage + 1);
        }
    }

    function handlePageSizeChange(event: Event) {
        const select = event.target as HTMLSelectElement;
        const newPageSize = parseInt(select.value);
        if (newPageSize && !loading) {
            onPageSizeChange(newPageSize);
        }
    }

    // Generate page numbers to display
    $: pageNumbers = pagination ? generatePageNumbers(pagination.currentPage, pagination.totalPages) : [];

    function generatePageNumbers(currentPage: number, totalPages: number): number[] {
        const delta = 2; // Show 2 pages before and after current page
        const range = [];
        const rangeWithDots = [];

        for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
            range.push(i);
        }

        if (currentPage - delta > 2) {
            rangeWithDots.push(1);
            if (currentPage - delta > 3) {
                rangeWithDots.push(-1); // -1 represents "..."
            }
        } else {
            rangeWithDots.push(1);
        }

        rangeWithDots.push(...range);

        if (currentPage + delta < totalPages - 1) {
            if (currentPage + delta < totalPages - 2) {
                rangeWithDots.push(-1); // -1 represents "..."
            }
            rangeWithDots.push(totalPages);
        } else if (totalPages > 1) {
            rangeWithDots.push(totalPages);
        }

        return rangeWithDots;
    }
</script>

{#if pagination && pagination.totalPages > 1}
    <div class="flex flex-col sm:flex-row items-center justify-between gap-4 py-4 px-6 bg-white border-t border-gray-200">
        <!-- Pagination Info -->
        <div class="flex items-center gap-4 text-sm text-gray-700">
            <div class="flex items-center gap-2">
                <span>Show</span>
                <select 
                    class="border border-gray-300 rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    value={pagination.pageSize}
                    on:change={handlePageSizeChange}
                    disabled={loading}
                >
                    {#each pageSizeOptions as size}
                        <option value={size}>{size}</option>
                    {/each}
                </select>
                <span>entries</span>
            </div>
            
            <div class="hidden sm:block text-gray-500">|</div>
            
            <div>
                Showing {((pagination.currentPage - 1) * pagination.pageSize) + 1} to {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalData)} of {pagination.totalData} entries
            </div>
        </div>

        <!-- Pagination Controls -->
        <div class="flex items-center gap-1">
            <!-- First Page -->
            <button
                on:click={goToFirstPage}
                disabled={pagination.currentPage === 1 || loading}
                class="px-2 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title="First page"
            >
                ⏮
            </button>

            <!-- Previous Page -->
            <button
                on:click={previousPage}
                disabled={!pagination.hasPreviousPage || loading}
                class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                title="Previous page"
            >
                ←
            </button>

            <!-- Page Numbers -->
            <div class="flex items-center gap-1">
                {#each pageNumbers as pageNum}
                    {#if pageNum === -1}
                        <span class="px-3 py-2 text-sm text-gray-400">...</span>
                    {:else}
                        <button
                            on:click={() => goToPage(pageNum)}
                            disabled={loading}
                            class="px-3 py-2 text-sm rounded transition-colors duration-200 {pageNum === pagination.currentPage 
                                ? 'bg-blue-500 text-white' 
                                : 'text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed'}"
                        >
                            {pageNum}
                        </button>
                    {/if}
                {/each}
            </div>

            <!-- Next Page -->
            <button
                on:click={nextPage}
                disabled={!pagination.hasNextPage || loading}
                class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                title="Next page"
            >
                →
            </button>

            <!-- Last Page -->
            <button
                on:click={goToLastPage}
                disabled={pagination.currentPage === pagination.totalPages || loading}
                class="px-2 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title="Last page"
            >
                ⏭
            </button>
        </div>
    </div>
{:else if pagination && pagination.totalData > 0}
    <!-- Show info even when there's only one page -->
    <div class="flex items-center justify-between py-4 px-6 bg-white border-t border-gray-200">
        <div class="text-sm text-gray-700">
            Showing all {pagination.totalData} entries
        </div>
        
        <div class="flex items-center gap-2 text-sm text-gray-700">
            <span>Show</span>
            <select 
                class="border border-gray-300 rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                value={pagination.pageSize}
                on:change={handlePageSizeChange}
                disabled={loading}
            >
                {#each pageSizeOptions as size}
                    <option value={size}>{size}</option>
                {/each}
            </select>
            <span>entries per page</span>
        </div>
    </div>
{/if} 