<script lang="ts">
    import { LogsApiService } from "$lib/services/logsApiService";
    import { showSuccessToast, showErrorToast } from "$lib/common/utils/common-utils";

    // Props (optional - defaults provided)
    export let autoRefresh: boolean = false;
    export let refreshInterval: number = 30000;

    // State
    let healthData: any = null;
    let configData: any = null;
    let isLoadingHealth = false;
    let isLoadingFlush = false;
    let isLoadingReset = false;
    let isLoadingConfig = false;
    let healthError: string | null = null;
    let showConfig = false;
    let showResetDialog = false;

    function getHealthStatusColor(health: string): string {
        switch (health) {
            case 'healthy': return 'text-green-600 bg-green-50';
            case 'warning': return 'text-yellow-600 bg-yellow-50';
            case 'error': return 'text-red-600 bg-red-50';
            default: return 'text-gray-600 bg-gray-50';
        }
    }

    // Simplified formatFileSize function - only handles MB input from API
    const formatFileSize = (totalSizeMB: string | null | undefined): string => {
        const sizeMB = parseFloat(totalSizeMB || "0");
        
        if (sizeMB === 0 || isNaN(sizeMB)) return "0 Bytes";
        
        const sizeBytes = Math.round(sizeMB * 1024 * 1024);
        
        if (sizeBytes < 1024) {
            return `${sizeBytes.toLocaleString()} Bytes`;
        } else if (sizeMB < 1) {
            return `${(sizeBytes / 1024).toFixed(1)} KB`;
        } else {
            return `${sizeMB.toFixed(2)} MB`;
        }
    };

    function formatNumber(num: number, decimals: number = 0): string {
        if (num === null || num === undefined || isNaN(num)) return 'N/A';
        return num.toLocaleString(undefined, { 
            minimumFractionDigits: 0, 
            maximumFractionDigits: decimals 
        });
    }

    function formatPercentage(rate: number): string {
        if (rate === null || rate === undefined || isNaN(rate)) return 'N/A';
        return (rate * 100).toFixed(2) + '%';
    }

    function formatTime(timeMs: number): string {
        if (timeMs === null || timeMs === undefined || isNaN(timeMs)) return 'N/A';
        if (timeMs < 1000) {
            return timeMs.toFixed(1) + 'ms';
        } else {
            return (timeMs / 1000).toFixed(1) + 's';
        }
    }

    async function fetchHealthData() {
        isLoadingHealth = true;
        healthError = null;

        try {
            const response = await LogsApiService.getLoggingHealth();
            healthData = response.data || null;
            
            // Debug logging - backend always sends totalSizeMB
            if (healthData?.fileStats) {
                console.debug('🔍 [LoggingHealth] API Response fileStats:', {
                    totalFiles: healthData.fileStats.totalFiles,
                    totalSizeMB: healthData.fileStats.totalSizeMB,
                    formattedSize: formatFileSize(healthData.fileStats.totalSizeMB),
                    files: healthData.fileStats.files?.length || 0
                });
            }
        } catch (error) {
            healthError = error instanceof Error ? error.message : 'Failed to fetch health data';
            console.error('❌ [LoggingHealth] Error fetching health data:', error);
        } finally {
            isLoadingHealth = false;
        }
    }

    async function handleFlushLogs() {
        isLoadingFlush = true;
        try {
            const response = await LogsApiService.flushLogs();
            const logsProcessed = response.data?.logsProcessed || 0;
            showSuccessToast(`Logs flushed! ${logsProcessed} new entries processed`);
            await fetchHealthData();
        } catch (error) {
            showErrorToast(error instanceof Error ? error.message : 'Failed to flush logs');
        } finally {
            isLoadingFlush = false;
        }
    }

    async function handleResetMetrics() {
        isLoadingReset = true;
        try {
            await LogsApiService.resetLoggingMetrics();
            showSuccessToast('Logging metrics reset successfully');
            showResetDialog = false;
            await fetchHealthData();
        } catch (error) {
            showErrorToast(error instanceof Error ? error.message : 'Failed to reset metrics');
        } finally {
            isLoadingReset = false;
        }
    }

    async function fetchConfigData() {
        isLoadingConfig = true;
        try {
            const response = await LogsApiService.getLoggingConfig();
            configData = response.data || null;
        } catch (error) {
            showErrorToast(error instanceof Error ? error.message : 'Failed to fetch configuration');
        } finally {
            isLoadingConfig = false;
        }
    }

    function toggleConfig() {
        if (!showConfig && !configData) {
            fetchConfigData();
        }
        showConfig = !showConfig;
    }
</script>

<div class="logging-health bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold text-gray-900">Logging System Health</h2>
        
        <button
            on:click={fetchHealthData}
            disabled={isLoadingHealth}
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors duration-200"
        >
            {isLoadingHealth ? "Loading..." : "Check Health"}
        </button>
    </div>

    {#if healthError}
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
            <strong>Error:</strong> {healthError}
        </div>
    {/if}

    {#if healthData}
        <!-- Health Status Indicator -->
        <div class="flex items-center gap-3 mb-4">
            <h3 class="text-lg font-medium text-gray-900">System Status</h3>
            <span class="px-3 py-1 text-sm font-medium rounded-full {getHealthStatusColor(healthData.health || 'unknown')}">
                {(healthData.health || 'unknown').toUpperCase()}
            </span>
        </div>

        <!-- Health Metrics Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
            <div class="bg-blue-50 rounded-lg p-4 min-h-[80px] flex flex-col justify-between">
                <h4 class="text-sm font-medium text-blue-800 mb-2">Total Logs Written</h4>
                <p class="text-xl font-bold text-blue-900 truncate">{formatNumber(healthData.metrics?.totalLogsWritten)}</p>
            </div>
            
            <div class="bg-red-50 rounded-lg p-4 min-h-[80px] flex flex-col justify-between">
                <h4 class="text-sm font-medium text-red-800 mb-2">Log Errors</h4>
                <p class="text-xl font-bold text-red-900 truncate">{formatNumber(healthData.metrics?.totalLogErrors)}</p>
            </div>
            
            <div class="bg-green-50 rounded-lg p-4 min-h-[80px] flex flex-col justify-between">
                <h4 class="text-sm font-medium text-green-800 mb-2">Buffer Flushes</h4>
                <p class="text-xl font-bold text-green-900 truncate">{formatNumber(healthData.metrics?.bufferFlushes)}</p>
            </div>
            
            <div class="bg-purple-50 rounded-lg p-4 min-h-[80px] flex flex-col justify-between">
                <h4 class="text-sm font-medium text-purple-800 mb-2">Avg Flush Time</h4>
                <p class="text-xl font-bold text-purple-900 truncate">{formatTime(healthData.metrics?.averageFlushTime)}</p>
            </div>

            <div class="bg-yellow-50 rounded-lg p-4 min-h-[80px] flex flex-col justify-between">
                <h4 class="text-sm font-medium text-yellow-800 mb-2">Error Rate</h4>
                <p class="text-xl font-bold text-yellow-900 truncate">{formatPercentage(healthData.metrics?.errorRate)}</p>
            </div>
            
            <div class="bg-indigo-50 rounded-lg p-4 min-h-[80px] flex flex-col justify-between">
                <h4 class="text-sm font-medium text-indigo-800 mb-2">Uptime</h4>
                <p class="text-xl font-bold text-indigo-900 truncate">{Math.floor((healthData.metrics?.uptime || 0) / 3600)}h</p>
            </div>
        </div>

        <!-- File Statistics -->
        {#if healthData.fileStats}
            <div class="border-t border-gray-200 pt-4 mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-3">File Statistics</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Total Files:</span>
                        <span class="font-mono text-sm font-medium">{formatNumber(healthData.fileStats.totalFiles)}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Total Size:</span>
                        <div class="flex flex-col items-end">
                            <span class="font-mono text-sm font-medium">
                                {formatFileSize(healthData.fileStats?.totalSizeMB)}
                            </span>
                            <span class="text-xs text-gray-400" title="Raw API value: {healthData.fileStats?.totalSizeMB || '0'} MB">
                                {healthData.fileStats?.totalSizeMB || '0'} MB
                            </span>
                        </div>
                    </div>
                    {#if healthData.fileStats.oldestFile}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Oldest File:</span>
                            <span class="font-mono text-xs text-gray-500">{new Date(healthData.fileStats.oldestFile).toLocaleDateString()}</span>
                        </div>
                    {/if}
                    {#if healthData.fileStats.newestFile}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Newest File:</span>
                            <span class="font-mono text-xs text-gray-500">{new Date(healthData.fileStats.newestFile).toLocaleDateString()}</span>
                        </div>
                    {/if}
                </div>
                
                <!-- Warning for edge cases -->
                {#if healthData.fileStats.totalFiles > 0}
                    {#if !healthData.fileStats.totalSizeMB || parseFloat(healthData.fileStats.totalSizeMB || '0') === 0}
                        <div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <span class="text-sm text-yellow-700">Warning: {healthData.fileStats.totalFiles} files detected but total size appears to be zero</span>
                            </div>
                        </div>
                    {/if}
                {/if}
            </div>
        {/if}

        <!-- Action Buttons -->
        <div class="border-t border-gray-200 pt-4">
            <div class="flex flex-wrap gap-3">
                <button
                    on:click={handleFlushLogs}
                    disabled={isLoadingFlush}
                    class="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:opacity-50 transition-colors duration-200 flex items-center gap-2"
                    title="Force flush pending logs from buffer"
                >
                    <svg class="w-4 h-4 {isLoadingFlush ? 'animate-spin' : ''}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    {isLoadingFlush ? "Flushing..." : "Flush Logs"}
                </button>

                <button
                    on:click={() => showResetDialog = true}
                    disabled={isLoadingReset}
                    class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 transition-colors duration-200"
                    title="Reset all logging metrics"
                >
                    Reset Metrics
                </button>

                <button
                    on:click={toggleConfig}
                    disabled={isLoadingConfig}
                    class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 disabled:opacity-50 transition-colors duration-200"
                    title="View logging configuration"
                >
                    {isLoadingConfig ? "Loading..." : (showConfig ? "Hide Config" : "View Config")}
                </button>
            </div>
        </div>

        <!-- Configuration Display -->
        {#if showConfig && configData}
            <div class="mt-6 bg-gray-50 rounded-lg p-4">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Logging Configuration</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="font-medium text-gray-600">Buffer Size:</span>
                            <span class="font-mono">{formatNumber(configData.bufferSize)}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="font-medium text-gray-600">Flush Interval:</span>
                            <span class="font-mono">{configData.flushInterval ? configData.flushInterval + 'ms' : 'N/A'}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="font-medium text-gray-600">Log Level:</span>
                            <span class="font-mono uppercase">{configData.logLevel || 'N/A'}</span>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="font-medium text-gray-600">Max File Size:</span>
                            <span class="font-mono">{configData.maxFileSize || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="font-medium text-gray-600">Retention Days:</span>
                            <span class="font-mono">{formatNumber(configData.retentionDays)}</span>
                        </div>
                        {#if configData.outputPath}
                            <div class="flex justify-between items-start">
                                <span class="font-medium text-gray-600">Output Path:</span>
                                <span class="font-mono text-xs text-gray-500 text-right max-w-[200px] truncate" title={configData.outputPath}>{configData.outputPath}</span>
                            </div>
                        {/if}
                    </div>
                </div>
                
                {#if configData.enabledFeatures && configData.enabledFeatures.length > 0}
                    <div class="mt-4 pt-3 border-t border-gray-200">
                        <span class="font-medium text-gray-600 text-sm">Enabled Features:</span>
                        <div class="flex flex-wrap gap-2 mt-2">
                            {#each configData.enabledFeatures as feature}
                                <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">{feature}</span>
                            {/each}
                        </div>
                    </div>
                {/if}
            </div>
        {/if}
    {:else if !isLoadingHealth}
        <div class="text-center py-8 text-gray-600">
            Click "Check Health" to view logging system metrics.
        </div>
    {/if}
</div>

<!-- Reset Confirmation Dialog -->
{#if showResetDialog}
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
        <div class="relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Reset Metrics</h3>
            <p class="text-sm text-gray-600 mb-6">
                Are you sure you want to reset all logging metrics? This action cannot be undone and will clear all current performance statistics.
            </p>
            <div class="flex items-center justify-end gap-3">
                <button
                    on:click={() => showResetDialog = false}
                    disabled={isLoadingReset}
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 disabled:opacity-50 transition-colors duration-200"
                >
                    Cancel
                </button>
                <button
                    on:click={handleResetMetrics}
                    disabled={isLoadingReset}
                    class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 transition-colors duration-200 flex items-center gap-2"
                >
                    {#if isLoadingReset}
                        <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Resetting...
                    {:else}
                        Reset Metrics
                    {/if}
                </button>
            </div>
        </div>
    </div>
{/if} 