<script lang="ts">
    import { onMount, onDestroy } from "svelte";
    import { JsonView } from "@zerodevx/svelte-json-view";
    import { logsStore } from "$lib/stores/logsStore";
    import LogsPagination from "$lib/components/common/LogsPagination.svelte";
    import LogsFilters from "$lib/components/common/LogsFilters.svelte";
    import LoggingHealth from "$lib/components/common/LoggingHealth.svelte";
    import type { IMorganLog, LogFilters } from "$lib/types/logs";
    import { isErrorLog, formatTimestamp, getStatusClass, getMethodClass } from "$lib/types/logs";

    // import debug from "debug";

    // Props
    export let autoRefresh: boolean = false;
    export let refreshInterval: number = 30000; // 30 seconds

    // Component state
    let selectedLog: IMorganLog | null = null;
    let showLogModal = false;
    let showFilters = false;
    let showStatistics = true;

    // Auto-refresh timer
    let refreshTimer: NodeJS.Timeout | null = null;

    // Store subscriptions (reactive)
    let availableDates: string[] = [];
    let selectedDate: string = "";
    let logs: IMorganLog[] = [];
    let pagination: any = null;
    let statistics: any = null;
    let loading: boolean = false;
    let error: string | null = null;
    let filters: LogFilters = {};

    function handleDateSelect(date: string) {
        logsStore.setSelectedDate(date);
    }

    function handleFiltersChanged(event: CustomEvent<LogFilters>) {
        logsStore.setFilters(event.detail);
    }

    function handleFiltersClear() {
        logsStore.clearFilters();
    }

    function handlePageChange(page: number) {
        if (selectedDate && pagination) {
            logsStore.fetchPaginatedLogs(selectedDate, page, pagination.pageSize, filters);
        }
    }

    function handlePageSizeChange(pageSize: number) {
        logsStore.changePageSize(pageSize);
    }

    function openLogModal(log: IMorganLog) {
        selectedLog = log;
        showLogModal = true;
    }

    function closeLogModal() {
        selectedLog = null;
        showLogModal = false;
    }

    function handleRefresh() {
        if (selectedDate) {
            logsStore.fetchLogsAndStats(
                selectedDate,
                pagination?.currentPage || 1,
                pagination?.pageSize || 50
            );
        }
    }

    function startAutoRefresh() {
        if (refreshTimer) clearInterval(refreshTimer);
        refreshTimer = setInterval(() => {
            if (selectedDate && !loading) {
                handleRefresh();
            }
        }, refreshInterval);
    }

    function stopAutoRefresh() {
        if (refreshTimer) {
            clearInterval(refreshTimer);
            refreshTimer = null;
        }
    }

    $: if (autoRefresh) {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }

    // Store unsubscribes
    let unsubscribes: (() => void)[] = [];

    onMount(async () => {
        // Subscribe to store changes
        unsubscribes = [
            logsStore.availableDates.subscribe((val) => (availableDates = val)),
            logsStore.selectedDate.subscribe((val) => (selectedDate = val)),
            logsStore.logs.subscribe((val) => (logs = val)),
            logsStore.pagination.subscribe((val) => (pagination = val)),
            logsStore.statistics.subscribe((val) => (statistics = val)),
            logsStore.loading.subscribe((val) => (loading = val)),
            logsStore.error.subscribe((val) => (error = val)),
            logsStore.filters.subscribe((val) => (filters = val)),
        ];

        await logsStore.fetchAvailableDates();
    });

    onDestroy(() => {
        stopAutoRefresh();
        unsubscribes.forEach((unsubscribe) => unsubscribe());
    });
</script>

<div class="logs-dashboard mx-auto">
    <!-- Header -->
    <div
        class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 bg-gradient-to-r from-primary via-primary-light to-white p-6 text-gray-700 border-[0.5px] border-gray-100 "
    >
        <div>
            <h1 class="text-3xl font-bold text-white">Server Logs Dashboard</h1>
            <p class="text-sm text-gray-200 mt-1">
                Analyze and monitor server logs with comprehensive statistics and filtering
            </p>
        </div>

        <div class="flex items-center gap-3">
            <!-- Auto-refresh toggle -->
            <label class="flex items-center gap-2 text-sm font-medium">
                <input type="checkbox" bind:checked={autoRefresh} class="rounded" />
                Auto-refresh ({refreshInterval / 1000}s)
            </label>

            <!-- Manual refresh button -->
            <button
                on:click={handleRefresh}
                disabled={loading || !selectedDate}
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center gap-2"
            >
                <svg
                    class="w-4 h-4 {loading ? 'animate-spin' : ''}"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    ></path>
                </svg>
                {loading ? "Refreshing..." : "Refresh"}
            </button>
        </div>
    </div>

    <div class="p-6">
        <!-- Date Selector -->
        <div class="mb-6">
            <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                Select Date:
            </label>
            <select
                id="date"
                value={selectedDate}
                on:change={(e) => handleDateSelect((e.target as HTMLSelectElement)?.value)}
                class="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                disabled={loading}
            >
                <option value="">Choose a date...</option>
                {#each availableDates as date}
                    <option value={date}>{date}</option>
                {/each}
            </select>
        </div>

        <!-- Error Display -->
        {#if error}
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center gap-2">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clip-rule="evenodd"
                        ></path>
                    </svg>
                    <strong>Error:</strong>
                    {error}
                </div>
            </div>
        {/if}

        <!-- Logging Health Section -->
        <LoggingHealth {autoRefresh} {refreshInterval} />

        <!-- Statistics Section -->
        {#if statistics}
            <div class="bg-blue-300/30 rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">
                        Statistics for {statistics.date}
                    </h2>
                    <button
                        on:click={() => (showStatistics = false)}
                        class="text-gray-400 hover:text-gray-600"
                        aria-label="Hide statistics"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"
                            ></path>
                        </svg>
                    </button>
                </div>

                <!-- Stats Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-blue-800">Total Requests</h3>
                        <p class="text-2xl font-bold text-blue-900">{statistics.totalRequests}</p>
                    </div>
                    <div class="bg-red-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-red-800">Error Count</h3>
                        <p class="text-2xl font-bold text-red-900">{statistics.errorCount}</p>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-green-800">Avg Response Time</h3>
                        <p class="text-2xl font-bold text-green-900">
                            {statistics.averageResponseTime}ms
                        </p>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-purple-800">Unique IPs</h3>
                        <p class="text-2xl font-bold text-purple-900">{statistics.topIPs.length}</p>
                    </div>
                </div>

                <!-- Detailed Stats -->
                <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    <!-- Requests by Method -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Requests by Method</h4>
                        <div class="space-y-2">
                            {#each Object.entries(statistics.requestsByMethod || {}) as [method, count]}
                                <div class="flex justify-between items-center">
                                    <span
                                        class="px-2 py-1 text-xs font-medium rounded {getMethodClass(
                                            method
                                        )}"
                                    >
                                        {method}
                                    </span>
                                    <span class="font-mono text-sm">{count}</span>
                                </div>
                            {/each}
                        </div>
                    </div>

                    <!-- Requests by Status -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Requests by Status</h4>
                        <div class="space-y-2">
                            {#each Object.entries(statistics.requestsByStatus || {}) as [status, count]}
                                <div class="flex justify-between items-center">
                                    <span
                                        class="font-mono text-sm {getStatusClass(parseInt(status))}"
                                    >
                                        {status}
                                    </span>
                                    <span class="font-mono text-sm">{count}</span>
                                </div>
                            {/each}
                        </div>
                    </div>

                    <!-- Top IPs -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Top IP Addresses</h4>
                        <div class="space-y-2 max-h-48 overflow-y-auto">
                            {#each statistics.topIPs || [] as ipData}
                                <div class="flex justify-between items-center">
                                    <span class="font-mono text-xs text-gray-600 truncate">
                                        {ipData.ip}
                                    </span>
                                    <span class="font-mono text-sm">{ipData.count}</span>
                                </div>
                            {/each}
                        </div>
                    </div>
                </div>

                <!-- Additional Stats -->
                <div class="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Slowest Requests -->
                    {#if statistics.slowestRequests && statistics.slowestRequests.length > 0}
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Slowest Requests</h4>
                            <div class="space-y-2 max-h-48 overflow-y-auto">
                                {#each statistics.slowestRequests || [] as request}
                                    <div class="bg-gray-50 rounded p-2">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1 min-w-0">
                                                <span
                                                    class="px-2 py-1 text-xs font-medium rounded {getMethodClass(
                                                        request.method
                                                    )} mr-2"
                                                >
                                                    {request.method}
                                                </span>
                                                <span
                                                    class="text-xs text-gray-600 truncate block mt-1"
                                                >
                                                    {request.url}
                                                </span>
                                            </div>
                                            <div class="text-right ml-2">
                                                <div
                                                    class="font-mono text-sm font-bold text-red-600"
                                                >
                                                    {request.responseTime}ms
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    {new Date(request.timestamp).toLocaleTimeString(
                                                        "en-US",
                                                        {
                                                            hour: "numeric",
                                                            minute: "2-digit",
                                                            second: "2-digit",
                                                            hour12: true,
                                                        }
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {/each}
                            </div>
                        </div>
                    {/if}

                    <!-- Error Distribution -->
                    {#if statistics.errorDistribution && Object.keys(statistics.errorDistribution || {}).length > 0}
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">
                                Error Distribution
                            </h4>
                            <div class="space-y-2">
                                {#each Object.entries(statistics.errorDistribution || {}) as [errorType, count]}
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-red-600">{errorType}</span>
                                        <span class="font-mono text-sm">{count}</span>
                                    </div>
                                {/each}
                            </div>
                        </div>
                    {/if}
                </div>

                <!-- Time Range -->
                {#if statistics.timeRange}
                    <div class="mt-6 bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Time Range</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Start:</span>
                                <span class="font-mono ml-2">
                                    {new Date(statistics.timeRange.start).toLocaleString("en-US", {
                                        year: "numeric",
                                        month: "2-digit",
                                        day: "2-digit",
                                        hour: "numeric",
                                        minute: "2-digit",
                                        second: "2-digit",
                                        hour12: true,
                                    })}
                                </span>
                            </div>
                            <div>
                                <span class="text-gray-600">End:</span>
                                <span class="font-mono ml-2">
                                    {new Date(statistics.timeRange.end).toLocaleString("en-US", {
                                        year: "numeric",
                                        month: "2-digit",
                                        day: "2-digit",
                                        hour: "numeric",
                                        minute: "2-digit",
                                        second: "2-digit",
                                        hour12: true,
                                    })}
                                </span>
                            </div>
                        </div>
                    </div>
                {/if}
            </div>
        {/if}

        <!-- Filters -->
        <LogsFilters
            {filters}
            {loading}
            bind:expanded={showFilters}
            on:filtersChanged={handleFiltersChanged}
            on:clear={handleFiltersClear}
        />

        <!-- Logs Table -->
        {#if logs.length > 0}
            <div class="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
                <div class="max-h-[55vh] overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Timestamp
                                </th>
                                <th
                                    scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Method
                                </th>
                                <th
                                    scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    URL
                                </th>
                                <th
                                    scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Status
                                </th>
                                <th
                                    scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Response Time
                                </th>
                                <th
                                    scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    IP
                                </th>
                                <th
                                    scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Error
                                </th>
                                <th
                                    scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each logs as log (log.id)}
                                <tr
                                    class="hover:bg-gray-50 transition-colors duration-200 {isErrorLog(
                                        log
                                    )
                                        ? 'bg-red-50'
                                        : ''}"
                                >
                                    <td
                                        class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono"
                                    >
                                        {formatTimestamp(log.timestamp)}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span
                                            class="px-2 py-1 text-xs font-medium rounded {getMethodClass(
                                                log.request.method
                                            )}"
                                        >
                                            {log.request.method}
                                        </span>
                                    </td>
                                    <td
                                        class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate"
                                        title={log.request.url}
                                    >
                                        {log.request.url}
                                    </td>
                                    <td
                                        class="px-6 py-4 whitespace-nowrap text-sm font-mono {getStatusClass(
                                            log.response.status
                                        )}"
                                    >
                                        {log.response.status}
                                    </td>
                                    <td
                                        class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900"
                                    >
                                        {log.response.responseTime}
                                    </td>
                                    <td
                                        class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900"
                                    >
                                        {log.ip}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        {isErrorLog(log) ? "❌" : "✅"}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <button
                                            on:click={() => openLogModal(log)}
                                            class="text-blue-600 hover:text-blue-800 font-medium"
                                        >
                                            View
                                        </button>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination Info and Controls -->
                {#if pagination}
                    <div class="mb-4">
                        <!-- Pagination Info -->
                        <div
                            class="flex flex-col sm:flex-row justify-between items-center border-t border-gray-200 border-dashed py-2 sm:items-center gap-4 px-6"
                        >
                            <div class="text-sm text-gray-600 mr-4">
                                Showing {(pagination.currentPage - 1) * pagination.pageSize +
                                    1}-{Math.min(
                                    pagination.currentPage * pagination.pageSize,
                                    pagination.totalData
                                )}
                                of {pagination.totalData.toLocaleString()} entries
                                {#if Object.keys(filters).length > 0}
                                    <span class="text-blue-600 font-medium">(filtered)</span>
                                {/if}
                            </div>

                            <!-- Page Size Selector -->
                            <div class="flex items-center gap-2">
                                <label for="pageSize" class="text-sm text-gray-600">Show:</label>
                                <select
                                    id="pageSize"
                                    bind:value={pagination.pageSize}
                                    on:change={() => handlePageSizeChange(pagination.pageSize)}
                                    class="border border-gray-300 rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    disabled={loading}
                                >
                                    <option value={25}>25</option>
                                    <option value={50}>50</option>
                                    <option value={100}>100</option>
                                    <option value={200}>200</option>
                                </select>
                                <span class="text-sm text-gray-600">per page</span>
                            </div>
                        </div>

                        <!-- Pagination Controls -->
                        <LogsPagination
                            {pagination}
                            {loading}
                            onPageChange={handlePageChange}
                            onPageSizeChange={handlePageSizeChange}
                        />
                    </div>
                {/if}
            </div>
        {:else if selectedDate && !loading}
            <div class="text-center py-12 bg-gray-50 rounded-lg">
                <svg
                    class="w-12 h-12 text-gray-400 mx-auto mb-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    ></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No logs found</h3>
                <p class="text-gray-600">
                    {Object.keys(filters).length > 0
                        ? "No logs match your current filters."
                        : `No log entries were found for ${selectedDate}.`}
                </p>
                {#if Object.keys(filters).length > 0}
                    <button
                        on:click={handleFiltersClear}
                        class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
                    >
                        Clear Filters
                    </button>
                {/if}
            </div>
        {:else if loading}
            <div class="text-center py-12 bg-gray-50 rounded-lg">
                <div
                    class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"
                ></div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Loading logs...</h3>
                <p class="text-gray-600">Please wait while we fetch the data.</p>
            </div>
        {/if}
    </div>
</div>

<!-- Log Details Modal -->
{#if showLogModal && selectedLog}
    <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
    <div
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4"
        on:click={closeLogModal}
        on:keydown={(e) => {
            if (e.key === "Escape" || e.key === "Enter" || e.key === " ") {
                closeLogModal();
            }
        }}
        role="button"
        tabindex="0"
        aria-label="Close modal"
    >
        <div
            class="relative mx-auto p-5 border w-full max-w-6xl max-h-[90vh] shadow-lg rounded-md bg-white overflow-hidden flex flex-col"
            role="dialog"
            on:click|stopPropagation
            on:keydown|stopPropagation
        >
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Log Entry Details</h3>
                <button
                    on:click={closeLogModal}
                    class="text-gray-400 hover:text-gray-600"
                    aria-label="Close modal"
                >
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"
                        ></path>
                    </svg>
                </button>
            </div>

            <!-- Log Overview -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <span class="text-xs font-medium text-gray-500">Timestamp</span>
                        <p class="font-mono text-sm">{formatTimestamp(selectedLog.timestamp)}</p>
                    </div>
                    <div>
                        <span class="text-xs font-medium text-gray-500">Method</span>
                        <p>
                            <span
                                class="px-2 py-1 text-xs font-medium rounded {getMethodClass(
                                    selectedLog.request.method
                                )}"
                            >
                                {selectedLog.request.method}
                            </span>
                        </p>
                    </div>
                    <div>
                        <span class="text-xs font-medium text-gray-500">Status</span>
                        <p class="font-mono text-sm {getStatusClass(selectedLog.response.status)}">
                            {selectedLog.response.status}
                        </p>
                    </div>
                    <div>
                        <span class="text-xs font-medium text-gray-500">Response Time</span>
                        <p class="font-mono text-sm">{selectedLog.response.responseTime}</p>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="text-xs font-medium text-gray-500">URL</span>
                    <p class="font-mono text-sm break-all">{selectedLog.request.url}</p>
                </div>
                <div class="mt-3">
                    <span class="text-xs font-medium text-gray-500">IP Address</span>
                    <p class="font-mono text-sm">{selectedLog.ip}</p>
                </div>
            </div>

            <!-- Detailed Information -->
            <div class="space-y-6 overflow-y-auto flex-1 min-h-0">
                <!-- Request Details -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                        <svg
                            class="w-4 h-4 mr-2 text-blue-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M7 16l-4-4m0 0l4-4m-4 4h18"
                            ></path>
                        </svg>
                        Request Details
                    </h4>
                    <div class="bg-blue-50 rounded-lg p-4">
                        <JsonView json={selectedLog.request} />
                    </div>
                </div>

                <!-- Response Details -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                        <svg
                            class="w-4 h-4 mr-2 text-green-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M17 8l4 4m0 0l-4 4m4-4H3"
                            ></path>
                        </svg>
                        Response Details
                    </h4>
                    <div class="bg-green-50 rounded-lg p-4">
                        <JsonView json={selectedLog.response} />
                    </div>
                </div>

                <!-- Error Details -->
                {#if selectedLog.error}
                    <div>
                        <h4 class="font-medium text-red-900 mb-3 flex items-center">
                            <svg
                                class="w-4 h-4 mr-2 text-red-500"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                ></path>
                            </svg>
                            Error Details
                        </h4>
                        <div class="bg-red-50 rounded-lg p-4 border border-red-200">
                            <JsonView json={selectedLog.error} />
                        </div>
                    </div>
                {/if}
            </div>

            <!-- Modal Footer -->
            <div class="mt-6 flex justify-end">
                <button
                    on:click={closeLogModal}
                    class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200"
                >
                    Close
                </button>
            </div>
        </div>
    </div>
{/if}
