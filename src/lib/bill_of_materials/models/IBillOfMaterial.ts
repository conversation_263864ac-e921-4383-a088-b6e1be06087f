import type { IFinalGoodsVariation } from "$lib/final_goods/models/IFinalGoods";
import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";

// Base interfaces for BOM
interface IBillOfMaterial {
    id: number;
    finalGoodsVariation: IFinalGoodsVariation|null;
    rawMaterialVariations: IBillOfMaterialRawMaterialVariation[];
    createdAt: Date;
}

interface IBillOfMaterialRawMaterialVariation {
    id: number;
    bomId: number;
    rawMaterialVariation: IRawMaterialVariation|null;
    qty: number;
}

// Overview interface for table display
interface IBillOfMaterialOverview {
    id: number,
    finalGoodsVariation: {
        id: number,
        name: string,
    },
    createdBy: {
        id: number,
        firstName: string,
        lastName: string
    },
    createdAt: Date,
}

// Request interfaces for API calls
interface IBillOfMaterialAddRequest {
    finalGoodsVariationId: number;
    rawMaterials: IBillOfMaterialRawMaterialRequest[];
}

interface IBillOfMaterialRawMaterialRequest {
    rawMaterialVariationId: number;
    qty: number;
}

interface IBillOfMaterialUpdateRequest extends IBillOfMaterialAddRequest {
    id: number;
}

// Form state interfaces
interface IBillOfMaterialFormState extends Omit<IBillOfMaterial, "id" | "createdAt"> {
   
}


interface IBillOfMaterialEditFormState extends IBillOfMaterialFormState {
    id: number;
}

export type {
    IBillOfMaterial,
    IBillOfMaterialOverview,
    IBillOfMaterialAddRequest,
    IBillOfMaterialRawMaterialRequest,
    IBillOfMaterialUpdateRequest,
    IBillOfMaterialFormState,
    IBillOfMaterialEditFormState,
    IBillOfMaterialRawMaterialVariation,
    
};
