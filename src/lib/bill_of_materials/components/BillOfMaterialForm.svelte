<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input } from "flowbite-svelte";
    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import type {
        IBillOfMaterialAddRequest,
        IBillOfMaterialFormState,
        IBillOfMaterialRawMaterialVariation,
    } from "../models/IBillOfMaterial";
    import { BillOfMaterialUtils } from "../utils/BillOfMaterialUtils";
    import { onMount } from "svelte";
    import type { ValidationErrors } from "$lib/common/utils/types";
    import FinalGoodsVariationSearch from "./FinalGoodsVariationSearch.svelte";
    import RawMaterialVariationSearch from "./RawMaterialVariationSearch.svelte";
    import { TrashBinSolid } from "flowbite-svelte-icons";

    export let formState: IBillOfMaterialFormState = BillOfMaterialUtils.emptyFormState;
    export let isInsideModal: boolean = false;
    export let onSubmitSuccess: ((data: any) => void) | null = null;

    let isDoingTask: boolean = false;
    let validationErrors: ValidationErrors = new Map();


    const addRawMaterial = () => {
        const newRawMaterial: IBillOfMaterialRawMaterialVariation = {
            id: -1,
            bomId:-1,
            rawMaterialVariation: null,
            qty: 0,
        };
        formState.rawMaterialVariations = [...formState.rawMaterialVariations, newRawMaterial];
    };

    const removeRawMaterial = (index: number) => {
        formState.rawMaterialVariations = formState.rawMaterialVariations.filter((_, i) => i !== index);
    };

    const handleSubmit = async () => {
        const payload: IBillOfMaterialAddRequest = {
            finalGoodsVariationId: formState.finalGoodsVariation?.id ?? -1,
            rawMaterials: formState.rawMaterialVariations.map((item) => ({
                rawMaterialVariationId: item.rawMaterialVariation?.id ?? -1,
                qty: item.qty,
            })),
        };

        validationErrors = PresenterProvider.billOfMaterialPresenter.onValidate(payload);

        if (validationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }

        isDoingTask = true;
        try {
            const res = await PresenterProvider.billOfMaterialPresenter.onSubmit(payload);
            if (res.success) {
                showSuccessToast("Bill of Material created successfully");
                if (onSubmitSuccess) {
                    onSubmitSuccess(res.data);
                } else {
                    await goto("/admin/bill-of-materials");
                }
            } else {
                showErrorToast(res.message);
            }
        } catch (error) {
            showErrorToast("An error occurred while creating the bill of material");
        }
        isDoingTask = false;
    };

    onMount(() => {
        // Initialize with one empty raw material if none exist
        if (formState.rawMaterialVariations.length === 0) {
            addRawMaterial();
        }
    });
</script>

<div class="flex items-center justify-center">
    <div class="w-[90vw] p-2">
        {#if !isInsideModal}
            <div class="flex items-center justify-between py-2">
                <FormHeader label="Add Bill of Material" />
                <BreadCrumbs breadCrumbData={[]} />
            </div>
            <hr class="mb-5" />
        {/if}

        <div class="space-y-6">
            <!-- Final Goods Variation Selection -->
            <div>
                <FinalGoodsVariationSearch
                    labelText="Final Goods Variation"
                    selected={formState.finalGoodsVariation}
                    onSelected={(data) => {
                        formState.finalGoodsVariation = data;
                    }}
                    errorMap={validationErrors}
                    fieldName="finalGoodsVariationId"
                />
                {#if validationErrors.has("finalGoodsVariationId")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("finalGoodsVariationId")}
                    </p>
                {/if}
            </div>

            <!-- Raw Materials Section -->
            <div>
                <div class="flex items-center justify-between mb-4">
                    <Label class="text-lg font-semibold">Raw Materials</Label>
                    <CustomButton
                        title="Add Raw Material"
                        cssClass="bg-blue-600 hover:bg-blue-700"
                        onClick={addRawMaterial}
                    />
                </div>

                {#if validationErrors.has("rawMaterials")}
                    <p class="mb-4 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("rawMaterials")}
                    </p>
                {/if}

                <div class="space-y-4">
                    {#each formState.rawMaterialVariations as rawMaterial, index}
                        <div class="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-medium">Raw Material {index + 1}</h4>
                                {#if formState.rawMaterialVariations.length > 1}
                                    <button
                                        type="button"
                                        class="text-red-600 hover:text-red-800"
                                        on:click={() => removeRawMaterial(index)}
                                    >
                                        <TrashBinSolid class="w-5 h-5" />
                                    </button>
                                {/if}
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <!-- Raw Material Variation Selection -->
                                <div class="md:col-span-2">
                                    <RawMaterialVariationSearch
                                        labelText="Raw Material Variation"
                                        selected={rawMaterial.rawMaterialVariation}
                                        onSelected={(data) => {
                                            formState.rawMaterialVariations[index].rawMaterialVariation =
                                                data;
                                            formState.rawMaterialVariations = formState.rawMaterialVariations; // Trigger reactivity
                                        }}
                                        errorMap={validationErrors}
                                        fieldName={`rawMaterials.${index}.rawMaterialVariationId`}
                                    />
                                    {#if validationErrors.has(`rawMaterials.${index}.rawMaterialVariationId`)}
                                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                            {validationErrors.get(
                                                `rawMaterials.${index}.rawMaterialVariationId`
                                            )}
                                        </p>
                                    {/if}
                                </div>

                                <!-- Quantity Input -->
                                <div>
                                    <Label
                                        for="qty-{index}"
                                        class="mb-2 font-sans capitalize tracking-[0px]"
                                    >
                                        Quantity
                                        {#if rawMaterial.rawMaterialVariation?.unit.name}
                                            <span class="text-sm text-gray-500">
                                                ({rawMaterial.rawMaterialVariation.unit.name})
                                            </span>
                                        {/if}
                                        {#if validationErrors.has(`rawMaterials.${index}.qty`)}
                                            <span class="text-red-600">*</span>
                                        {/if}
                                    </Label>
                                    <Input
                                        type="number"
                                        id="qty-{index}"
                                        placeholder="Enter quantity"
                                        min="0"
                                        step="0.01"
                                        class="dark:bg-primary-700 {validationErrors.has(
                                            `rawMaterials.${index}.qty`
                                        )
                                            ? 'border-red-500'
                                            : ''}"
                                        bind:value={rawMaterial.qty}
                                    />
                                    {#if validationErrors.has(`rawMaterials.${index}.qty`)}
                                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                            {validationErrors.get(`rawMaterials.${index}.qty`)}
                                        </p>
                                    {/if}
                                </div>
                            </div>
                        </div>
                    {/each}
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end pt-6">
                <CustomButton
                    onClick={handleSubmit}
                    cssClass="w-32 bg-black"
                    title="Save"
                    isLoading={isDoingTask}
                />
            </div>
        </div>
    </div>
</div>
