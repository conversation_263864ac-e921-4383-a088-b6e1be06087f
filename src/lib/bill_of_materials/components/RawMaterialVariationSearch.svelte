<script lang="ts">
    import SearchWithDrop from "$lib/common/components/SearchWithDrop.svelte";
    import { debounce } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";

    export let isLabel: boolean = true;
    export let labelText: string = "Raw Material Variation";
    export let selected: IRawMaterialVariation | null = null;
    export let onSelected: (data: IRawMaterialVariation | null) => void;
    export let disabled: boolean = false;
    export let errorMap: Map<string, string> | null = null;
    export let fieldName: string = "rawMaterialVariationId";

    let searchTerm: string = "";
    let fetchedData: IRawMaterialVariation[] = [];
    let doingSearch: boolean = false;

    const getData = async () => {
        if (!searchTerm.trim()) {
            fetchedData = [];
            return;
        }

        doingSearch = true;
        try {
            const res = await PresenterProvider.billOfMaterialPresenter.searchRawMaterialVariations(1, 20, searchTerm);
            if (res.success && res.data) {
                fetchedData = res.data.data;
            } else {
                fetchedData = [];
            }
        } catch (error) {
            console.error("Error searching raw material variations:", error);
            fetchedData = [];
        }
        doingSearch = false;
    };

    const debounceSearch = debounce(getData, 600);

    const doSearch = (search: string) => {
        searchTerm = search;
        debounceSearch();
    };

    const getSelectedDisplayText = (): string | null => {
        if (!selected) return null;
        return `${selected.name} (${selected.sku})`;
    };

    // Transform data to include display text for SearchWithDrop
    $: transformedData = fetchedData.map(item => ({
        ...item,
        displayText: `${item.name} (${item.sku})`
    }));
</script>

<SearchWithDrop
    {isLabel}
    {disabled}
    {errorMap}
    {fieldName}
    label={labelText}
    bind:searchTerm
    level="displayText"
    loading={doingSearch}
    placeholder="Search raw material variation..."
    searchInput={doSearch}
    selected={getSelectedDisplayText()}
    selectedObj={selected}
    selectedFunc={(data) => {
        fetchedData = [];
        searchTerm = "";
        onSelected(data);
        // selected = data;
    }}
    bind:filteredData={transformedData}
/>
