<script lang="ts">
    import { Spinner } from "flowbite-svelte";
    import {
        capitalizeFirstWord,
        debounce,
        showErrorToast,
        showSuccessToast,
    } from "$lib/common/utils/common-utils";
    import { onMount } from "svelte";
    import { goto } from "$app/navigation";
    import Loader from "$lib/common/components/Loader.svelte";
    import type { IBillOfMaterialOverview } from "../models/IBillOfMaterial";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { openConfirmDialog } from "$lib/common/utils/custom_confirmation_component_store";

    export let paginationData: PaginatedDataWrapper<IBillOfMaterialOverview>;
    export let searchTerm: string = "";
    export let onSearchClear: () => void = () => {};
    export let selectedRowsMap: Map<number, IBillOfMaterialOverview> = new Map();

    let isLoading: boolean = false;
    let isSearching: boolean = false;
    let filteredData: IBillOfMaterialOverview[] = [];
    let alreadyFetchedData: IBillOfMaterialOverview[] = [];
    let selectAll: boolean = false;
    let isDeleting: boolean = false;

    const headers = [
        { key: "finalGoodsVariationName", label: "Final Goods Variation", format: capitalizeFirstWord },
        { key: "createdBy", label: "Created By", format: capitalizeFirstWord },
        { key: "createdAt", label: "Created Date", format: (date: Date) => new Date(date).toLocaleDateString() },
    ];

    let debounceSearch = debounce(async (e: any) => {
        if (searchTerm.trim().length === 0) {
            paginationData.searchText = undefined;
            onSearchClear();
            return;
        }
        if (e.target.value.trim().length > 2) {
            searchTerm = e.target.value.trim();
            paginationData.searchText = searchTerm;
            // filteredData = paginationData.pagination.data.filter((data) =>
            //     data.finalGoodsVariationName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            //     data.finalGoodsName.toLowerCase().includes(searchTerm.toLowerCase())
            // );
            filteredData =[];
            if (filteredData.length === 0) {
                isSearching = true;
                const result = await PresenterProvider.billOfMaterialPresenter.getBillOfMaterials(
                    1,
                    paginationData.pageSize,
                    e.target.value.trim()
                );
                if (!result.success) {
                    return showErrorToast(result.message);
                } else {
                    filteredData = result.data.data;
                    isSearching = false;
                }
            }
        } else {
            filteredData = alreadyFetchedData;
        }
    }, 300);

    const toggleRowSelection = (row: IBillOfMaterialOverview, id: number) => {
        if (selectedRowsMap.has(id)) {
            selectedRowsMap.delete(id);
        } else {
            selectedRowsMap.set(id, row);
        }
        selectedRowsMap = selectedRowsMap;
        selectAll = selectedRowsMap.size === filteredData.length;
    };

    const toggleSelectAll = () => {
        if (selectAll) {
            selectedRowsMap.clear();
        } else {
            filteredData.forEach((row) => {
                selectedRowsMap.set(row.id, row);
            });
        }
        selectedRowsMap = selectedRowsMap;
    };

    const handleDelete = async () => {
        if (selectedRowsMap.size === 0) {
            showErrorToast("Please select at least one item to delete");
            return;
        }

        openConfirmDialog({
            heading: "Delete Bill of Materials",
            body: `Are you sure you want to delete ${selectedRowsMap.size} bill(s) of materials? This action cannot be undone.`,
            positiveButtonText: "Delete",
            negativeButtonText: "Cancel",
            positiveAction: async () => {
                isDeleting = true;
                try {
                    const ids = Array.from(selectedRowsMap.keys());
                    const result = await PresenterProvider.billOfMaterialPresenter.onDeleteBillOfMaterial(ids);

                    if (result.success) {
                        showSuccessToast(`Successfully deleted ${ids.length} bill(s) of materials`);
                        selectedRowsMap.clear();
                        selectedRowsMap = selectedRowsMap;
                        selectAll = false;
                        // Refresh the data
                        onSearchClear();
                    } else {
                        showErrorToast(result.message);
                    }
                } catch (error) {
                    showErrorToast("An error occurred while deleting");
                }
                isDeleting = false;
            }
        });
    };

    onMount(() => {
        
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;
    });

   
</script>

{#if isLoading}
    <div class="flex h-[30rem] w-full items-center justify-center">
        <Loader />
    </div>
{:else}
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <div class="flex flex-col items-center bg-gray-200 justify-between space-y-3 p-4 md:flex-row md:space-x-4 md:space-y-0">
            <div class="w-full md:w-1/2">
                <form class="flex items-center">
                    <label for="simple-search" class="sr-only">Search</label>
                    <div class="relative w-full">
                        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                            <img src="/images/svg/search.svg" alt="search" width="15px" />
                        </div>
                        <input
                            type="text"
                            id="simple-search"
                            bind:value={searchTerm}
                            on:input={debounceSearch}
                            class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2 pl-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                            placeholder="Search bill of materials..."
                        />
                        {#if isSearching}
                            <div class="absolute right-0 top-0 bottom-0 flex items-center justify-center">
                                <Loader />
                            </div>
                        {/if}
                    </div>
                </form>
            </div>
            <div class="flex w-full flex-shrink-0 flex-col items-stretch justify-end space-y-2 md:w-auto md:flex-row md:items-center md:space-x-3 md:space-y-0">
                <button
                    type="button"
                    class="flex items-center justify-center rounded-lg bg-primary-500 px-4 py-2 text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                    on:click={() => goto("/admin/bill-of-materials/add")}
                >
                    <svg class="mr-2 h-3.5 w-3.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    Add BOM
                </button>
                {#if selectedRowsMap.size > 0}
                    <button
                        type="button"
                        class="flex items-center justify-center rounded-lg bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-4 focus:ring-red-300 dark:bg-red-500 dark:hover:bg-red-600 dark:focus:ring-red-900"
                        on:click={handleDelete}
                        disabled={isDeleting}
                    >
                        {#if isDeleting}
                            <Spinner class="mr-2 h-3.5 w-3.5" />
                        {:else}
                            <svg class="mr-2 h-3.5 w-3.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        {/if}
                        Delete ({selectedRowsMap.size})
                    </button>
                {/if}
            </div>
        </div>

        {#if isSearching}
            <div class="flex h-[30rem] w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <div class="max-h-[48vh] overflow-y-auto">
                <table class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
                    <thead class="bg-primary-500 font-primary text-base uppercase text-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th class="px-4 py-3">
                                <input
                                    type="checkbox"
                                    bind:checked={selectAll}
                                    on:change={toggleSelectAll}
                                />
                            </th>
                            <th scope="col" class="px-6 py-3">SR No.</th>
                            {#each headers as heading}
                                <th scope="col" class="px-6 py-3">{heading.label}</th>
                            {/each}
                        </tr>
                    </thead>
                    <tbody>
                        {#each filteredData as row, index}
                            <tr class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600">
                                <td class="px-4 py-4">
                                    <input
                                        type="checkbox"
                                        checked={selectedRowsMap.has(row.id)}
                                        on:change={() => toggleRowSelection(row, row.id)}
                                    />
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/bill-of-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {(paginationData.pagination.currentPage - 1) * paginationData.pageSize + index + 1}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/bill-of-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.finalGoodsVariation.name}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/bill-of-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.createdBy.firstName} {row.createdBy.lastName}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/bill-of-materials/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {new Date(row.createdAt).toLocaleDateString()}
                                    </a>
                                </td>
                            </tr>
                        {/each}
                        {#if filteredData.length === 0}
                            <tr class="font-medium text-black dark:text-gray-400">
                                <td colspan={headers.length + 2} class="h-[50vh] text-center">
                                    No bill of materials found
                                </td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>
        {/if}
    </div>
    <PaginationButtons {paginationData} />
{/if}
