import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IFinalGoodsVariation } from "$lib/final_goods/models/IFinalGoods";
import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
import type {
    IBillOfMaterial,
    IBillOfMaterialOverview,
    IBillOfMaterialAddRequest,
    IBillOfMaterialUpdateRequest,
} from "../models/IBillOfMaterial";

export interface IBillOfMaterialRepo {
    // CRUD operations
    getBillOfMaterials(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IBillOfMaterialOverview>>>;
    getBillOfMaterialById(id: number): Promise<DTO<IBillOfMaterial>>;
    create(payload: IBillOfMaterialAddRequest): Promise<DTO<null>>;
    updateBillOfMaterial(payload: IBillOfMaterialUpdateRequest): Promise<DTO<null>>;
    deleteBillOfMaterial(ids: number[]): Promise<DTO<null>>;
    deleteBillOfMaterialById(id: number): Promise<DTO<null>>;

    // Search operations for dropdowns
    searchFinalGoodsVariations(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IFinalGoodsVariation>>>;
    searchRawMaterialVariations(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialVariation>>>;
}
