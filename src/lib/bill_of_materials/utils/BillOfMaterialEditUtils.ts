import type {
    IBillOfMaterial,
    IBillOfMaterialEditFormState,
    IBillOfMaterialRawMaterialVariation,
} from "../models/IBillOfMaterial";

export abstract class BillOfMaterialEditUtils {

    /**
     * Converts IBillOfMaterialResponse data to IBillOfMaterialEditFormState for editing
     */
    static convertToEditFormState(billOfMaterial: IBillOfMaterial): IBillOfMaterialEditFormState {
        return billOfMaterial;
    }

    /**
     * Creates an empty edit form state
     */
    static getEmptyEditFormState(): IBillOfMaterialEditFormState {
        return {
            id: 0,
            finalGoodsVariation: null,
            rawMaterialVariations: [],
        };
    }

    /**
     * Generates a temporary ID for new raw material items
     */
    static generateTempId(): string {
        return Math.random().toString(36).substring(2, 11);
    }

    /**
     * Creates an empty raw material form item
     */
    static getEmptyRawMaterialFormItem(): IBillOfMaterialRawMaterialVariation {
        return {
            id: -1,
            bomId: -1,
            rawMaterialVariation: null,
            qty: 0
        };
    }
}
