import type {
    IBillOfMaterialFormState,
    IBillOfMaterialAddRequest,
    IBillOfMaterialUpdateRequest
} from "../models/IBillOfMaterial";
import type { ValidationErrors } from "$lib/common/utils/types";
import { z } from "zod";

export abstract class BillOfMaterialUtils {

    static emptyFormState: IBillOfMaterialFormState = {
        finalGoodsVariation: null,
        rawMaterialVariations: []
    };

    static getEmptyRawMaterial() {
        return {
            id: Math.random().toString(36).substring(2, 11),
            rawMaterialVariation: null,
            qty: 0
        };
    }

    // Validation schemas
    private static rawMaterialSchema = z.object({
        rawMaterialVariationId: z.number().int().positive("Raw material variation is required"),
        qty: z.number().positive("Quantity must be greater than 0")
    });

    static creationSchema = z.object({
        finalGoodsVariationId: z.number().int().positive("Final goods variation is required"),
        rawMaterials: z.array(BillOfMaterialUtils.rawMaterialSchema)
            .min(1, "At least one raw material is required")
    });

    static updateSchema = BillOfMaterialUtils.creationSchema.extend({
        id: z.number().int().positive("ID is required")
    });

    static validateWithSchema = <T>(schema: z.ZodSchema<T>, payload: unknown): ValidationErrors => {
        const errors: ValidationErrors = new Map();
        const result = schema.safeParse(payload);

        if (!result.success) {
            result.error.issues.forEach((issue) => {
                const path = issue.path.join('.');
                errors.set(path, issue.message);
            });
        }

        return errors;
    };

    static validateCreate = (payload: IBillOfMaterialAddRequest): ValidationErrors => {
        return BillOfMaterialUtils.validateWithSchema(BillOfMaterialUtils.creationSchema, payload);
    };

    static validateUpdate = (payload: IBillOfMaterialUpdateRequest): ValidationErrors => {
        return BillOfMaterialUtils.validateWithSchema(BillOfMaterialUtils.updateSchema, payload);
    };
}
