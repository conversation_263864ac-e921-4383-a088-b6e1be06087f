import type { FetchResult } from "$lib/fetch/models/Fetch";

// Base interfaces from the codebase
interface BaseModel {
    id: number;
    createdBy: string;
    createdAt: Date;
}

interface UpdatedMetaData {
    updatedBy: string | null;
    updatedAt: Date | null;
}

interface DeletedMetaData {
    deletedBy: string | null;
    deletedAt: Date | null;
}

// API Response wrapper
export interface APIBaseResponse<T> {
    success: boolean;
    message: string;
    data: T | null;
}

// Raw Material Stock interface
export interface IRawMaterialStock extends BaseModel, DeletedMetaData, UpdatedMetaData {
    id: number;
    rawMaterialId: number;
    rawMaterialName: string;
    sku: string;
    msq: number;
    totalStock: number;
    usableStock: number;
    assignedStock: number;
}

// Extended interface for problematic stocks with additional fields
export interface IRawMaterialStockDetails extends IRawMaterialStock {
    categoryName: string;
}

// Frontend-compatible interface with string dates
export interface ProblematicStock {
    id: number;
    rawMaterialId: number;
    rawMaterialName: string;
    sku: string;
    categoryName: string;
    msq: number;
    totalStock: number;
    usableStock: number;
    assignedStock: number;
    createdBy: string;
    createdAt: string;
    updatedBy: string | null;
    updatedAt: string | null;
    deletedBy: string | null;
    deletedAt: string | null;
}

export type ProblematicStocksResponse = FetchResult<ProblematicStock[]>

// Problem types enum
export enum ProblemType {
    NEGATIVE_TOTAL_STOCK = 'Negative Total Stock',
    NEGATIVE_USABLE_STOCK = 'Negative Usable Stock',
    NEGATIVE_ASSIGNED_STOCK = 'Negative Assigned Stock',
    STOCK_CALCULATION_MISMATCH = 'Stock Calculation Mismatch'
}

// Utility function for problem detection
export function getProblemTypes(stock: ProblematicStock): ProblemType[] {
    const problems: ProblemType[] = [];
    
    if (stock.totalStock < 0) problems.push(ProblemType.NEGATIVE_TOTAL_STOCK);
    if (stock.usableStock < 0) problems.push(ProblemType.NEGATIVE_USABLE_STOCK);
    if (stock.assignedStock < 0) problems.push(ProblemType.NEGATIVE_ASSIGNED_STOCK);
    
    // Check stock calculation consistency
    const expectedUsable = stock.totalStock - stock.assignedStock;
    if (stock.usableStock !== expectedUsable) {
        problems.push(ProblemType.STOCK_CALCULATION_MISMATCH);
    }
    
    return problems;
}

// Utility function for problem severity
export function getProblemSeverity(problems: ProblemType[]): 'critical' | 'warning' | 'none' {
    if (problems.includes(ProblemType.NEGATIVE_TOTAL_STOCK) || 
        problems.includes(ProblemType.NEGATIVE_USABLE_STOCK)) {
        return 'critical';
    }
    if (problems.length > 0) {
        return 'warning';
    }
    return 'none';
} 