<script lang="ts">
    import {
        capitalizeFirstWord,
        capitalizeInitials,
        debounce,
        formatDateUI,
        getDateDifference,
        getFutureDate,
        getPastDate,
        showErrorToast,
    } from "$lib/common/utils/common-utils";
    import { onMount } from "svelte";
    import { goto } from "$app/navigation";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { Spinner } from "flowbite-svelte";
    import type { IOpeningStock } from "../models/IOpeningStock";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import Loader from "$lib/common/components/Loader.svelte";
    import { downloadAllCsv, downloadCsv } from "$lib/opening-stock/utils/OpeningStockUtils";

    export let searchTerm: string = "";
    export let paginationData: PaginatedDataWrapper<IOpeningStock>;

    let isLoading: boolean = false;
    let isSearching: boolean = false;
    let filteredData: IOpeningStock[] = [];
    let alreadyFetchedData: IOpeningStock[] = [];

    const exportAllDate: {
        startDate: Date;
        endDate: Date;
        loading: boolean;
        exportAllLoading: boolean;
    } = {
        startDate: getPastDate(new Date(), 365),
        endDate: new Date(),
        loading: false,
        exportAllLoading: false,
    };

    let selectAll = false;

    // Debounced search logic
    let debounceSearch = debounce(async (e: any) => {
        if (e.target.value.trim().length > 2) {
            isSearching = true;
            const result = await PresenterProvider.openingStockPresenter.getAll(
                1,
                paginationData.pageSize,
                e.target.value.trim()
            );
            if (!result.success) {
                return showErrorToast(result.message);
            } else {
                filteredData = result.data.data;
                isSearching = false;
            }
        } else {
            filteredData = alreadyFetchedData;
        }
    }, 300);

    onMount(() => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;
    });

    export let selectedRowsMap: Map<number, IOpeningStock> = new Map();
    // Toggle all selections
    function toggleSelectAll() {
        if (selectAll) {
            filteredData.forEach((rmData) => {
                selectedRowsMap.set(rmData.id, rmData);
            });
        } else {
            selectedRowsMap.clear();
            selectAll = false;
        }
        selectedRowsMap = selectedRowsMap;
    }

    function toggleRowSelection(row: IOpeningStock, id: number) {
        if (selectedRowsMap.has(id)) {
            selectedRowsMap.delete(id);
        } else {
            selectedRowsMap.set(id, row);
        }
    }
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-full items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <h1
        class="mb-4 font-primary text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
    >
        Stock Adjustments
    </h1>

    <div class="flex my-3 px-1 flex-col md:flex-row items-center gap-4 justify-end">
        <p class="text-sm italic text-red-500">* Date Range cannot be more than 1 year</p>
    </div>

    <div class="flex my-3 px-1 flex-col md:flex-row items-center gap-4 justify-end">
        <!-- Start Date Input -->
        <div class="flex flex-col md:flex-row md:items-center gap-2">
            <label for="start-date" class="font-medium text-gray-700">Start Date</label>
            <input
                type="date"
                id="start-date"
                class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                value={exportAllDate.startDate.toISOString().split("T")[0]}
                on:input={(e) => {
                    //  @ts-ignore
                    const dd = new Date(e.target.value);
                    const number = getDateDifference(dd, exportAllDate.endDate);
                    if (number > 365) {
                        exportAllDate.endDate = getFutureDate(dd, 365);
                        exportAllDate.startDate = dd;
                    } else if (number < 1) {
                        exportAllDate.startDate = dd;
                        exportAllDate.endDate = getFutureDate(exportAllDate.startDate, 1);
                    }
                }}
            />
        </div>

        <!-- End Date Input -->
        <div class="flex flex-col md:flex-row md:items-center gap-2">
            <label for="end-date" class="font-medium text-gray-700">End Date</label>
            <input
                type="date"
                id="end-date"
                class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                value={exportAllDate.endDate.toISOString().split("T")[0]}
                on:input={(e) => {
                    //  @ts-ignore
                    const dd = new Date(e.target.value);
                    const number = getDateDifference(exportAllDate.startDate, dd);

                    if (number > 365) {
                        exportAllDate.startDate = getPastDate(dd, 365);
                        exportAllDate.endDate = dd;
                    } else if (number < 1) {
                        exportAllDate.endDate = dd;
                        exportAllDate.startDate = getPastDate(dd, 1);
                    }
                }}
            />
        </div>

        <!-- Export Button -->
        <div class="h-full flex items-center">
            <button
                id="dropdownActionButton"
                data-dropdown-toggle="dropdownAction"
                class="inline-flex items-center justify-center rounded-lg border border-gray-300 text-gray-600 px-4 py-2 text-sm font-medium hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-red-300"
                type="button"
                disabled={exportAllDate.exportAllLoading}
                on:click={async () => {
                    exportAllDate.exportAllLoading = true;
                    await downloadAllCsv(exportAllDate.startDate, exportAllDate.endDate);
                    exportAllDate.exportAllLoading = false;
                }}
            >
                {#if exportAllDate.exportAllLoading}
                    <div class="flex gap-2 items-center justify-center">
                        Loading <Spinner class="size-5" />
                    </div>
                {:else}
                    Export All
                {/if}
            </button>
        </div>
    </div>

    <div class="relative overflow-x-auto font-primary shadow-md sm:rounded-lg">
        <div
            class="flex-column flex flex-wrap items-center justify-between space-y-4 rounded-lg bg-[#E3E3E3] p-4 text-sm focus:ring-primary-500 dark:bg-[#131313] md:flex-row md:space-y-0"
        >
            <label for="table-search" class="sr-only">Search</label>

            <div class="relative">
                <div
                    class="rtl:inset-r-0 pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
                >
                    <img src="/images/svg/search.svg" alt="search-icon" width="15px" />
                </div>
                <input
                    type="text"
                    id="opening-stock-search"
                    bind:value={searchTerm}
                    on:input={debounceSearch}
                    class="block w-96 rounded-lg p-2 ps-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:bg-primary-800 dark:text-white"
                    placeholder="Search by Raw Material ID or Quantity"
                />
                {#if isSearching}
                    <div class="absolute right-0 top-0 bottom-0 flex items-center justify-center">
                        <Loader />
                    </div>
                {/if}
            </div>

            <div class="space-x-4">
                <button
                    id="dropdownActionButton"
                    class="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    type="button"
                    on:click={() => {
                        goto("/admin/stock-adjustments/add");
                    }}
                >
                    Add New
                </button>

                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    type="button"
                    disabled={exportAllDate.loading}
                    on:click={async () => {
                        exportAllDate.loading = true;
                        await downloadCsv([...selectedRowsMap.values()], "Opening Stock");
                        exportAllDate.loading = false;
                    }}
                >
                    {#if exportAllDate.loading}
                        <div class="flex gap-2 items-center justify-center">
                            Loading <Spinner class="size-5" />
                        </div>
                    {:else}
                        Export
                    {/if}
                </button>
            </div>
        </div>

        {#if isSearching}
            <div class="flex h-[30rem] w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <div class="max-h-[48vh] overflow-y-auto">
                <table
                    class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right"
                >
                    <thead
                        class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                    >
                        <tr>
                            <th class="px-4 py-3">
                                <input
                                    type="checkbox"
                                    bind:checked={selectAll}
                                    on:change={toggleSelectAll}
                                />
                            </th>
                            <th scope="col" class="px-6 py-3">SR No.</th>
                            <th scope="col" class="px-6 py-3">Raw Material</th>
                            <th scope="col" class="px-6 py-3">Quantity</th>
                            <th scope="col" class="px-6 py-3">Stock Date</th>
                            <th scope="col" class="px-6 py-3">Created By</th>
                            <th scope="col" class="px-6 py-3">Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each filteredData as row, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-4 py-4">
                                    <input
                                        type="checkbox"
                                        checked={selectedRowsMap.has(row.id)}
                                        on:change={() => toggleRowSelection(row, row.id)}
                                    />
                                </td>

                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/stock-adjustments/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {(paginationData.pagination.currentPage - 1) *
                                            paginationData.pageSize +
                                            index +
                                            1}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/stock-adjustments/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {capitalizeFirstWord(row.rawMaterial.name.trim())}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/stock-adjustments/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.quantity}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/stock-adjustments/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {formatDateUI(row.date, false)}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/stock-adjustments/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {capitalizeInitials(row.createdBy)}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/stock-adjustments/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {formatDateUI(row.createdAt)}
                                    </a>
                                </td>
                            </tr>
                        {/each}
                        {#if filteredData.length === 0}
                            <tr class="font-medium text-black dark:text-gray-400">
                                <td colspan="6" class="h-[50vh] text-center">
                                    {searchTerm.trim().length > 0
                                        ? "No results found"
                                        : "Yet no opening stocks"}
                                </td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>
        {/if}
    </div>
    <PaginationButtons {paginationData} />
{/if}
