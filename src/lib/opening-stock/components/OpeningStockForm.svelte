<script lang="ts">
    import { goto } from "$app/navigation";
    import {
        formatDateToInput,
        showErrorToast,
        showSuccessToast,
    } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, CloseButton } from "flowbite-svelte";
    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import SupplierRawMaterialSearch from "$lib/purchase_invoice/components/SupplierRawMaterialSearch.svelte";

    import { OpeningStockUtils } from "../utils/OpeningStockUtils";
    import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
    import type { IOpeningStockRequest } from "../models/IOpeningStock";
    import type { ValidationErrors } from "$lib/common/utils/types";

    export let obj: IOpeningStockRequest = OpeningStockUtils.getEmpty();
    let isDoingTask: boolean = false;
    let selectedDate = "";
    let validationErrors: ValidationErrors = new Map();
    let isEditCase: boolean = false;

    let selectedRawMaterials: Map<string, IRawMaterialVariation> = new Map();

    const onRawMaterialSelected = (data: IRawMaterialVariation, index: number) => {
        if (!data) {
            /* remove from selected map */
            selectedRawMaterials.delete(index.toString());
            obj.data[index].rawMaterialId = -1;
            obj = obj;
            selectedRawMaterials = selectedRawMaterials;
            return;
        }

        /* check if raw material is already in the array */
        let isAlreadyInArray = false;
        for (let i = 0; i < obj.data.length; i++) {
            if (obj.data[i].rawMaterialId.toString() === data.id.toString()) {
                isAlreadyInArray = true;
                break;
            }
        }
        if (isAlreadyInArray) {
            showErrorToast("Raw material already added");
            return;
        }
        selectedRawMaterials.set(index.toString(), data);
        selectedRawMaterials = selectedRawMaterials;
        obj.data[index].rawMaterialId = Number(data.id);
        obj = obj;
    };

    const addRow = () => {
        obj.data.push({
            rawMaterialId: -1,
            quantity: 0,
        });
        obj = obj;
    };

    const handleSubmit = async () => {
        /* validate */
        validationErrors = PresenterProvider.openingStockPresenter.onValidate(obj);
        if (validationErrors.size > 0) {
            showErrorToast(validationErrors.get(validationErrors.keys().next().value!));
            return;
        }

        isDoingTask = true;

        let res = isEditCase
            ? await PresenterProvider.openingStockPresenter.onUpdate({
                  data: [
                      {
                          id: obj.id!,
                          quantity: obj.data[0].quantity,
                          rawMaterialId: (obj.data[0].rawMaterialId),
                      },
                  ],
              })
            : await PresenterProvider.openingStockPresenter.onSubmit(obj);

        if (res.success) {
            showSuccessToast(
                isEditCase
                    ? `Opening stock updated successfully`
                    : `Opening stock added successfully`
            );
            await goto("/admin/stock-adjustments");
        } else {
            showErrorToast(res.message);
        }

        isDoingTask = false;
    };
</script>

<!-- Main Container -->
<div class="flex items-center justify-center">
    <div class="w-[90vw] p-2">
        <div class="flex items-center justify-between py-2">
            <FormHeader label={isEditCase ? "Edit Stock Adjustment" : "Add Stock Adjustments"} />
            <BreadCrumbs breadCrumbData={[]} />
        </div>
        <hr class="mb-5" />

        <!-- Input Fields -->
        <div class="grid grid-cols-5 gap-6 mb-4">
            <div>
                <Label for="date" class="mb-2 font-sans capitalize tracking-[0px]">
                    Invoice Date
                    {#if validationErrors.has("date")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>
                <Input
                    type="date"
                    id="date"
                    class="dark:bg-primary-700 {validationErrors.has('date')
                        ? 'border-red-500'
                        : ''}"
                    value={formatDateToInput(obj.date)}
                    onchange={(e: any) => {
                        obj.date = new Date(e.target.value);
                    }}
                />
                {#if validationErrors.has("date")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("date")}
                    </p>
                {/if}
            </div>
        </div>

        <div class="overflow-x-auto">
            <table
                class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right border"
            >
                <thead
                    class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                >
                    <tr>
                        <th scope="col" class="px-6 py-3 text-sm">SR No.</th>
                        <th scope="col" class="px-6 py-3 text-sm">Raw_Material</th>
                        <th scope="col" class="px-6 py-3 text-sm">Qty</th>
                        <th scope="col" class="px-6 py-3">X</th>
                    </tr>
                </thead>
                <tbody>
                    {#each obj.data as item, index}
                        <tr
                            class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                        >
                            <td class="px-6 py-4">
                                {index + 1}
                            </td>
                            <td class="px-6 py-4 w-[300px] h-[60px]">
                                <div class="mb-[35px] w-[250px]">
                                    <SupplierRawMaterialSearch
                                        showAddNew={false}
                                        selected={selectedRawMaterials.get(index.toString()) ??
                                            null}
                                        onSelected={(data) => {
                                            onRawMaterialSelected(data, index);
                                        }}
                                    />
                                </div>
                            </td>

                            <td class="px-6 py-4">
                                <Input
                                    class="mb-[35px] w-[100px]"
                                    type="number"
                                    value={item.quantity}
                                    on:change={(e: any) => {
                                        item.quantity = parseFloat(e.target.value);

                                        if (!item.quantity) {
                                            item.quantity = 0;
                                        }
                                    }}
                                />
                            </td>

                            <td class="px-6 py-4">
                                {#if obj.data.length > 1}
                                    <CloseButton
                                        class="w-content mb-[35px]"
                                        on:click={() => {
                                            obj.data.splice(index, 1);
                                            obj = obj;
                                        }}
                                    />
                                {/if}
                            </td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        </div>
        <div class="mt-5 flex w-full justify-between">
            <div>
                <CustomButton
                    onClick={addRow}
                    cssClass=" bg-black"
                    title={"Add new"}
                    isLoading={isDoingTask}
                />
            </div>
            <CustomButton
                onClick={handleSubmit}
                cssClass="w-32 bg-black"
                title={"Save"}
                isLoading={isDoingTask}
            />
        </div>
    </div>
</div>
