import type { DTO } from "$lib/common/models/BaseDTO";
import type { IFileRecordResponse, IOpeningStock, IOpeningStockRequest, updateOpeningStockPayload } from "../models/IOpeningStock";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { ValidationErrors } from "$lib/common/utils/types";

export interface IOpeningStockPresenter {
    getAll(page: number, pageSize: number, text?: string ,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IOpeningStock>>>;
    onSubmit(payload: IOpeningStockRequest): Promise<DTO<null>>;
    onValidate(payload: IOpeningStockRequest): ValidationErrors;
    loadById(id:number):Promise<DTO<IOpeningStock>>;
    onUpdate(payload: updateOpeningStockPayload): Promise<DTO<boolean>>;
    onUpdateDateValidate(payload: updateOpeningStockPayload): ValidationErrors;
    onLoadFileRecord(page: number, pageSize: number, text?: string ,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IFileRecordResponse>>>;
}
