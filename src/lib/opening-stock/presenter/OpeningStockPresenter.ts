import type { DTO } from "$lib/common/models/BaseDTO";
import type { IFileRecordResponse, IOpeningStock, IOpeningStockRequest, updateOpeningStockPayload } from "../models/IOpeningStock";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import { RepoProvider } from "$lib/RepoProvider";
import type { IOpeningStockPresenter } from "./IOpeningStockPresenter";
import type { ValidationErrors } from "$lib/common/utils/types";
import { OpeningStockUtils } from "../utils/OpeningStockUtils";

export class OpeningStockPresenter implements IOpeningStockPresenter {
    onLoadFileRecord(page: number, pageSize: number, text?: string ,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IFileRecordResponse>>> {
        return RepoProvider.openingStockRepo.getAllFileRecord(page, pageSize, text,startDate,endDate);
    }

    getAll(page: number, pageSize: number, text?: string ,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IOpeningStock>>> {
        return RepoProvider.openingStockRepo.getAll(page, pageSize, text,startDate,endDate);
    }

    onSubmit(payload: IOpeningStockRequest): Promise<DTO<null>> {
        return RepoProvider.openingStockRepo.create(payload);
    }


    onValidate(payload: IOpeningStockRequest): ValidationErrors {

        const errors: ValidationErrors = new Map();
        /* validate */
        const result = OpeningStockUtils.creationSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;

    }

    onUpdateDateValidate(payload: updateOpeningStockPayload): ValidationErrors {

        const errors: ValidationErrors = new Map();
        /* validate */
        const result = OpeningStockUtils.updateSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;

    }


    loadById(id:number):Promise<DTO<IOpeningStock>>{
        return RepoProvider.openingStockRepo.getById(id);
    }

    onUpdate(payload: updateOpeningStockPayload): Promise<DTO<boolean>> {
        return RepoProvider.openingStockRepo.update(payload);
    }

}
