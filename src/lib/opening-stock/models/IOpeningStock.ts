import type { BaseModel, DeletedMetaData, UpdatedMetaData } from "$lib/common/models/base_model"



interface IOpeningStockRequest {
  id?: number;
  date: Date;
  data: {
    rawMaterialId: number;
    quantity: number;
  }[];
}


interface IOpeningStock {
  id: number;
  rawMaterial: {
    id: number;
    name: string;
  };
  date: Date;
  quantity: number;
  createdBy: string;
  createdAt: Date;
}


interface updateOpeningStockPayload {
  data: {
    id: number;
    rawMaterialId: number;
    quantity: number;
  }[]
}


interface IFileRecordResponse {
  id: string;
  fileName: string;
  month: string;
  url: string | null;
  s3Location: string | null;
  status: string;
  createdAt: string;
  updatedAt: string; 
  deletedAt: string | null;
}

export { type IOpeningStockRequest, type IOpeningStock, type updateOpeningStockPayload, type IFileRecordResponse }
