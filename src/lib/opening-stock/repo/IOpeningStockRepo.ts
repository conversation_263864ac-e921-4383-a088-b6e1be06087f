import type { DTO } from "$lib/common/models/BaseDTO";
import type { IFileRecordResponse, IOpeningStock, IOpeningStockRequest, updateOpeningStockPayload } from "../models/IOpeningStock";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export interface IOpeningStockRepo {
    getAll(page: number, pageSize: number, text?: string ,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IOpeningStock>>>;
    create(payload: IOpeningStockRequest): Promise<DTO<null>>;
    getById(id:number):Promise<DTO<IOpeningStock>>;
    update(payload: updateOpeningStockPayload): Promise<DTO<boolean>>;
    getAllFileRecord(page: number, pageSize: number, text?: string ,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IFileRecordResponse>>>
}
