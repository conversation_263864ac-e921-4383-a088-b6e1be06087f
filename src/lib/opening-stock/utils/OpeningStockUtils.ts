import { z } from "zod";

import type { IOpeningStock, IOpeningStockRequest } from "../models/IOpeningStock";

import { showErrorToast } from "$lib/common/utils/common-utils";
import { CSVProvider } from "$lib/csv-provider/repositories/CSVProvider";
import { PresenterProvider } from "$lib/PresenterProvider";

export abstract class OpeningStockUtils {

    static getEmpty(): IOpeningStockRequest {
        return {
            date: new Date(),
            data: [
                {
                    rawMaterialId: -1,
                    quantity: 0,
                }
            ],
        };
    }


    static getEmptyOpeningStock(): IOpeningStock {
        return {
            id: 1,
            rawMaterial: {
                id: 1,
                name: "",
            },
            date: new Date(),
            quantity: 0,
            createdBy: "",
            createdAt: new Date(),
        };
    }



    static creationSchema = z.object({
        date: z.date().optional().refine(val => val != null && val != undefined, {
            message: "Invalid date",
        }),
        data: z.array(z.object({
            rawMaterialId: z.number().optional().refine(val => val && val > 0, {
                message: "Invalid raw material id",
            }),
            quantity: z.number().optional().refine(val => val && val > 0, {
                message: "Invalid quantity",
            }),
        }))
    });

    static updateSchema = z.object({
        data: z.array(z.object({
            id: z.number().optional().refine(val => val && val > 0, {
                message: "Invalid id",
            }),
            rawMaterialId: z.number().optional().refine(val => val && val > 0, {
                message: "Invalid raw material id",
            }),
            quantity: z.number().optional().refine(val => val && val > 0, {
                message: "Invalid quantity",
            }),
        }))
    });
}

                                                            
let keyMap: { [key: string]: string } = {
    "Raw  Material": "rawMaterial.name",
    "Quantity": "quantity",
    "Date": "date",
    "Created By": "createdBy",
    "Created At": "createdAt",
}                             

export const downloadCsv = async (data: IOpeningStock[], fileName: string) => {
    if (data.length === 0) {
        showErrorToast("Please choose data")
        return;
    }
    await new CSVProvider().save(data, fileName, keyMap);
}

export const downloadAllCsv = async (startDate: Date, endDate: Date) => {
    const response = await PresenterProvider.openingStockPresenter.getAll(1, 2, undefined, startDate, endDate)
    if (response.success && response.data.data.length > 0) {
        await new CSVProvider().save(response.data.data, "Opening stock", keyMap);
    } else {
        showErrorToast("No data found")
    }
}
