<script lang="ts">
    export let title;
    export let total;
    export let color;
    export let width;
    export let options; // Dropdown options
    export let selected: any; // Currently selected option

    // Emit change event on dropdown change
    function handleChange(event: any) {
        const selectedValue = event.target.value;
        // Dispatch a change event to the parent component
        // dispatch('change', selectedValue);
    }
</script>

<div class="card" style="background-color: {color}; width: {width}">
    <div class="card-content">
        <div class="header">
            <div class="text-content">
                <p>{title}</p>
                <h3>{total}</h3>
            </div>
            <div class="dropdown-container">
                <select on:change={handleChange} bind:value={selected}>
                    {#each options as option}
                        <option value={option}>{option}</option>
                    {/each}
                </select>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 10px;
        padding: 20px;
        margin: 0;
        color: black;
        box-shadow: 0 4px 10px rgb(178 178 178 / 20%);
        transition:
            transform 0.3s,
            box-shadow 0.3s;
        cursor: pointer;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    }

    .card-content {
        display: flex;
        flex-direction: column;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .text-content {
        display: flex;
        flex-direction: column;
    }

    .dropdown-container {
        margin-left: auto;
    }

    .dropdown-container select {
        padding-right: 26px;
        font-size: 13px;
    }

    select {
        padding: 5px;
        border-radius: 5px;
        border: none;
        background-color: transparent;
        color: black;
    }

    h3 {
        font-size: 2rem;
        margin: 0;
    }

    p {
        font-size: 1.2rem;
        margin: 5px 0;
    }
</style>
