<script lang="ts">
  // Import ApexCharts using a dynamic import
  // @ts-ignore
  import ApexCharts from "apexcharts";
  import { onMount } from "svelte";

  const options = {
    chart: {
      type: "bar",
      height: 350, // Optional: Set the height of the chart
    },
    series: [
      {
        name: "sales",
        data: [30, 40, 35, 50, 49, 60, 70, 91, 125],
      },
    ],
    xaxis: {
      categories: [1991, 1992, 1993, 1994, 1995, 1996, 1997, 1998, 1999],
    },
  };

  let chart: ApexCharts | null = null; // Declare chart variable

  function renderChart() {
    chart = new ApexCharts(document.querySelector("#chart"), options); // Assign the chart instance
    chart.render();
  }

  onMount(() => {
    renderChart();

    // Cleanup the chart when the component is destroyed
    return () => {
      if (chart) {
        chart.destroy(); // Ensure the chart is destroyed
      }
    };
  });
</script>

<div class="chart-container">
  <div id="chart"></div>
</div>

<style>
  .chart-container {
    border-radius: 10px;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    transition:
      transform 0.3s,
      box-shadow 0.3s;
    cursor: pointer;
    margin-top: 0px;
  }

  .chart-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  }
</style>
