import type { DTO } from "$lib/common/models/BaseDTO";
import type { ValidationErrors } from "$lib/common/utils/types";

export interface IExcelUploadPresenter {
    onValidate(file: File | null): ValidationErrors;
    onSubmit(file: File): Promise<DTO<null>>;
    // getAll(page: number, limit: number, search?: string): Promise<DTO<PaginatedBaseResponse<IExcelUploadResponse>>>;
    // getById(id: string): Promise<DTO<IExcelUploadResponse>>;
}
