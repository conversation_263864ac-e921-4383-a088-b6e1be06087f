import type { DTO } from "$lib/common/models/BaseDTO";
import type { ValidationErrors } from "$lib/common/utils/types";
import { RepoProvider } from "$lib/RepoProvider";
import type { IExcelUploadPresenter } from "./IExcelUploadPresenter";

export class ExcelUploadPresenter implements IExcelUploadPresenter {
    onValidate(file: File | null): ValidationErrors {
        const errors: ValidationErrors = new Map();
        
        if (!file) {
            errors.set("file", "Please select a file");
            return errors;
        }
        
        const validExcelTypes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel.sheet.macroEnabled.12'
        ];
        
        if (!validExcelTypes.includes(file.type)) {
            errors.set("file", "Please upload a valid Excel file (.xls, .xlsx)");
        }
        
        const maxSize = 10 * 1024 * 1024; 
        if (file.size > maxSize) {
            errors.set("file", "File size should not exceed 10MB");
        }
        
        return errors;
    }
    
    async onSubmit(file: File): Promise<DTO<null>> {
        const formData = new FormData();
        formData.append('excelFile', file);
        
        return await RepoProvider.excelUploadRepo.upload(formData);
    }
    
    // async getAll(page: number, limit: number, search?: string): Promise<DTO<PaginatedBaseResponse<IExcelUploadResponse>>> {
    //     return await RepoProvider.excelUploadRepo.getAll(page, limit, search);
    // }
    
    // async getById(id: string): Promise<DTO<IExcelUploadResponse>> {
    //     return await RepoProvider.excelUploadRepo.getById(id);
    // }
}
