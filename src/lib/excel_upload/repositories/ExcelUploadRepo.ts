import { EXCEL_API_PATH } from "$lib/common/configs/serverConfig";
import { type DTO, getHandledErrorDTO, getSuccessDTO, getUnhandledErrorDTO } from "$lib/common/models/BaseDTO";
import { handleError } from "$lib/common/utils/logging";
import { usiFirebaseAuth } from "../../../firebaseInit";
import type { IExcelUploadRepo } from "./IExcelUploadRepo";

export class ExcelUploadRepo implements IExcelUploadRepo {
    private _apiPath: string = EXCEL_API_PATH;

    async upload(payload: FormData): Promise<DTO<null>> {
        try {
            const user = usiFirebaseAuth.currentUser;
            let token = null;
            if (user) {
                token = await user.getIdToken();
            }

            const headers: HeadersInit = {};
            if (token) {
                headers["Authorization"] = `Bearer ${token}`;
            }

            const options = {
                method: "POST",
                body: payload,
                headers
            };

            const response = await fetch(this._apiPath + "/process", options);

            const data = await response.json().catch(() => ({
                success: false,
                message: response.statusText || "Failed to parse response"
            }));

            if (response.ok && data.success) {
                return getSuccessDTO(data.data || null);
            } else {
                return getHandledErrorDTO(data.message || "Upload failed");
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error.message || "An unexpected error occurred", error);
        }
    }

    // async getAll(page: number, limit: number, search?: string): Promise<DTO<PaginatedBaseResponse<IExcelUploadResponse>>> {
    //     try {
    //         const queryParams = new URLSearchParams();
    //         queryParams.append("page", page.toString());
    //         queryParams.append("limit", limit.toString());
    //         if (search) {
    //             queryParams.append("search", search);
    //         }

    //         const options = {
    //             method: "GET",
    //         };
    //         const res: FetchResult<PaginatedBaseResponse<IExcelUploadResponse>> = await fetchData(
    //             `${this._apiPath}?${queryParams.toString()}`,
    //             options
    //         );
    //         if (res.success) {
    //             return getSuccessDTO(res.data!);
    //         } else {
    //             return getHandledErrorDTO(res.message);
    //         }
    //     } catch (error: any) {
    //         handleError(error);
    //         return getUnhandledErrorDTO(error, error);
    //     }
    // }

    // async getById(id: string): Promise<DTO<IExcelUploadResponse>> {
    //     try {
    //         const options = {
    //             method: "GET",
    //         };
    //         const res: FetchResult<IExcelUploadResponse> = await fetchData(
    //             `${this._apiPath}/${id}`,
    //             options
    //         );
    //         if (res.success) {
    //             return getSuccessDTO(res.data!);
    //         } else {
    //             return getHandledErrorDTO(res.message);
    //         }
    //     } catch (error: any) {
    //         handleError(error);
    //         return getUnhandledErrorDTO(error, error);
    //     }
    // }
}
