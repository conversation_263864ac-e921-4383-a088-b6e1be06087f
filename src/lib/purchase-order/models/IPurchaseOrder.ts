import type { IDepartment } from "$lib/department/models/IDepartment";
import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
import type { ISupplier } from "$lib/supplier/models/ISupplier";

export interface IPurchaseOrderItems {
    id: number;
    qty: number;
    name: string;
}

export interface PurchaseItemDetails{
  item:IRawMaterialVariation,
  qty:number;
}

export interface IPurchaseOrder 
{
    id:number,
    poNumber:string,
    supplier: ISupplier,
    expectedDate:Date,
    items: PurchaseItemDetails[];
     createdByName:string|undefined;
    createdAt:Date|undefined;
    fromDepartment: IDepartment | null;
    toDepartment: IDepartment | null;
    supplierContactPerson?: string;
    
}
export interface IPurchaseOrderPayload extends IPurchaseOrder {
    orderItems:IPurchaseOrderItems[];
}

export interface ICreatePurchaseOrderPayload
{
  poNumber:string,
  supplierId:number,
  expectedDate:Date,
  items: IPurchaseOrderItems[],
   fromDepartmentId: number,
  toDepartmentId: number,
  supplierContactPerson?: string,

}

export interface IUpdatePurchaseOrderPayload extends ICreatePurchaseOrderPayload
{
  id:number,
}
