import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { ICreatePurchaseOrderPayload, IPurchaseOrder, IUpdatePurchaseOrderPayload } from "../models/IPurchaseOrder";

export interface IPurchaseOrderRepo {
    create(payload: IPurchaseOrder): Promise<DTO<IPurchaseOrder>>;
    update(payload: IPurchaseOrder): Promise<DTO<null>>;
    getById(id: number): Promise<DTO<IPurchaseOrder>>;
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseOrder>>>;
    searchByAny(text: string, page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseOrder>>>;
    searchByPO(poNumber: string,): Promise<DTO<IPurchaseOrder[]>>;

}