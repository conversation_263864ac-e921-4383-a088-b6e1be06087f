import { PURCHASE_ORDER_API_PATH } from "$lib/common/configs/serverConfig";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import { getHandledErrorDTO, getSuccessDTO, getUnhandledErrorDTO, type DTO } from "$lib/common/models/BaseDTO";
import { handleError } from "$lib/common/utils/logging";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { IPurchaseOrder } from "../models/IPurchaseOrder";
import { parsePOItems, parseUpdatePOItems } from "../utils/purchase-order-utils";
import type { IPurchaseOrderRepo } from "./IPurchaseOrderRepo";

export class PurchaseOrderRepo implements IPurchaseOrderRepo {
   
    searchByAny(text: string, page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseOrder>>> {
        throw new Error("Method not implemented.");
    }
    private _apiPath: string = PURCHASE_ORDER_API_PATH;

    async getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseOrder>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IPurchaseOrder>> = await fetchData(this._apiPath + "/?page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async create(payload: IPurchaseOrder): Promise<DTO<IPurchaseOrder>> {
        try {
            const options = {
                method: "POST",
                body: parsePOItems(payload),
            };
            console.log(options)
            const res: FetchResult<IPurchaseOrder> = await fetchData(
                this._apiPath + "/create",
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async update(payload: IPurchaseOrder): Promise<DTO<null>> {
        try {
            const options = {
                method: "PUT",
                body: parseUpdatePOItems(payload),
            };           
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/update/" + payload.id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getById(id:number):Promise<DTO<IPurchaseOrder>>{
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IPurchaseOrder> = await fetchData(
                this._apiPath + "/" + id,
                options
            );

            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }

    }
   

    async searchByPO(poNumber: string): Promise<DTO<IPurchaseOrder[]>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IPurchaseOrder[]> = await fetchData(
                this._apiPath + "/search?text=" + poNumber.trim()  ,
                options
            );

            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

}