<script lang="ts">
    import { goto } from "$app/navigation";
    import Loader from "$lib/common/components/Loader.svelte";
    import { Spinner } from "flowbite-svelte";
    import type { IPurchaseOrder } from "../models/IPurchaseOrder";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { onMount } from "svelte";
    import { debounce, formatDateUI, showErrorToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";

    export let isLoading: boolean = false;
    export let searchTerm: string = "";
    export let searchLoading: boolean = false;
    export let filteredData: IPurchaseOrder[] = [];
    export let paginationData: PaginatedDataWrapper<IPurchaseOrder>;
    export let onSearchClear: () => void = () => {};
    let isSearching: boolean = false;
    let alreadyFetchedData: IPurchaseOrder[] = [];

    let debounceSearch = debounce(async (e: any) => {
        if (searchTerm.trim().length === 0) {
            paginationData.searchText = undefined;
            onSearchClear();
            return;
        }
        if (e.target.value.trim().length > 2) {
            isSearching = true;

            paginationData.searchText = e.target.value.trim();

            const result = await PresenterProvider.purchaseOrderPresenter.searchByAny(
                paginationData.searchText!,
                paginationData.pagination.currentPage,
                paginationData.pageSize
            );
            if (!result.success) {
                return showErrorToast(result.message);
            } else {
                filteredData = result.data.data;

                paginationData.pagination = result.data;

                isSearching = false;
            }
        } else {
            filteredData = alreadyFetchedData;
        }
    }, 300);

    onMount(() => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;

        searchTerm = paginationData.searchText || "";
    });
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <h1
        class="mb-4 font-primary text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
    >
        Demand Slip
    </h1>

    <div class="relative overflow-x-auto font-primary shadow-md sm:rounded-lg">
        <div
            class="flex-column flex flex-wrap items-center justify-between space-y-4 rounded-lg bg-[#E3E3E3] p-4 text-sm focus:ring-primary-500 dark:bg-[#131313] md:flex-row md:space-y-0"
        >
            <div>
                <label for="table-search" class="sr-only">Search</label>

                <div class="relative">
                    <div
                        class="rtl:inset-r-0 pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
                    >
                        <img src="/images/svg/search.svg" alt="l" width="15px" />
                    </div>
                    <input
                        type="text"
                        id="franchise-search"
                        bind:value={searchTerm}
                        on:input={debounceSearch}
                        class="block w-96 rounded-lg p-2 ps-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:bg-primary-800 dark:text-white"
                        placeholder="Search by PO, Invoice number or Supplier"
                    />
                </div>
            </div>

            <div class="space-x-4">
                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    type="button"
                    on:click={() => {
                        goto("/admin/purchase-orders/add");
                    }}
                >
                    Add New
                </button>
            </div>
        </div>

        {#if searchLoading}
            <div class="flex h-[30rem] w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <div class="max-h-[50vh] overflow-y-auto">
                <table
                    class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right"
                >
                    <thead
                        class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                    >
                        <tr>
                            <th scope="col" class="px-6 py-3">SR No.</th>
                            <th scope="col" class="px-6 py-3">Supplier</th>
                            <th scope="col" class="px-6 py-3">PO Number</th>
                            <th scope="col" class="px-6 py-3">From Dept</th>
                            <th scope="col" class="px-6 py-3">To Dept</th>
                            <th scope="col" class="px-6 py-3">Raw Material</th>
                            <th scope="col" class="px-6 py-3">Expected Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each filteredData as row, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {(paginationData.pagination.currentPage - 1) *
                                            paginationData.pageSize +
                                            index +
                                            1}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium capitalize"
                                    >
                                        {row.supplier.name}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium capitalize"
                                    >
                                        {row.poNumber}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium capitalize"
                                    >
                                        {row.fromDepartment?.name || "N/A"}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium capitalize"
                                    >
                                        {row.toDepartment?.name || "N/A"}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium capitalize"
                                    >
                                        {row.items.map((item) => item.item.name).join(", ")}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/purchase-orders/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {formatDateUI(row.expectedDate, false)}
                                    </a>
                                </td>
                            </tr>
                        {/each}
                        {#if filteredData.length === 0}
                            <tr class="font-medium text-black dark:text-gray-400">
                                <td colspan="7" class="h-[50vh] text-center">
                                    {searchTerm.trim().length > 0
                                        ? "No results found"
                                        : "Yet no demand slips"}
                                </td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>
        {/if}
    </div>
    <PaginationButtons {paginationData} />
{/if}
