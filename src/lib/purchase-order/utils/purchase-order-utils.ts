import { z } from "zod";
import type { ICreatePurchaseOrderPayload, IPurchaseOrder, IUpdatePurchaseOrderPayload } from "../models/IPurchaseOrder"
import { SUPPLIER_STAUS } from "$lib/supplier/models/ISupplier";
import { DEPARTMENT_STATUS } from "$lib/department/models/IDepartment";

export const getEmptyPurchaseOrderItems = ()=>{
    let data:IPurchaseOrder = {
        poNumber: "",
        expectedDate: new Date(),
        createdByName: "",
        createdAt: new Date(),
        items: [],
        id: 0,
        supplier: {
            status: SUPPLIER_STAUS.ACTIVE,
            addressId: 0,
            address: {
                id: 0,
                street: "",
                postalCode: "",
                city: "",
                state: "",
                country: ""
            },
            name: "",
            email: null,
            phone: "",
            gst: null,
            pan: null,
            id: 0,
            createdBy: "",
            createdAt: new Date(),
            deletedBy: null,
            deletedAt: null,
            updatedBy: null,
            updatedAt: null
        },
        fromDepartment: null,
        toDepartment: null,
        supplierContactPerson: ""
    }
    return data;
}

export const parsePOItems = (item: IPurchaseOrder) =>{
    let data = {
        poNumber: item.poNumber,
        supplierId: item.supplier.id,
        expectedDate:item.expectedDate,
        fromDepartmentId: item.fromDepartment?.id || 0,
        toDepartmentId: item.toDepartment?.id || 0,
        supplierContactPerson: item.supplierContactPerson || "",
        items: item.items.map((item)=>{
            let dd={
                rawMaterialId: item.item.id,
                qty: item.qty
            }
            return dd;

        })
    }
    return data;
}

export const parseUpdatePOItems = (item: IPurchaseOrder) =>{
    let data = {
        id: item.id,
        expectedDate:item.expectedDate,
        poNumber: item.poNumber,
        supplierId: item.supplier.id,
        fromDepartmentId: item.fromDepartment?.id || 0,
        toDepartmentId: item.toDepartment?.id || 0,
        supplierContactPerson: item.supplierContactPerson || "",
        items: item.items.map((item)=>{
            let dd={
                rawMaterialId: item.item.id,
                qty: item.qty
            }
            return dd;

        })
    }
    return data;
}



export const purchaseOrderCreationSchema = z.object({
        expectedDate: z.date(),
        poNumber: z.string().min(3, "PO number must be at least 3 characters long").max(80, "PO number must be up to 80 characters long"),
        supplier: z.object({
            id: z.number().int().positive("Supplier cannot be empty"),
            name: z.string().min(1, "Supplier cannot be empty"),
        }),
        fromDepartment: z.object({
            id: z.number().int().positive("From Department cannot be empty"),
            name: z.string().min(1, "From Department cannot be empty"),
        }).nullable().refine((val) => val !== null, {
            message: "From Department is required",
        }),
        toDepartment: z.object({
            id: z.number().int().positive("To Department cannot be empty"),
            name: z.string().min(1, "To Department cannot be empty"),
        }).nullable().refine((val) => val !== null, {
            message: "To Department is required",
        }),
        supplierContactPerson: z.string().optional(),
        items: z.array(
            z.object({
                item: z.object({
                    id: z.number().int().positive("Invalid raw material"),
                }),
                qty: z.number().positive("Total quantity must be a positive number"),
            })
        ).min(1, "At least one raw material is required"),
    });