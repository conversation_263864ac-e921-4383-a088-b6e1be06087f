import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { ValidationErrors } from "$lib/common/utils/types";
import type { ICreatePurchaseOrderPayload, IPurchaseOrder, IUpdatePurchaseOrderPayload } from "../models/IPurchaseOrder";

export interface IPurchaseOrderPresenter {
    onSubmit(payload: IPurchaseOrder): Promise<DTO<IPurchaseOrder>>;
    onUpdate(payload: IPurchaseOrder): Promise<DTO<null>>;
    getById(id: number): Promise<DTO<IPurchaseOrder>>;
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseOrder>>>;
    onValidate(data: IPurchaseOrder): ValidationErrors;
    searchByAny(text: string, page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseOrder>>>;

}