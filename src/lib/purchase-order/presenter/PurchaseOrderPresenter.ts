import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { ValidationErrors } from "$lib/common/utils/types";
import { RepoProvider } from "$lib/RepoProvider";
import type { IPurchaseOrder } from "../models/IPurchaseOrder";
import { purchaseOrderCreationSchema } from "../utils/purchase-order-utils";
import type { IPurchaseOrderPresenter } from "./IPurchaseOrderPresenter";

export class PurchaseOrderPresenter implements IPurchaseOrderPresenter {
    searchByAny(text: string, page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseOrder>>> {
        return RepoProvider.purchaseOrderRepo.searchByAny(text, page, pageSize);
    }
    getById(id: number): Promise<DTO<IPurchaseOrder>> {
        return RepoProvider.purchaseOrderRepo.getById(id);
    }
    onValidate(data: IPurchaseOrder): ValidationErrors {

        console.log("data to valiate",data);
        
         const errors: ValidationErrors = new Map();
                /* validate */
                const result = purchaseOrderCreationSchema.safeParse(data);
                console.log(result);
        
                if (!result.success) {
                    result.error.issues.forEach((issue) => {
                        errors.set(issue.path[0].toString(), issue.message);
                    });
                }
                return errors;
    }
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IPurchaseOrder>>> {
        return RepoProvider.purchaseOrderRepo.getAll(page, pageSize);
    }
    onSubmit(payload: IPurchaseOrder): Promise<DTO<IPurchaseOrder>> {
         return RepoProvider.purchaseOrderRepo.create(payload)
    }
    onUpdate(payload: IPurchaseOrder): Promise<DTO<null>> {
        return RepoProvider.purchaseOrderRepo.update(payload)
    }
    
    
}