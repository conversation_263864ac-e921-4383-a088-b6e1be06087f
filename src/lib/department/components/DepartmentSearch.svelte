<script lang="ts">
    import SearchWithDrop from "$lib/common/components/SearchWithDrop.svelte";
    import { debounce } from "$lib/common/utils/common-utils";
    import { RepoProvider } from "$lib/RepoProvider";
    import type { IDepartment } from "../models/IDepartment";

    export let isLabel: boolean = true;
    export let labelText: string = "Department";
    export let selected: IDepartment | null = null;
    export let onSelected: (data: IDepartment | null) => void;
    export let disabled: boolean = false;
    export let excludeId: number | null = null; // Department ID to exclude from results
    export let placeholder: string | null = null;

    let searchTerm: string = "";
    let fetchedData: IDepartment[] = [];

    const getData = async () => {
        if (searchTerm.trim().length < 2) {
            fetchedData = [];
            return;
        }
        
        const res = await RepoProvider.departmentRepo.searchByText(searchTerm);
        if (res.success) {
            // Filter out excluded department if specified
            fetchedData = excludeId 
                ? res.data.filter(dept => dept.id !== excludeId)
                : res.data;
        } else {
            fetchedData = [];
        }
    };

    const debounceSearch = debounce(getData, 600);

    const doSearch = (search: string) => {
        searchTerm = search;
        debounceSearch();
    };
</script>

<SearchWithDrop
    {isLabel}
    {disabled}
    label={labelText}
    bind:searchTerm
    level="name"
    searchInput={doSearch}
    selected={selected?.name ?? null}
    selectedObj={selected}
    selectedFunc={(data) => {
        fetchedData = [];
        searchTerm = "";
        onSelected(data);
    }}
    bind:filteredData={fetchedData}
    {placeholder}
/>
