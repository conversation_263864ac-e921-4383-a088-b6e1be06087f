import type { BaseModel } from "$lib/common/models/base_model";

export enum DEPARTMENT_STATUS {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    DELETED = 'deleted'
}

export interface IDepartment extends BaseModel {
    name: string;
    status: DEPARTMENT_STATUS;
}

export interface ICreateDepartment {
    name: string;
    status: DEPARTMENT_STATUS;
}

export interface IUpdateDepartment extends ICreateDepartment {
    id: number;
}

export interface IDepartmentResponse {
    id: number;
    name: string;
    status: DEPARTMENT_STATUS;
    createdBy: string;
    createdAt: Date;
}
