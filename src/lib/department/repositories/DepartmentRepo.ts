import { DEPARTMENT_API_PATH } from "$lib/common/configs/serverConfig";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    getUnhandledErrorDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import { handleError } from "$lib/common/utils/logging";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { IDepartment, ICreateDepartment, IDepartmentResponse } from "../models/IDepartment";
import type { IDepartmentRepo } from "./IDepartmentRepo";

export class DepartmentRepo implements IDepartmentRepo {
    private _apiPath: string = DEPARTMENT_API_PATH;

    async create(payload: ICreateDepartment): Promise<DTO<IDepartment>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<IDepartment> = await fetchData(
                this._apiPath + "/create",
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async update(id: number, payload: ICreateDepartment): Promise<DTO<null>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IDepartmentResponse>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IDepartmentResponse>> = await fetchData(
                this._apiPath + "/?page=" + page + "&pageSize=" + pageSize,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async searchByText(text: string): Promise<DTO<IDepartment[]>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<{ data: IDepartment[] }> = await fetchData(
                this._apiPath + "/searchByText?text=" + text.trim(),
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!.data);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getById(id: number): Promise<DTO<IDepartmentResponse>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IDepartmentResponse> = await fetchData(
                this._apiPath + "/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async delete(ids: number[]): Promise<DTO<null>> {
        try {
            const options = {
                method: "DELETE",
                body: { ids },
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/delete",
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
}
