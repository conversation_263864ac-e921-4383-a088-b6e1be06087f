import type { DTO } from "$lib/common/models/BaseDTO";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { IDepartment, ICreateDepartment, IDepartmentResponse } from "../models/IDepartment";

export interface IDepartmentRepo {
    create(payload: ICreateDepartment): Promise<DTO<IDepartment>>;
    update(id: number, payload: ICreateDepartment): Promise<DTO<null>>;
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IDepartmentResponse>>>;
    searchByText(text: string): Promise<DTO<IDepartment[]>>;
    getById(id: number): Promise<DTO<IDepartmentResponse>>;
    delete(ids: number[]): Promise<DTO<null>>;
}
