import { z, Zod<PERSON>rror } from "zod";
import { confirmPasswordSchema, emailSchema, firstNameSchema, lastNameSchema, passwordSchema, phoneSchema, userNameSchema } from "../models/zod";
// import { type AllUser } from "$lib/users/models/User";


const credentialsSchema = z.object({
    email: emailSchema,
    password: passwordSchema,
});

const passwordMatchSchema = z.object({
    password: passwordSchema,
    confirmPassword: confirmPasswordSchema
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords must match",
    path: ["confirmPassword"], // The path to where the error message will be applied
});


// const userSchema = z.object({
//     firstName: firstNameSchema,
//     lastName: lastNameSchema,
//     phone: phoneSchema,
//     userName: userNameSchema

// });

// Function to validate credentials
const validateCredentials = (email: string, password: string) => {
    // Initialize a map to store errors
    const errorMap: Map<string, string> = new Map();

    try {
        // Attempt to parse data with the schema
        credentialsSchema.parse({ email, password });
        return errorMap;

    } catch (e: any) {
        if (e instanceof z.ZodError) {
            errorMap.clear();
            e.errors.forEach((error) => {
                const key = error.path.join('.') || 'general';
                if (!errorMap.has(key)) {
                    errorMap.set(key, error.message);
                }
            });
            return errorMap;
        } else {
            console.error("Unexpected error:", e);
            return errorMap;
        }
    }
};

const matchPassword = (password: string, confirmPassword: string): Map<string, string> => {

    // Initialize a map to store errors
    const errorMap: Map<string, string> = new Map();

    try {
        passwordMatchSchema.parse({ password, confirmPassword });
        return errorMap;
    } catch (e: any) {
        if (e instanceof z.ZodError) {
            e.errors.forEach((error) => {
                const key = error.path.join('.') || 'general';
                if (!errorMap.has(key)) {
                    errorMap.set(key, error.message);
                }
            });
            return errorMap;
        } else {
            return errorMap;
        }
    }

}

// const validateUser = (user: AllUser) => {

//     // Initialize a map to store errors
//     const errorMap: Map<string, string> = new Map();

//     try {
//         userSchema.parse({ firstName: user.firstName, lastName: user.lastName, phone: user.phoneNumber, userName: user.userName });
//         console.log("Validation succeeded");
//         return errorMap;
//     } catch (e: any) {
//         if (e instanceof z.ZodError) {
//             e.errors.forEach((error) => {
//                 const key = error.path.join('.') || 'general';
//                 if (!errorMap.has(key)) {
//                     errorMap.set(key, error.message);
//                 }
//             });
//             return errorMap;
//         } else {

//             return errorMap;
//         }
//     }
// }



function validateZodSchema<T>(schema: z.ZodSchema<T>, data: unknown): Map<string, string> {
    const errorMap = new Map<string, string>();

    try {
        schema.parse(data);
        return errorMap;
    } catch (error) {
        if (error instanceof ZodError) {
            error.errors.forEach(err => {
                if (err.path.length > 0) {
                    errorMap.set(err.path[0] as string, err.message);
                }
            });
        }
        console.log(errorMap);
        
        return errorMap;
    }

}

export { validateCredentials, matchPassword, validateZodSchema };
