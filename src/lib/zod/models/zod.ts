import { z } from 'zod';

// Define a schema for email validation
const emailSchema = z.string().email({ message: 'Invalid email address' });

// Define a schema for password validation
const passwordSchema = z.string().min(6, { message: 'Password must be at least 6 characters long' });
const confirmPasswordSchema = z.string().min(6, { message: 'Confirm Password does not match' });;
const firstNameSchema = z.string().min(2, { message: 'First name must be at least 2 characters long' });
const userNameSchema = z.string().min(5, { message: 'First name must be at least 5 characters long' });
const lastNameSchema = z.string().min(2, { message: 'Last name must be at least 2 characters long' });
const phoneSchema = z.string().min(10, { message: 'Phone number must be at least 10 characters long' });

export {
    emailSchema,
    passwordSchema,
    confirmPasswordSchema,
    firstNameSchema,
    lastNameSchema,
    phoneSchema,
    userNameSchema
}