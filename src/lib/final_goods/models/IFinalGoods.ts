import type { BaseModel, DeletedMetaData, UpdatedMetaData } from "$lib/common/models/base_model";
import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
import type { IItemAttribute, IItemAttributeValue } from "$lib/item_attributes/models/IItemAttribute";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import { number, z } from "zod";

enum FINAL_GOODS_STATUS {
    ACTIVE = "active",
    INACTIVE = "inactive",
}

/* main */
interface IFinalGoodOverview {
    id: number;
    categories: IdTitle[];
    unit: IdTitle;
    name: string;
    description: string;
    hsn: string;
    images: string[];
    status: FINAL_GOODS_STATUS;
    createdAt: Date;
    createdBy: IdTitle;
}



interface IFinalGoodVariationNested {
    id: number;
    tax: IdTitle;
    name: string;
    sku: string;
    price: number;
    expiryDays: number;
    discount: number;
    moq: number;
    msq: number;
    images: string[];
    attributeValuesIds: number[];
}

interface IFinalGoodDetailed extends IFinalGoodOverview {
    variations: IFinalGoodVariationNested[];
}




/* form states */
interface IFinalGoodAddFormState {
    finalGoods: {
        categories: IItemCategory[];
        unit: IItemUnit | null;
        name: string;
        description: string;
        imagesUrls: string[];
        imagesFiles: File[];
        hsn: string;
    };
    variations: IFinalGoodsVariationAddFormState[];
}



interface IFinalGoodsVariationAddFormState {
    name: string;
    sku: string;
    moq: number;
    msq: number;
    price: number;
    discount: number;
    taxRateId: number;
    expiryDays: number;
    imagesUrls: string[];
    imagesFiles: File[];
    attributes: {
        attributeValueId: number;
    }[];
}

interface IFinalGoodEditFormState {
    finalGoods: {
        id: number;
        categories: IItemCategory[];
        unit: IItemUnit | null;
        name: string;
        description: string;
        imagesUrls: string[];
        imagesFiles: File[];
        hsn: string;
    };
    variations: IFinalGoodsVariationEditFormState[];
}



interface IFinalGoodsVariationEditFormState {
    id: number;
    name: string;
    sku: string;
    moq: number;
    msq: number;
    price: number;
    discount: number;
    taxRateId: number;
    expiryDays: number;
    imagesUrls: string[];
    imagesFiles: File[];
    attributes: {
        attributeValueId: number;
    }[];
}


/* API payload */

interface IFinalGoodsPostPayload {
    finalGoods: {
        categoryIds: number[];
        unitId: number;
        name: string;
        description: string | null;
        images: string[] | null;
        hsn: string;
    };
    variations: {
        taxRateId: number;
        name: string;
        sku: string;
        discount: number;
        moq: number | null;
        msq: number | null;
        price: number;
        images: string[];
        attributeValuesIds: number[];
        expiryDays: number;
    }[];
}
interface IFinalGoodPutPayload{
    id: number;
    name: string;
    unitId: number;
    description: string | null;
    images: string[] | null;
    hsn: string;
    status: FINAL_GOODS_STATUS;
}
interface IFinalGoodVariationPostPayload{
    finalGoodsId?: number; // Optional for API URL construction
    variations: {
        taxRateId: number;
        name: string;
        sku: string;
        discount: number;
        moq: number | null;
        msq: number | null;
        price: number;
        images: string[];
        attributeValuesIds: number[];
        expiryDays: number;
    }[];
}
interface IFinalGoodVariationPutPayload{
    id: number;
    taxRateId: number;
    name: string;
    sku: string;
    discount: number;
    moq: number | null;
    msq: number | null;
    price: number;
    images: string[];
    attributeValuesIds: number[];
    expiryDays: number;
}
interface IFinalGoodVariationDeletePayload{
    ids: number[];
}
interface IFinalGoodDeletePayload{
    ids: number[];
}

// Additional interfaces for compatibility
interface IFinalGoodsCreatePaylod extends IFinalGoodsPostPayload {}
interface IFinalGoodsVariationUpdateStateForm extends IFinalGoodsVariationEditFormState {}

export {
    FINAL_GOODS_STATUS,
    type IFinalGoodOverview,
    type IFinalGoodDetailed,
    type IFinalGoodAddFormState,
    type IFinalGoodsVariationAddFormState,
    type IFinalGoodEditFormState,
    type IFinalGoodsVariationEditFormState,
    type IFinalGoodsPostPayload,
    type IFinalGoodPutPayload,
    type IFinalGoodVariationPostPayload,
    type IFinalGoodVariationPutPayload,
    type IFinalGoodVariationDeletePayload,
    type IFinalGoodDeletePayload,
    type IFinalGoodVariationNested,
    // Legacy exports for backward compatibility
    type IFinalGoodOverview as IFinalGoodsOverview,
    type IFinalGoodDetailed as IFinalGoods,
    type IFinalGoodAddFormState as IFinalGoodsFormState,
    type IFinalGoodsVariationAddFormState as IFinalGoodsVariationAddState,
    type IFinalGoodsPostPayload as IFinalGoodsAddRequest,
    type IFinalGoodPutPayload as IFinalGoodsUpdatePaylod,
    type IFinalGoodPutPayload as IFinalGoodsUpdateRequest,
    type IFinalGoodVariationNested as IFinalGoodsVariationNested,
    type IFinalGoodVariationNested as IFinalGoodsVariation,
    type IFinalGoodVariationPostPayload as TAddVariationRequest,
    type IFinalGoodVariationPutPayload as TUpdateVariationRequest,
    type IFinalGoodVariationDeletePayload as TDeleteVariationRequest,
    type IFinalGoodsCreatePaylod,
    type IFinalGoodsVariationUpdateStateForm,
};