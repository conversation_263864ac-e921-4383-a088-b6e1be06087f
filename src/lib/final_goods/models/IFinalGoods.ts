import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";

enum FINAL_GOODS_STATUS {
    ACTIVE = "active",
    INACTIVE = "inactive",
}

/* main */
interface IFinalGoodOverview {
    id: number;
    categories: IdTitle[];
    unit: IdTitle;
    name: string;
    description: string;
    hsn: string;
    images: string[];
    status: FINAL_GOODS_STATUS;
    createdAt: Date;
    createdBy: IdTitle;
    [key: string]: any; // Index signature for table access
}



interface IFinalGoodVariationNested {
    id: number;
    tax: IdTitle;
    taxRateId: number;
    name: string;
    sku: string;
    price: number;
    expiryDays: number;
    discount: number;
    moq: number;
    msq: number;
    images: string[];
    attributeValuesIds: number[];
    attributes: {
        attribute: any;
        attributeValue: any;
    }[];
}

interface IFinalGoodVariationOverview {
    id: number;
    finalGoodsId: number;
    tax: IdTitle;
    taxRateId: number;
    name: string;
    sku: string;
    price: number;
    expiryDays: number;
    discount: number;
    moq: number;
    msq: number;
    images: string[];
    attributeValuesIds: number[];
}

interface IFinalGoodVariationNested {
    id: number;
    tax: IdTitle;
    taxRateId: number;
    name: string;
    sku: string;
    price: number;
    expiryDays: number;
    discount: number;
    moq: number;
    msq: number;
    images: string[];
    attributeValuesIds: number[];
    attributes: {
        attribute: any;
        attributeValue: any;
    }[];
}

interface IFinalGoodDetailed extends IFinalGoodOverview {
    variations: IFinalGoodVariationNested[];
}




/* form states */
interface IFinalGoodAddFormState {
    finalGoods: {
        categories: IItemCategory[];
        unit: IItemUnit | null;
        name: string;
        description: string;
        imagesUrls: string[];
        imagesFiles: File[];
        hsn: string;
    };
    variations: IFinalGoodsVariationAddFormState[];
}



interface IFinalGoodsVariationAddFormState {
    name: string;
    sku: string;
    moq: number;
    msq: number;
    price: number;
    discount: number;
    taxRateId: number;
    expiryDays: number;
    imagesUrls: string[];
    imagesFiles: File[];
    attributes: {
        attributeValueId: number;
    }[];
}

interface IFinalGoodEditFormState {
    finalGoods: {
        id: number;
        categories: IItemCategory[];
        unit: IItemUnit | null;
        name: string;
        description: string;
        imagesUrls: string[];
        imagesFiles: File[];
        hsn: string;
    };
    variations: IFinalGoodsVariationEditFormState[];
}



interface IFinalGoodsVariationEditFormState {
    id: number;
    name: string;
    sku: string;
    moq: number;
    msq: number;
    price: number;
    discount: number;
    taxRateId: number;
    expiryDays: number;
    imagesUrls: string[];
    imagesFiles: File[];
    attributes: {
        attributeValueId: number;
    }[];
}


/* API payload */

interface IFinalGoodsPostPayload {
    finalGoods: {
        categoryIds: number[];
        unitId: number;
        name: string;
        description: string | null;
        images: string[] | null;
        hsn: string;
    };
    variations: {
        taxRateId: number;
        name: string;
        sku: string;
        discount: number;
        moq: number | null;
        msq: number | null;
        price: number;
        images: string[];
        attributeValuesIds: number[];
        expiryDays: number;
    }[];
}
interface IFinalGoodPutPayload{
    id: number;
    name: string;
    unitId: number;
    categoryIds: number[];
    description: string | null;
    images: string[] | null;
    hsn: string;
    status: FINAL_GOODS_STATUS;
}
interface IFinalGoodVariationPostPayload{
    finalGoodsId?: number; // Optional for API URL construction
    variations: {
        taxRateId: number;
        name: string;
        sku: string;
        discount: number;
        moq: number | null;
        msq: number | null;
        price: number;
        images: string[];
        attributeValuesIds: number[];
        expiryDays: number;
    }[];
}
interface IFinalGoodVariationPutPayload{
    id: number;
    taxRateId: number;
    name: string;
    sku: string;
    discount: number;
    moq: number | null;
    msq: number | null;
    price: number;
    images: string[];
    attributeValuesIds: number[];
    expiryDays: number;
}
interface IFinalGoodVariationDeletePayload{
    ids: number[];
}
interface IFinalGoodDeletePayload{
    ids: number[];
}



export {
    FINAL_GOODS_STATUS,
    type IFinalGoodOverview,
    type IFinalGoodDetailed,
    type IFinalGoodVariationOverview,
    type IFinalGoodVariationNested,
    type IFinalGoodAddFormState,
    type IFinalGoodsVariationAddFormState,
    type IFinalGoodEditFormState,
    type IFinalGoodsVariationEditFormState,
    type IFinalGoodsPostPayload,
    type IFinalGoodPutPayload,
    type IFinalGoodVariationPostPayload,
    type IFinalGoodVariationPutPayload,
    type IFinalGoodVariationDeletePayload,
    type IFinalGoodDeletePayload,
};