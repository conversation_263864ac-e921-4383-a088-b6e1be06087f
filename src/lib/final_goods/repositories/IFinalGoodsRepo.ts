import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type {  
    IFinalGoodsVariation, 
    IFinalGoodsUpdatePaylod, 
    IFinalGoodsAddRequest, 
    IFinalGoodsOverview, 
    IFinalGoods, 
    TAddVariationRequest, 
    TDeleteVariationRequest, 
    TUpdateVariationRequest, 
    IFinalGoodsVariationNested 
} from "../models/IFinalGoods";

export interface IFinalGoodsRepo {
    create(payload: IFinalGoodsAddRequest): Promise<DTO<null>>;
    updateFinalGoods(payload: IFinalGoodsUpdatePaylod): Promise<DTO<null>>;
    deleteFinalGoods(ids: number[]): Promise<DTO<null>>;
    deleteFinalGoodsById(id: number): Promise<DTO<null>>;
    getFinalGoodsById(id: number): Promise<DTO<IFinalGoods>>;
    getFinalGoodsVariationById(id: number): Promise<DTO<IFinalGoodsVariation>>;
    getFinalGoodss(page: number, pageSize: number,text?:string): Promise<DTO<PaginatedBaseResponse<IFinalGoodsOverview>>>;
    addVariation(payload: TAddVariationRequest[]): Promise<DTO<IFinalGoodsVariationNested[] | null>>;
    updateVariation(payload: TUpdateVariationRequest): Promise<DTO<IFinalGoodsVariationNested | null>>;
    deleteVariation(payload: TDeleteVariationRequest): Promise<DTO<null>>;
    getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IFinalGoodsVariation>>>;
}