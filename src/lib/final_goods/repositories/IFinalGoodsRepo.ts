import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type {
    IFinalGoodVariationOverview,
    IFinalGoodPutPayload,
    IFinalGoodsPostPayload,
    IFinalGoodOverview,
    IFinalGoodDetailed,
    IFinalGoodVariationPostPayload,
    IFinalGoodVariationDeletePayload,
    IFinalGoodVariationPutPayload,
    IFinalGoodVariationNested
} from "../models/IFinalGoods";

export interface IFinalGoodsRepo {
    create(payload: IFinalGoodsPostPayload): Promise<DTO<null>>;
    updateFinalGoods(payload: IFinalGoodPutPayload): Promise<DTO<null>>;
    deleteFinalGoods(ids: number[]): Promise<DTO<null>>;
    deleteFinalGoodsById(id: number): Promise<DTO<null>>;
    getFinalGoodsById(id: number): Promise<DTO<IFinalGoodDetailed>>;
    getFinalGoodsVariationById(id: number): Promise<DTO<IFinalGoodVariationOverview>>;
    getFinalGoodss(page: number, pageSize: number,text?:string): Promise<DTO<PaginatedBaseResponse<IFinalGoodOverview>>>;
    addVariation(payload: IFinalGoodVariationPostPayload): Promise<DTO<IFinalGoodVariationNested[] | null>>;
    updateVariation(payload: IFinalGoodVariationPutPayload): Promise<DTO<IFinalGoodVariationNested | null>>;
    deleteVariation(payload: IFinalGoodVariationDeletePayload): Promise<DTO<null>>;
    getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IFinalGoodVariationOverview>>>;
}