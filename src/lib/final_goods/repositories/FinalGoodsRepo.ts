import { FINAL_GOODS_API_PATH, FINAL_GOODS_VARIATION_API_PATH } from "$lib/common/configs/serverConfig";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    getUnhandledErrorDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { handleError } from "$lib/common/utils/logging";
import type { IFinalGoodsRepo, } from "./IFinalGoodsRepo";
import type {
    IFinalGoodPutPayload,
    IFinalGoodsPostPayload,
    IFinalGoodOverview,
    IFinalGoodDetailed,
    IFinalGoodVariationOverview,
    IFinalGoodVariationNested,
    IFinalGoodVariationPostPayload,
    IFinalGoodVariationDeletePayload,
    IFinalGoodVariationPutPayload
} from "../models/IFinalGoods";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class FinalGoodsRepo implements IFinalGoodsRepo {
    private _apiPath: string = FINAL_GOODS_API_PATH;
    private _apiPathVariation: string = FINAL_GOODS_VARIATION_API_PATH;

    async create(payload: IFinalGoodsPostPayload): Promise<DTO<null>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/create",
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async updateFinalGoods(payload: IFinalGoodPutPayload): Promise<DTO<null>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/update/" + payload.id,
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async deleteFinalGoods(ids: number[]): Promise<DTO<null>> {
        try {
            const options = {
                method: "DELETE",
                body: {
                    ids: ids,
                },
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/",
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async deleteFinalGoodsById(id: number): Promise<DTO<null>> {
        try {
            const options = {
                method: "DELETE",
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/delete/" +id,
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getFinalGoodsById(id: number): Promise<DTO<IFinalGoodDetailed>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IFinalGoodDetailed> = await fetchData(
                this._apiPath + "/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async getFinalGoodsVariationById(id: number): Promise<DTO<IFinalGoodVariationOverview>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IFinalGoodVariationOverview> = await fetchData(
                this._apiPath + "/variation/" + id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getFinalGoodss(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IFinalGoodOverview>>> {
        try {
            const options = {
                method: "GET",
            };
            let query = "?page=" + page + "&pageSize=" + pageSize;
            if (text) {
                query = query + "&text=" + text;
            }
            const res: FetchResult<PaginatedBaseResponse<IFinalGoodOverview>> = await fetchData(this._apiPath + query, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IFinalGoodVariationOverview>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IFinalGoodVariationOverview>> = await fetchData(this._apiPath + "/searchByText?text=" + text, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

   
    async getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IFinalGoodVariationOverview>>> {
        try {
            const options = {
                method: "GET",
            };
            let query = "?page=" + page + "&pageSize=" + pageSize;
            if (text) {
                query = query + "&text=" + text;
            }
            if (supplierId) {
                query = query + "&supplierId=" + supplierId;
            }

            let url = this._apiPathVariation + "/"+query;
            const res: FetchResult<PaginatedBaseResponse<IFinalGoodVariationOverview>> = await fetchData(url, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async addVariation(payload: IFinalGoodVariationPostPayload): Promise<DTO<IFinalGoodVariationNested[] | null>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<IFinalGoodVariationNested[] | null> = await fetchData(
                this._apiPathVariation + "/add-new-variation/" + payload.finalGoodsId,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async updateVariation(payload: IFinalGoodVariationPutPayload): Promise<DTO<IFinalGoodVariationNested | null>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<IFinalGoodVariationNested | null> = await fetchData(
                this._apiPathVariation + "/update-variation/" + payload.id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async deleteVariation(payload: IFinalGoodVariationDeletePayload): Promise<DTO<null>> {
        try {
            const options = {
                method: "DELETE",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPathVariation+"/variation",
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }


}
