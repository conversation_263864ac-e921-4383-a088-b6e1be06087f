<script lang="ts">
    import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import type { IFinalGoodsVariationAddFormState } from "../models/IFinalGoods";
    import { goto } from "$app/navigation";
    import { PresenterProvider } from "$lib/PresenterProvider";

    import { showErrorToast } from "$lib/common/utils/common-utils";
    import AttributesSelection from "$lib/common/components/AttributesSelection.svelte";
    import RedRoundCloseButton from "$lib/common/components/RedRoundCloseButton.svelte";
    import { Label, Input } from "flowbite-svelte";
    let taxItems: any;
    import type {
        IItemAttribute,
        IItemAttributeValue,
    } from "$lib/item_attributes/models/IItemAttribute";
    import { afterUpdate } from "svelte";
    import ImageUploaderDirect from "$lib/common/components/ImageUploaderDirect.svelte";
    import ImageUploader from "$lib/common/components/ImageUploader.svelte";
    import type { ITax } from "$lib/tax/models/Tax";
    import DropdownGen from "$lib/item_category/components/DropdownGen.svelte";
    export let variations: IFinalGoodsVariationAddFormState[] = [];
    export let variationErrors: IndexedValidationErrors = new Map();
    export let collapseAll = false;

    let expandedVariations: boolean[] = [];
    const loadTax = async () => {
        const response = await PresenterProvider.taxPresenter.onLoad(1, 10, "");
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            taxItems = response.data.data;
            if (taxItems.length === 0) {
                showErrorToast("Please add at least one unit to add a final goods", 6);
                return goto("/admin/tax/add");
            }
            console.log(taxItems);
        }
        return taxItems;
    };

    // Track expanded state for each variation
    const handleVariationNumberField = (
        e: Event,
        index: number,
        field: keyof (typeof variations)[number]
    ) => {
        if (!variations) {
            return showErrorToast("Please add variation first");
        }

        const value = parseFloat((e.target as HTMLInputElement).value);
        (variations[index][field] as unknown as number) = isNaN(value) ? 0 : value;
    };

    const handleAttributeSelection = (
        data: { attribute: IItemAttribute; attributeValue: IItemAttributeValue }[],
        variationIndex: number
    ) => {
        if (!variations) {
            return showErrorToast("Please add variation first");
        }
        variations[variationIndex].attributes = data.map((item) => ({
            attributeValueId: item.attributeValue.id,
        }));
        variations = variations;
    };

    const toggleVariation = (index: number) => {
        expandedVariations[index] = !expandedVariations[index];
        expandedVariations = expandedVariations;
    };

    $: if (variations) {
        while (expandedVariations.length < variations.length) {
            expandedVariations.push(true);
        }
        if (expandedVariations.length > variations.length) {
            expandedVariations = expandedVariations.slice(0, variations.length);
        }
    }

    afterUpdate(() => {
        if (collapseAll) {
            expandedVariations = expandedVariations.map(() => false);
            collapseAll = false;
        }
    });
</script>

<div class="space-y-6">
    {#each variations as variation, index (index)}
        <div
            class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
        >
            <!-- Header with expand/collapse and remove buttons -->
            <div
                class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700"
            >
                <div class="flex justify-between items-center gap-3 w-full">
                    <span class="text-2xl font-bold text-gray-600 dark:text-gray-400">
                        Variation {index + 1}
                    </span>
                    <button
                        type="button"
                        class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        on:click={() => toggleVariation(index)}
                        aria-label={expandedVariations[index]
                            ? "Collapse variation details"
                            : "Expand variation details"}
                        title={expandedVariations[index] ? "Collapse" : "Expand"}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 transition-transform duration-200 {expandedVariations[
                                index
                            ]
                                ? 'rotate-90'
                                : ''}"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 5l7 7-7 7"
                            />
                        </svg>
                    </button>
                </div>
                <div class="flex items-center gap-2">
                    {#if variations.length > 1}
                        <RedRoundCloseButton
                            onClick={() => {
                                variations?.splice(index, 1);
                                variations = variations; // Trigger reactivity
                            }}
                        />
                    {/if}
                </div>
            </div>

            <!-- Basic fields in 3-column grid -->
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Name -->
                    <div>
                        <Label for="name-{index}" class="mb-2">Name</Label>
                        <Input
                            type="text"
                            id="name-{index}"
                            placeholder="Name"
                            bind:value={variation.name}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "name")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "name")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- SKU -->
                    <div>
                        <Label for="sku-{index}" class="mb-2">SKU</Label>
                        <Input
                            type="text"
                            id="sku-{index}"
                            placeholder="SKU"
                            bind:value={variation.sku}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "sku")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "sku")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- MSQ -->
                    <div>
                        <Label for="msq-{index}" class="mb-2">MSQ</Label>
                        <Input
                            type="number"
                            id="msq-{index}"
                            placeholder="MSQ"
                            value={variation.msq}
                            on:input={(e) => handleVariationNumberField(e, index, "msq")}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "msq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "msq")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- MOQ -->
                    <div>
                        <Label for="moq-{index}" class="mb-2">MOQ</Label>
                        <Input
                            type="number"
                            id="moq-{index}"
                            placeholder="MOQ"
                            value={variation.moq}
                            on:change={(e) => {
                                handleVariationNumberField(e, index, "moq");
                            }}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "moq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "moq")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- Price -->
                    <div>
                        <Label for="price-{index}" class="mb-2">Price</Label>
                        <Input
                            type="number"
                            id="price-{index}"
                            placeholder="Price"
                            value={variation.price}
                            on:input={(e) => handleVariationNumberField(e, index, "price")}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "price")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "price")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- Discount -->
                    <div>
                        <Label for="discount-{index}" class="mb-2">Discount (Optional)</Label>
                        <Input
                            type="number"
                            id="discount-{index}"
                            placeholder="Discount"
                            value={variation.discount}
                            on:input={(e) => handleVariationNumberField(e, index, "discount")}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "discount")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "discount")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- Expire Days -->
                    <div>
                        <Label for="expire-days-{index}" class="mb-2">Expire Days</Label>
                        <Input
                            type="number"
                            id="expire-days-{index}"
                            placeholder="Expire Days"
                            value={variation.expiryDays}
                            on:input={(e) => handleVariationNumberField(e, index, "expiryDays")}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "expire_days")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "expire_days")!.errorMessage}
                            </p>
                        {/if}
                    </div>

                    <!-- Tax -->
                    <div>
                        <Label for="tax-{index}" class="mb-2">Tax</Label>
                        <DropdownGen loadItems={loadTax} bind:value={variation.taxRateId} />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "taxRateId")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "taxRateId")!.errorMessage}
                            </p>
                        {/if}
                    </div>
                </div>
            </div>

            <!-- Expanded section for attributes and images -->
            {#if expandedVariations[index]}
                <div
                    class="border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-750"
                >
                    <div class="p-4">
                        <!-- Attributes section -->
                        <div class="mb-6">
                            <div class="flex items-center gap-2 mb-3">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5 text-blue-600"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                                    />
                                </svg>
                                <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300">
                                    Attributes
                                </h4>
                            </div>
                            <div
                                class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600"
                            >
                                <AttributesSelection
                                    existingData={new Array()}
                                    onSelect={(data) => {
                                        handleAttributeSelection(data, index);
                                    }}
                                />
                                {#if variationErrors.has(index) && variationErrors
                                        .get(index)!
                                        .find((item) => item.fieldName === "attributes")}
                                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                        {variationErrors
                                            .get(index)!
                                            .find((item) => item.fieldName === "attributes")!
                                            .errorMessage}
                                    </p>
                                {/if}
                            </div>
                        </div>

                        <!-- Images section -->
                        <div class="mb-4">
                            <div class="flex items-center gap-2 mb-3">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5 text-green-600"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                    />
                                </svg>
                                <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300">
                                    Images
                                </h4>
                            </div>
                            <div
                                class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600"
                            >
                                <ImageUploaderDirect
                                    bind:files={variation.imagesFiles}
                                    multiple={true}
                                    showDeleteButtons={true}
                                />
                                {#if variationErrors.has(index) && variationErrors
                                        .get(index)!
                                        .find((item) => item.fieldName === "images")}
                                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                        {variationErrors
                                            .get(index)!
                                            .find((item) => item.fieldName === "images")!
                                            .errorMessage}
                                    </p>
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    {/each}
</div>
