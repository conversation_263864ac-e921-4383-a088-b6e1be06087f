<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input } from "flowbite-svelte";
    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import {
        type IFinalGoods,
        // type IFinalGoodsFormState,
        type IFinalGoodsUpdatePaylod,
        type IFinalGoodsVariationAddFormState,
        type IFinalGoodsVariationAddState,
        type IFinalGoodsVariationNested,
        type TAddVariationRequest,
        type IFinalGoodsUpdateRequest,
        type TDeleteVariationRequest,
        type IFinalGoodsAddRequest,
        type TUpdateVariationRequest,
        type IFinalGoodsVariationEditFormState,
        FINAL_GOODS_STATUS,
    } from "../models/IFinalGoods";
    import { FinalGoodsEditUtils } from "../utils/FinalGoodsEditUtils";
    import { FinalGoodsUtils } from "../utils/FinalGoodsUtils";
    import { onMount } from "svelte";
    import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import ItemUnitDropdown from "$lib/item_unit/components/ItemUnitDropdown.svelte";
    import MulltiItemCategorySelect from "$lib/item_category/components/MulltiItemCategorySelect.svelte";
    import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import FinalGoodsVariationTable from "./FinalGoodsVariationTable.svelte";
    import FinalGoodsVariationItem from "./FinalGoodsVariationItem.svelte";
    import { openConfirmDialog } from "$lib/common/utils/custom_confirmation_component_store";
    import { closeActivity, showActivity } from "$lib/common/utils/activity_store";
    import ImageUploaderDirect from "$lib/common/components/ImageUploaderDirect.svelte";

    let imageFiles: File[] = [];
    const uploadImagesFn = async (files: File[]) => {
        // debugger;
        const data = await PresenterProvider.S3Uploader.upload(files, "final-goods");
        if (!data) {
            return [];
        }
        return data.data;
    };
    export let finalGoods: IFinalGoods;
    export let isInsideModal: boolean = false;
    export let onUpdateSuccess: (() => void) | null = null;
    export let addNewVariation: boolean = false;

    let formState: any = FinalGoodsEditUtils.getEmptyFormState();
    let newVariations: IFinalGoodsVariationAddState[] = [];
    let isDoingTask: boolean = false;
    let isLoadingUnits: boolean = true;
    let validationErrors: ValidationErrors = new Map();
    let itemUnits: IItemUnit[] = [];
    let variationErrors: IndexedValidationErrors = new Map();
    let notifySavedState = false;
    let deletedImages: string[] = [];

    // this function  is being used somewhere need to understand{
    const handleUpdateFinalGoods = async () => {
        // console.log(formState.finalGoods)
        let NewImages: string[] = (await uploadImagesFn(imageFiles)) ?? [];
        
        // Remove deleted images from the formState
        const updatedImages = formState.finalGoods.images.filter(
            (image: string) => !deletedImages.includes(image)
        );
        
        // Add new images to the updated list
        formState.finalGoods.images = [...updatedImages, ...NewImages];
        console.log(formState.finalGoods.images);
        // formState.finalGoods.images = await uploadImagesFn(imageFiles)
        validationErrors = FinalGoodsEditUtils.validateFinalGoodsUpdate({
            id: finalGoods.id,
            name: formState.finalGoods.name ?? "",
            unitId: formState.finalGoods.unit?.id ?? -1,
            description: formState.finalGoods.description,
            // categoryIds not supported in PUT payload
            images: formState.finalGoods.images ?? [],
            hsn: formState.finalGoods.hsn,
            status: FINAL_GOODS_STATUS.ACTIVE,
        });
        if (validationErrors.size !== 0) {
            console.log(validationErrors);
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        // console.log(formState,formState.finalGoods.id,finalGoods.status)
        const payload: IFinalGoodsUpdatePaylod = FinalGoodsEditUtils.prepareFinalGoodsUpdatePayload(
            formState,
            finalGoods.id,
            finalGoods.status
        );
        // console.log(payload);
        isDoingTask = true;
        showActivity("Updating Final goods...");
        let res = await PresenterProvider.finalGoodsPresenter.onUpdateFinalGoods(payload);
        closeActivity();
        if (res.success) {
            showSuccessToast("final goods updated successfully");
            if (onUpdateSuccess) {
                onUpdateSuccess();
            } else {
                await goto("/admin/final-goods");
            }
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };
    // }

    const _loadUnits = async () => {
        // load the units field values
        const response = await PresenterProvider.finalGoodsPresenter.getItemUnits(1, 1000);
        if (!response.success) {
            // if not found
            showErrorToast(response.message);
        } else {
            itemUnits = response.data.data;
            if (itemUnits.length === 0) {
                // incase the units is empty
                showErrorToast("Please add at least one unit to edit a final goods", 6);
                return goto("/admin/item-units/add");
            }

            isLoadingUnits = false;
        }
    };

    const deleteFinalGoods = async () => {
        isDoingTask = true;
        let res = await PresenterProvider.finalGoodsPresenter.onDeleteFinalGoodsById(finalGoods.id);
        if (res.success) {
            showSuccessToast("final goods deleted successfully");
            if (onUpdateSuccess) {
                onUpdateSuccess();
            } else {
                await goto("/admin/final-goods");
            }
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };

    const handleDeleteVariation = async (variation: IFinalGoodsVariationNested) => {
        const payload: TDeleteVariationRequest = {
            ids: [variation.id],
        };
        isDoingTask = true;
        showActivity("Deleting variation...");
        let res = await PresenterProvider.finalGoodsPresenter.deleteVariation(payload);
        closeActivity();
        if (res.success) {
            showSuccessToast("Variation deleted successfully");
            finalGoods.variations = finalGoods.variations.filter(
                (item) => item.id !== variation.id
            );
            finalGoods = finalGoods;
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };
    const handleAddNewVariation = () => {
        if (!addNewVariation) {
            addNewVariation = true;
            return;
        }
        const newVariation = FinalGoodsUtils.getEmptyVariation();
        newVariations.push(newVariation);
        newVariations = newVariations;
    };
    const handleSaveNewVariations = async () => {
        if (newVariations.length === 0) {
            showErrorToast("Please add at least one variation");
            return;
        }
        if(newVariations.some((item)=> item.imagesFiles!.length === 0)){
            showErrorToast("Please add at least one image for each variation");
            return;
        }
        
        const uploadedImages = await Promise.all(
          newVariations.map((item) => uploadImagesFn(item.imagesFiles ?? []))
        );

        // Assign back uploaded URLs
        newVariations.forEach((item, index) => {
          item.imagesUrls = uploadedImages[index] ?? [];
        });

        const payload: any = newVariations.map((item) => {
            return {
                name: item.name,
                sku: item.sku,
                msq: item.msq,
                moq: item.moq,
                expire_days: item.expiryDays,
                price: item.price,
                discount: item.discount,
                taxRateId: item.taxRateId,
                attributes: (item.attributes ?? []).map((attribute) => {
                    return {
                        attributeValueId: attribute.attributeValueId,
                    };
                }),
            };
        });

        variationErrors = FinalGoodsUtils.validateVariations(payload);
        // if (variationErrors.size !== 0) {
        //     showErrorToast("Please fill the required fields correctly");
        //     return;
        // }
        isDoingTask = true;
        showActivity("Adding variations...");
        const savePayload: TAddVariationRequest = {
            finalGoodsId: finalGoods.id,
            variations: newVariations.map((item) => ({
                name: item.name,
                sku: item.sku,
                msq: item.msq,
                moq: item.moq,
                expiryDays: item.expiryDays,
                images: item.imagesUrls ?? [],
                price: item.price,
                discount: item.discount,
                taxRateId: item.taxRateId,
                attributeValuesIds: (item.attributes ?? []).map(
                    (attribute: { attributeValueId: number }) => {
                        return attribute.attributeValueId;
                    }
                ),
            }))
        };
        let res = await PresenterProvider.finalGoodsPresenter.addVariation(savePayload);
        closeActivity();
        if (res.success) {
            notifySavedState = false;
            showSuccessToast("Variations added successfully");
            finalGoods.variations.push(...res.data!);
            finalGoods.variations = finalGoods.variations;
            newVariations = [FinalGoodsUtils.getEmptyVariation()];
            finalGoods = finalGoods;
            notifySavedState = true;
        } else {
            showErrorToast(res.message);
        }
        isDoingTask = false;
    };
    onMount(() => {
        _loadUnits();
        formState = FinalGoodsEditUtils.convertToFormState(finalGoods);
        newVariations.push(FinalGoodsUtils.getEmptyVariation());
        newVariations = newVariations;
    });
    $: () => {
        console.log(formState.finalGoods.images);
    };
</script>

{#if isLoadingUnits}
    <PageLoader />
{:else}
    <div class="flex items-center justify-center">
        <div class=" w-[90vw] p-2">
            {#if !isInsideModal}
                <div class=" flex items-center justify-between py-2">
                    <FormHeader label={"Edit final goods"}></FormHeader>
                    <BreadCrumbs breadCrumbData={[]} />
                </div>
                <hr class="mb-5" />
            {/if}

            <!-- final goods Basic Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                    final goods Information
                </h2>

                <div class="m-2"></div>

                <div class=" grid grid-cols-2 gap-6">
                    <div class="categoryIds">
                        <MulltiItemCategorySelect
                            onSelected={(data) => {
                                if (data) {
                                    formState.finalGoods.categoryIds = data;
                                    // console.log(data)
                                } else {
                                    formState.finalGoods.categoryIds = null;
                                }
                            }}
                            selected={formState.finalGoods.categoryIds}
                        />

                        {#if validationErrors.has("categoryIds")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("categoryIds")}
                            </p>
                        {/if}
                    </div>
                    <div>
                        <Label for="name" class="mb-2 font-sans capitalize tracking-[0px]">
                            Name
                            {#if validationErrors.has("name")}
                                <span class="text-red-600">*</span>
                            {/if}
                        </Label>
                        <Input
                            type="text"
                            id="name"
                            placeholder="Name"
                            class="dark:bg-primary-700 {validationErrors.has('name')
                                ? 'border-red-500'
                                : ''}"
                            bind:value={formState.finalGoods.name}
                        />
                        {#if validationErrors.has("name")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("name")}
                            </p>
                        {/if}
                    </div>

                    <div class="description">
                        <Label for="description" class="mb-2 font-sans capitalize tracking-[0px]">
                            Description
                            {#if validationErrors.has("description")}
                                <span class="text-red-600">*</span>
                            {/if}
                        </Label>
                        <textarea
                            id="description"
                            placeholder="description"
                            class="dark:bg-primary-700 resize-none border-gray-300 h-[45px] bg-[#F9FAFB] w-full rounded-xl px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 {validationErrors.has(
                            'description'
                        )
                            ? 'border-red-500'
                            : ''}"
                            bind:value={formState.finalGoods.description}
                        ></textarea>
                        {#if validationErrors.has("description")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("description")}
                            </p>
                        {/if}
                    </div>
                    <div class="hsn">
                        <Label for="hsn" class="mb-2 font-sans capitalize tracking-[0px]">
                            HSN
                            {#if validationErrors.has("hsn")}
                                <span class="text-red-600">*</span>
                            {/if}
                        </Label>
                        <Input
                            type="text"
                            id="hsn"
                            placeholder="hsn"
                            class="dark:bg-primary-700 {validationErrors.has('hsn')
                                ? 'border-red-500'
                                : ''}"
                            bind:value={formState.finalGoods.hsn}
                        />
                        {#if validationErrors.has("hsn")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("hsn")}
                            </p>
                        {/if}
                    </div>

                    <div class="unitblock">
                        <Label for="unit" class="mb-2 font-sans capitalize tracking-[0px]">
                            Unit
                            {#if validationErrors.has("unitId")}
                                <span class="text-red-600">*</span>
                            {/if}
                        </Label>

                        <ItemUnitDropdown
                            data={itemUnits}
                            id="unit"
                            cssClass="dark:bg-primary-700 {validationErrors.has('unitId')
                                ? 'border-red-500'
                                : ''}"
                            selectedValue={formState.finalGoods.unit}
                            onSelected={(data) => {
                                formState.finalGoods.unit = data;
                            }}
                        />

                        {#if validationErrors.has("unitId")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("unitId")}
                            </p>
                        {/if}
                    </div>

                    <div class=".image-uploader col-span-2">
                        <ImageUploaderDirect
                            bind:initialUrls={formState.finalGoods.images}
                            bind:deletedImages
                            bind:files={imageFiles}
                            multiple={true}
                            showDeleteButtons={true}
                        />
                        {#if validationErrors.has("image-uploader")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("image-uploader")}
                            </p>
                        {/if}
                        <!-- onUploaded={handleUploaded}
                uploadFn={mockUploadFn}
                onError={handleError} -->
                    </div>
                </div>

                <div class="m-2"></div>

                <div class=" grid grid-cols-2 gap-6"></div>

                <div class="mt-5 flex justify-end gap-2">
                    <CustomButton
                        onClick={() => {
                            openConfirmDialog({
                                heading: "Delete final goods",
                                body: "Are you sure you want to delete this final goods?",
                                positiveButtonText: "Yes",
                                negativeButtonText: "No",
                                positiveAction: deleteFinalGoods,
                            });
                        }}
                        cssClass="w-40 !bg-red-600 hover:bg-red-700"
                        title={"Delete"}
                    />
                    <CustomButton
                        onClick={handleUpdateFinalGoods}
                        cssClass="w-40 bg-blue-600 hover:bg-blue-700"
                        title={"Update"}
                        isLoading={isDoingTask}
                    />
                </div>
            </div>
            <!-- Existing Variations Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                    Existing Variations
                </h2>
                {#if finalGoods.variations && finalGoods.variations.length > 0}
                    <div class="space-y-4">
                        {#each finalGoods.variations.filter((v) => v.id !== undefined) as variation, index (variation.id)}
                            <FinalGoodsVariationItem
                                totalVariations={finalGoods.variations.length}
                                parentFinalGoodsId={finalGoods.id}
                                variation={{ ...variation, id: variation.id as number }}
                                onDelete={() =>
                                    openConfirmDialog({
                                        heading: "Delete Variation!",
                                        body: "Are you sure you want to delete this variation?",
                                        positiveButtonText: "Yes",
                                        negativeButtonText: "No",
                                        positiveAction: () =>
                                            handleDeleteVariation(variation as any),
                                    })}
                                onEditSuccess={(variation) => {
                                    finalGoods.variations[index] = variation;
                                    finalGoods.variations = finalGoods.variations;
                                }}
                            />
                        {/each}
                    </div>
                {:else}
                    <p class="text-gray-500 dark:text-gray-400 text-center py-8">
                        No existing variations found.
                    </p>
                {/if}
            </div>
            <!-- New Variations Section -->
            {#if newVariations.length > 0 && addNewVariation}
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                        Add New Variations
                    </h2>

                    <div class="overflow-x-auto">
                        <FinalGoodsVariationTable
                            variations={newVariations}
                            {variationErrors}
                            collapseAll={notifySavedState}
                        />
                    </div>
                </div>
            {/if}
            <!-- Add New Variation Button -->
            <div class="flex justify-between mb-6 gap-2">
                <CustomButton
                    title="Add New Variation"
                    cssClass="bg-green-600 hover:bg-green-700 text-white px-6 py-3"
                    onClick={handleAddNewVariation}
                />
                {#if addNewVariation && newVariations.length > 0}
                    <CustomButton
                        title="Save variations"
                        cssClass="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
                        onClick={handleSaveNewVariations}
                        isLoading={isDoingTask}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}
