<script lang="ts">
    import { Input, Spinner } from "flowbite-svelte";
    import { goto } from "$app/navigation";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import type {
        IFinalGoodsVariationAddFormState,
        IFinalGoodVariationNested,
        IFinalGoodVariationPutPayload,
    } from "../models/IFinalGoods";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import AttributesSelection from "$lib/common/components/AttributesSelection.svelte";
    import RedRoundCloseButton from "$lib/common/components/RedRoundCloseButton.svelte";
    import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import { onMount } from "svelte";
    import { FinalGoodsUtils } from "../utils/FinalGoodsUtils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import type {
        IItemAttribute,
        IItemAttributeValue,
    } from "$lib/item_attributes/models/IItemAttribute";
    import { closeActivity, showActivity } from "$lib/common/utils/activity_store";
    import ImageUploaderDirect from "$lib/common/components/ImageUploaderDirect.svelte";
    import DropdownGen from "$lib/item_category/components/DropdownGen.svelte";

    export let variation: IFinalGoodVariationNested;
    export let parentFinalGoodsId: number;
    export let totalVariations: number;
    export let onEditSuccess: (variation: IFinalGoodVariationNested) => void;
    export let onDelete: () => void;
    let variationState: IFinalGoodsVariationAddFormState;
    let isPreparingData = true;
    let isExpanded: boolean = false;
    let isEditing: boolean = false;
    let validationErrors: ValidationErrors = new Map();
    let taxItems: any;
    const loadTax = async () => {
        const response = await PresenterProvider.taxPresenter.onLoad(1, 10, "");
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            taxItems = response.data.data;
            if (taxItems.length === 0) {
                showErrorToast("Please add at least one unit to add a final goods", 6);
                return goto("/admin/tax/add");
            }
            console.log(taxItems);
        }
        return taxItems;
    };
    let imageFiles: File[] = [];
    let deletedImages: string[] = [];
    const handleAttributeSelection = (
        data: { attribute: IItemAttribute; attributeValue: IItemAttributeValue }[]
    ) => {
        console.log(variationState);
        variationState.attributes = data.map((item) => ({
            attributeValueId: Number(item.attributeValue.id),
        }));
        console.log(variationState);
        variationState = variationState;
    };

    const toggleExpand = () => {
        isExpanded = !isExpanded;
    };

    const uploadImagesFn = async (files: File[] | undefined) => {
        // debugger;
        if (!files) {
            return [];
        }
        const data = await PresenterProvider.S3Uploader.upload(files, "final-goods");
        if (!data) {
            return [];
        }
        return data.data;
    };
    const updateVariation = async () => {
        console.log(variationState.attributes);
        const newImages = (await uploadImagesFn(imageFiles)) ?? [];

        // Remove deleted images from the variationState
        const updatedImages = variationState.imagesUrls!.filter(
            (image: string) => !deletedImages.includes(image)
        );

        // Add new images to the updated list
        variationState.imagesUrls = [...updatedImages, ...newImages];
        const payload: IFinalGoodVariationPutPayload = {
            id: variation.id,
            // FinalGoodsId not needed in PUT payload
            name: variationState.name,
            sku: variationState.sku,
            msq: variationState.msq,
            moq: variationState.moq,
            price: variationState.price,
            discount: variationState.discount ?? 0,
            taxRateId: variationState.taxRateId ?? 0,
            images: variationState.imagesUrls ?? [],
            expiryDays: variationState.expiryDays,
            attributeValuesIds: (variationState.attributes ?? []).map((item) => {
                return item.attributeValueId;
            }),
        };
        console.log(payload);
        validationErrors = FinalGoodsUtils.validateUpdateVariation(payload);
        console.log("validationErrors", validationErrors);
        if (validationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        // isDoingTask = true;
        showActivity("Updating variation...");
        let res = await PresenterProvider.finalGoodsPresenter.updateVariation(payload);
        closeActivity();
        if (res.success) {
            isEditing = false;
            isExpanded = false;
            showSuccessToast("Variation updated successfully");
            onEditSuccess(res.data!);
            // window.location.reload();
        } else {
            showErrorToast(res.message);
        }
        // isDoingTask = false;
    };

    onMount(() => {
        variationState = {
            name: variation.name,
            sku: variation.sku,
            msq: variation.msq,
            moq: variation.moq,
            price: variation.price,
            discount: variation.discount,
            expiryDays: variation.expiryDays,
            imagesUrls: variation.images,
            imagesFiles: [],
            taxRateId: variation.taxRateId,
            attributes: variation.attributes.map((attr) => ({
                attributeValueId: attr.attributeValue.id ?? 1,
            })),
        };
        console.log(variationState);
        isPreparingData = false;
    });
</script>

{#if isPreparingData}
    <div class="flex h-[80vh] w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <div
        id="variation-{variation.id}"
        class="mb-4 rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
        <div
            class="flex items-center justify-between px-6 py-4"
            class:border-b={isExpanded}
            class:border-gray-200={isExpanded}
            class:dark:border-gray-700={isExpanded}
        >
            <div class="flex items-center gap-4">
                <button
                    type="button"
                    class="flex h-8 w-8 items-center justify-center rounded-full transition-colors hover:bg-gray-200 dark:hover:bg-gray-600"
                    on:click={toggleExpand}
                    aria-label={isExpanded
                        ? "Collapse variation details"
                        : "Expand variation details"}
                    title={isExpanded ? "Collapse" : "Expand"}
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 transition-transform duration-200 {isExpanded
                            ? 'rotate-90'
                            : ''}"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 5l7 7-7 7"
                        />
                    </svg>
                </button>
                <h3 class="text-md font-semibold text-gray-700 dark:text-gray-300">
                    Variation #{variation.id}: {variationState.name || "Unnamed"}
                </h3>
            </div>
            <div class="flex items-center gap-2">
                {#if isEditing}
                    <button
                        type="button"
                        class="rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
                        on:click={() => {
                            updateVariation();
                            // if (validationErrors.size === 0) {
                            //     location.reload();
                            // }
                        }}
                    >
                        Update
                    </button>

                    <button
                        type="button"
                        class="rounded-md bg-gray-300 px-3 py-1.5 text-sm font-medium text-gray-800 shadow-sm hover:bg-gray-400"
                        on:click={() => {
                            isEditing = false;
                            isExpanded = false;
                            variationState = {
                                name: variation.name,
                                sku: variation.sku,
                                msq: variation.msq,
                                moq: variation.moq,
                                expiryDays: variation.expiryDays,
                                attributes: variation.attributes.map((attr) => ({
                                    attributeValueId: attr.attributeValue.id,
                                })),
                                price: variation.price,
                                imagesUrls: variation.images,
                                imagesFiles: [],
                                discount: variation.discount,
                                taxRateId: variation.taxRateId,
                            };
                        }}
                    >
                        Cancel
                    </button>
                {:else}
                    <button
                        type="button"
                        class="rounded-md bg-blue-500 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-blue-600"
                        on:click={() => {
                            isEditing = true;
                            isExpanded = true;
                        }}
                    >
                        Edit
                    </button>
                {/if}

                {#if totalVariations > 1}
                    <button
                        type="button"
                        class="rounded-md bg-red-500 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-red-600"
                        on:click={() => onDelete()}
                    >
                        Delete
                    </button>
                {/if}
            </div>
        </div>

        {#if isExpanded}
            <div class="p-6">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <div class="name">
                        <label
                            for="name-{variation.id}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            Name
                        </label>
                        <Input
                            type="text"
                            id="name-{variation.id}"
                            placeholder="Name"
                            bind:value={variationState.name}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("name")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("name")}
                            </p>
                        {/if}
                    </div>

                    <div class="sku">
                        <label
                            for="sku-{variation.id}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            SKU
                        </label>
                        <Input
                            type="text"
                            id="sku-{variation.id}"
                            placeholder="SKU"
                            bind:value={variationState.sku}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("sku")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("sku")}
                            </p>
                        {/if}
                    </div>

                    <div class="msq">
                        <label
                            for="msq-{variation.id}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            MSQ
                        </label>
                        <Input
                            type="number"
                            id="msq-{variation.id}"
                            placeholder="MSQ"
                            bind:value={variationState.msq}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("msq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("msq")}
                            </p>
                        {/if}
                    </div>

                    <div class="moq">
                        <label
                            for="moq-{variation.id}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            MOQ
                        </label>
                        <Input
                            type="number"
                            id="moq-{variation.id}"
                            placeholder="MOQ"
                            bind:value={variationState.moq}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("moq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("moq")}
                            </p>
                        {/if}
                    </div>
                    <div class="price">
                        <label
                            for="moq-{variation.price}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            Price
                        </label>
                        <Input
                            type="number"
                            id="price-{variation.id}"
                            placeholder="price"
                            bind:value={variationState.price}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("price")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("price")}
                            </p>
                        {/if}
                    </div>
                    <div class="discount">
                        <label
                            for="moq-{variation.discount}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            Discount (Optional)
                        </label>
                        <Input
                            type="number"
                            id="discount-{variation.id}"
                            placeholder="Discount"
                            bind:value={variationState.discount}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("discount")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("discount")}
                            </p>
                        {/if}
                    </div>

                    <div class="expire_days">
                        <label
                            for="moq-{variation.expiryDays}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            Expire days
                        </label>
                        <Input
                            type="number"
                            id="expire_days-{variation.id}"
                            placeholder="expire_days"
                            bind:value={variationState.expiryDays}
                            disabled={!isEditing}
                        />
                        {#if validationErrors.has("expire_days")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("expire_days")}
                            </p>
                        {/if}
                    </div>

                    <div class="taxRateId w-full">
                        <label
                            for="moq-{variation.taxRateId}"
                            class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                        >
                            tax
                        </label>
                        <DropdownGen
                            loadItems={loadTax}
                            bind:value={variationState.taxRateId}
                            disabled={!isEditing}
                        />

                        {#if validationErrors.has("tax")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("tax")}
                            </p>
                        {/if}
                    </div>
                </div>

                <div class="mt-6 border-t border-gray-200 pt-6 dark:border-gray-700">
                    <div class="mb-3 flex items-center gap-2">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 text-blue-600"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                            />
                        </svg>
                        <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300">
                            Attributes
                        </h4>
                    </div>
                    <div
                        class="rounded-lg border border-gray-200 bg-white p-3 dark:border-gray-600 dark:bg-gray-800"
                    >
                        <AttributesSelection
                            onSelect={handleAttributeSelection}
                            existingData={variation.attributes.map((attr) => ({
                                attribute: attr.attribute,
                                attributeValue: attr.attributeValue,
                            }))}
                            isDisabled={!isEditing}
                        />
                        {#if validationErrors.has("attributes")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("attributes")}
                            </p>
                        {/if}
                    </div>
                </div>
                <div class="imageUploader">
                    <ImageUploaderDirect
                        bind:files={imageFiles}
                        bind:deletedImages
                        multiple={true}
                        lazy={true}
                        accept="image/*"
                        initialUrls={variation.images ?? []}
                        showDeleteButtons={isEditing}
                    />
                    <!-- onUploaded={handleUploaded}
                          uploadFn={mockUploadFn}
                          onError={handleError} -->
                </div>
            </div>
        {/if}
    </div>
{/if}
