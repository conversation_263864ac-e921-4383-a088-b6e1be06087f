import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import type { 
    IFinalGoodsVariation, 
    IFinalGoodsAddRequest, 
    IFinalGoodsCreatePaylod, 
    IFinalGoodsUpdatePaylod, 
    IFinalGoodsOverview, 
    IFinalGoods, 
    IFinalGoodsVariationNested, 
    TAddVariationRequest, 
    TDeleteVariationRequest, 
    TUpdateVariationRequest 
    } from "../models/IFinalGoods";

export interface IFinalGoodsPresenter {
    getFinalGoodss(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IFinalGoodsOverview>>>;
    getFinalGoodsById(id: number): Promise<DTO<IFinalGoods>>;
    onSubmit(payload: IFinalGoodsAddRequest): Promise<DTO<null>>;
    onUpdateFinalGoods(payload: IFinalGoodsUpdatePaylod): Promise<DTO<null>>;
    onDeleteFinalGoods(ids: number[]): Promise<DTO<null>>;
    onDeleteFinalGoodsById(id: number): Promise<DTO<null>>;
    getItemUnits(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemUnit>>>;
    getItemCategories(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemCategory>>>;
    addVariation(payload: TAddVariationRequest): Promise<DTO<IFinalGoodsVariationNested[] | null>>;
    updateVariation(payload: TUpdateVariationRequest): Promise<DTO<IFinalGoodsVariationNested | null>>;
    deleteVariation(payload: TDeleteVariationRequest): Promise<DTO<null>>;
    getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IFinalGoodsVariation>>>;
}