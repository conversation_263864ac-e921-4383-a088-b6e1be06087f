import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import type {
    IFinalGoodVariationOverview,
    IFinalGoodsPostPayload,
    IFinalGoodPutPayload,
    IFinalGoodOverview,
    IFinalGoodDetailed,
    IFinalGoodVariationNested,
    IFinalGoodVariationPostPayload,
    IFinalGoodVariationDeletePayload,
    IFinalGoodVariationPutPayload
    } from "../models/IFinalGoods";

export interface IFinalGoodsPresenter {
    getFinalGoodss(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IFinalGoodOverview>>>;
    getFinalGoodsById(id: number): Promise<DTO<IFinalGoodDetailed>>;
    onSubmit(payload: IFinalGoodsPostPayload): Promise<DTO<null>>;
    onUpdateFinalGoods(payload: IFinalGoodPutPayload): Promise<DTO<null>>;
    onDeleteFinalGoods(ids: number[]): Promise<DTO<null>>;
    onDeleteFinalGoodsById(id: number): Promise<DTO<null>>;
    getItemUnits(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemUnit>>>;
    getItemCategories(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemCategory>>>;
    addVariation(payload: IFinalGoodVariationPostPayload): Promise<DTO<IFinalGoodVariationNested[] | null>>;
    updateVariation(payload: IFinalGoodVariationPutPayload): Promise<DTO<IFinalGoodVariationNested | null>>;
    deleteVariation(payload: IFinalGoodVariationDeletePayload): Promise<DTO<null>>;
    getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IFinalGoodVariationOverview>>>;
}