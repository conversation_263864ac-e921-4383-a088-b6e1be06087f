import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type {  ValidationErrors } from "$lib/common/utils/types";
import type { IFinalGoodsPresenter, } from "./IFinalGoodsPresenter";
import type { 
    IFinalGoodsVariation, 
    IFinalGoodsUpdatePaylod, 
    IFinalGoodsAddRequest, 
    IFinalGoodsOverview, 
    IFinalGoods, 
    IFinalGoodsVariationNested, 
    TAddVariationRequest, 
    TDeleteVariationRequest, 
    TUpdateVariationRequest 
} from "../models/IFinalGoods";
import { FinalGoodsUtils } from "../utils/FinalGoodsUtils";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class FinalGoodsPresenter implements IFinalGoodsPresenter {

   
    getFinalGoodss(page: number, pageSize: number, text?:string): Promise<DTO<PaginatedBaseResponse<IFinalGoodsOverview>>> {
        return RepoProvider.finalGoodsRepo.getFinalGoodss(page, pageSize,text);
    }

    getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IFinalGoodsVariation>>> {
        return RepoProvider.finalGoodsRepo.getVariations(page, pageSize, text, supplierId);
    }
    getFinalGoodsById(id: number): Promise<DTO<IFinalGoods>> {
        return RepoProvider.finalGoodsRepo.getFinalGoodsById(id);
    }
    onSubmit(payload: IFinalGoodsAddRequest): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.create(payload);
    }
    onUpdateFinalGoods(payload: IFinalGoodsUpdatePaylod): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.updateFinalGoods(payload);
    }
    onDeleteFinalGoods(ids: number[]): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.deleteFinalGoods(ids);
    }
    onDeleteFinalGoodsById(id: number): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.deleteFinalGoodsById(id);
    }
    onValidate(payload: IFinalGoodsVariation): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        console.log(payload);

        const result = FinalGoodsUtils.creationSchema.safeParse(payload);
        console.log(result);

        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }

        return errors;
    }


    getItemUnits(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemUnit>>> {
        return RepoProvider.itemUnitRepo.getAll(page, pageSize);
    }
    getItemCategories(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemCategory>>> {
        return RepoProvider.itemCategoryRepo.getAll(page, pageSize);
    }
    addVariation(payload: TAddVariationRequest[]): Promise<DTO<IFinalGoodsVariationNested[] | null>> {
        return RepoProvider.finalGoodsRepo.addVariation(payload);
    }
    updateVariation(payload: TUpdateVariationRequest): Promise<DTO<IFinalGoodsVariationNested | null>> {
        return RepoProvider.finalGoodsRepo.updateVariation(payload);
    }
    deleteVariation(payload: TDeleteVariationRequest): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.deleteVariation(payload);
    }
    
}