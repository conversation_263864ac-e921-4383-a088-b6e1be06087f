import { 
    type IFinalGoods, 
    type <PERSON><PERSON>GoodsFormState, 
    type <PERSON>inalGoodsVariationAddState, 
    type IFinalGoodsUpdatePaylod,
    type IFinalGoodsAddRequest, 
    FINAL_GOODS_STATUS, 
    type IFinalGoodsVariation, 
    type IFinalGoodsUpdateRequest 
} from "../models/IFinalGoods";
import type { ValidationErrors } from "$lib/common/utils/types";
import { FinalGoodsUtils } from "./FinalGoodsUtils";

export abstract class FinalGoodsEditUtils {


    /**
     * Converts IFinalGoods data to IFinalGoodsFormState for editing
     */
    static convertToFormState(finalGoods: IFinalGoods): 
    // IFinalGoodsFormState
    any
     {
        
        type FormState = {
            finalGoods: {
            name: string;
            categoryIds: any;
            unit: any; // Replace 'any' with the actual unit type if available
            unitId: number;
            description: string;
            expire_days: number;
            hsn: string;
            images: string[];
            };
        };

        const formState: FormState = {
            finalGoods: {
            name: finalGoods.name,
            categoryIds: finalGoods.categories ?? [],
            unit: finalGoods.unit,
            unitId: finalGoods.unit?.id ?? -2,
            description: finalGoods.description ?? "",
            expire_days: 0, // finalGoods.expiryDays - not available in overview
            hsn: finalGoods.hsn,
            images: finalGoods.images ?? []
            }
        };
        console.log(formState)
        return formState;
    }

    /**
     * Prepares update payload from form state for final goods data only
     */
    static prepareFinalGoodsUpdatePayload(
        formState: IFinalGoodsAddRequest,
        finalGoodsId: number,
        status: FINAL_GOODS_STATUS = FINAL_GOODS_STATUS.ACTIVE
        
    ): IFinalGoodsUpdatePaylod {
        console.log(formState,finalGoodsId,status)
        return {
            id: finalGoodsId,
            name: formState.finalGoods.name,
            unitId: formState.finalGoods.unitId ?? -1,
            description:formState.finalGoods.description,
            images:formState.finalGoods.images,
            hsn:formState.finalGoods.hsn,
            status: status,
        };
    }

    /**
     * Validates final goods data for update
     */
    static validateFinalGoodsUpdate(formState: IFinalGoodsUpdateRequest): ValidationErrors {
        // console.log(formState)
        return FinalGoodsUtils.validateUpdate(formState);
    }

    /**
     * Creates an empty form state for new final goods
     */
    static getEmptyFormState(): {
        finalGoods:{
            categoryIds:[],
            unit:null,
            unitId:number,
            description:string,
            name:string,
            hsn:string,
            expire_days:number,
            images:string[]|[]
        }
    } {
        return {
            finalGoods: {
                categoryIds: [],
                unit: null,
                unitId:0,
                description:"",
                name: "",
                hsn: "",
                // gstPercentage: 0,
                expire_days:0,
                images:[]
            },
            // variations: [],
        };
    }

    /**
     * Adds a new variation to the form state
     */
    static addNewVariation(formState: IFinalGoodsFormState): IFinalGoodsFormState {
        const newVariation = FinalGoodsUtils.getEmptyVariation();
        return {
            ...formState,
            variations: [...formState.variations, newVariation]
        };
    }

    /**
     * Removes a variation from the form state
     */
    static removeVariation(formState: IFinalGoodsFormState, index: number): IFinalGoodsFormState {
        const newVariations = [...formState.variations];
        newVariations.splice(index, 1);
        return {
            ...formState,
            variations: newVariations
        };
    }
}
