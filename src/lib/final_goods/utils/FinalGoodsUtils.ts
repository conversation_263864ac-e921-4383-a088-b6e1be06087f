import { z } from "zod";
import {
    // FINAL_GOODS_STATUS,
    // type ICreateVariation,
    type IFinalGoodOverview,
    type IFinalGoodsPostPayload,
    // type IFinalGoodsCreatePaylod,
    type IFinalGoodAddFormState,
    type IFinalGoodsVariationAddFormState,
    // type IFinalGoods,
    type IFinalGoodPutPayload,
    type IFinalGoodVariationPutPayload,
 } from "../models/IFinalGoods";
import { CSVProvider } from "$lib/csv-provider/repositories/CSVProvider";
import { showErrorToast } from "$lib/common/utils/common-utils";
import { PresenterProvider } from "$lib/PresenterProvider";
import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import { ZodSchema } from 'zod';

export abstract class FinalGoodsUtils {
  
  

    static emptyFormState: () => IFinalGoodAddFormState = () => ({
        finalGoods: {
            categories: [],
            unit: null,
            name: "",
            description: "",
            imagesUrls: [],
            imagesFiles: [],
            hsn: "",
        },
        variations: [],
    });
    

    static getEmptyVariation(): IFinalGoodsVariationAddFormState {
        return {
            name: "",
            sku: "",
            moq: 0,
            msq: 0,
            price: 0,
            discount: 0,
            taxRateId: 1,
            expiryDays: 0,
            imagesUrls: [],
            imagesFiles: [],
            attributes: [],
        };
    }

    private static variationSchema = z.object({
      finalGoodsId: z.any(), // mandatory
      name: z.string().min(3).max(100),
      sku: z.string().min(3).max(100),
      moq: z.number().nonnegative(),
      msq: z.number().nonnegative(),
      price: z.number().refine(val => val !== 0, { message: "Price must be non-zero" }),
      images: z.array(z.string()).optional(), // mandatory
      discount: z.number().optional().default(0),
      expire_days: z.number()
            .refine((val) => val !== undefined && val !== null || val < 0, { message: "Expire days is a mandatory field" })
            .refine((val) => val >= 0, { message: "Expire days must be 0 or a positive number" }),
      taxRateId: z.union([z.number(), z.string(), z.null()]).refine(val => val !== 0 && val !== "0" && val !== null, { message: "Tax Rate ID must be non-zero" }),
      attributes: z
        .array(
          z.object({
            attributeValue: z.any(), // replace with actual `IItemAttributeValue` schema if available
          })
        ),
    });

    static creationSchema = z.object({  
        categoryIds: z.array(z.number().min(1, 'At least one category is required'))
            .min(1, { message: "Category is a mandatory field" }),
        unitId: z.number().int()
            .refine((val) => val !== -1, { message: "Unit is a mandatory field" }),
        name: z.string()
            .min(1, { message: "Name is a mandatory field" })
            .min(3, { message: "Name should be at least 3 characters" })
            .max(100, { message: "Name should not exceed 100 characters" }),
        description: z.string()
            .min(1, { message: "Description is a mandatory field" })
            .min(15, { message: "Description should be at least 15 characters" }),
        images: z.array(z.string()).optional(),
            // .min(1, { message: "Please upload at least one image" }),
                hsn: z.string()
            .min(3, { message: "HSN should be at least 3 characters" }),
    });

    static updateSchema = this.creationSchema.extend({
        id: z.number().int().positive("ID is required"),
        status: z.enum(["active", "inactive"]),
        
    });


static validateWithSchema = <T>(schema: ZodSchema<T>, payload: unknown): ValidationErrors => {
    const errors: ValidationErrors = new Map();
    const result = schema.safeParse(payload);

    if (!result.success) {
        result.error.issues.forEach((issue) => {
            errors.set(issue.path[0]?.toString() ?? 'unknown', issue.message);
        });
    }

    return errors;
};
static validateCreate = (payload: IFinalGoodsPostPayload) =>
    FinalGoodsUtils.validateWithSchema(FinalGoodsUtils.creationSchema, payload.finalGoods);

static validateUpdate = (payload: IFinalGoodPutPayload) =>
    {console.log(payload)
        return FinalGoodsUtils.validateWithSchema(FinalGoodsUtils.updateSchema, payload);
    }


    static validateVariations = (payload: any[]): IndexedValidationErrors => {
        const errors: IndexedValidationErrors = new Map();

        let result;
        let item;
        for (let i = 0; i < payload.length; i++) {
            item = payload[i];

            result = this.variationSchema.safeParse(item);

            if (!result.success) {
                result.error.issues.forEach((issue) => {
                    console.log(issue);
                    
                    const fieldName = issue.path.join('.');
                    if (!errors.has(i)) {
                        errors.set(i, [{ fieldName, errorMessage: issue.message }]);
                    } else {
                        errors.get(i)!.push({ fieldName, errorMessage: issue.message });
                    }
                });

                if (item?.attributeValuesIds?.length === 0) {
                    if (!errors.has(i)) {
                        errors.set(i, [{ fieldName: "attributes", errorMessage: "At least one attribute is required" }]);
                    } else {
                        errors.get(i)!.push({ fieldName: "attributes", errorMessage: "At least one attribute is required" });
                    }
                }
                break;

            }
        }
        return errors;
    }


    static validateUpdateVariation = (payload: TUpdateVariationRequest): ValidationErrors => {
        const errors: ValidationErrors = new Map();
        const result = this.variationSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                console.log(issue);
                // errors.set(issue.path[0].toString(), issue.message);
                errors.set(issue.path.join("."), issue.message);
            });
        }
        return errors;
    }
}

let keyMap: { [key: string]: string } = {
    // 'ID': 'id',
    'Name': 'name',
    'Unit Name': 'unit.name',
    'Description':'description',
    // 'Expire days':'expire_days',
    'Status':'status'
}

export const downloadCsv = async (data: IFinalGoodsOverview[], fileName: string) => {
    if (data.length === 0) {
        showErrorToast("Please choose data")
        return;
    }
    console.log(data)
    await new CSVProvider().save(data, fileName, keyMap);
}

export const downloadAllCsv = async (startDate: Date, endDate: Date) => {
    // const response = await PresenterProvider.rawMaterialPresenter.getRawMaterials(1, -1, undefined)
    const response = await PresenterProvider.finalGoodsPresenter.getFinalGoodss(1,-1, undefined)

    if (response.success && response.data.data && response.data.data.length > 0) {
        await new CSVProvider().save(response.data.data, "final-goods", keyMap);
    } else {
        showErrorToast("No data found")
    }
}