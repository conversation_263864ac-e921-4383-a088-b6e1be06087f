import { IFactoryGateRepo } from "../features/factory_gates/repositories/IFactoryGateRepo";
import { PostgresFactoryGateRepo } from "../features/factory_gates/repositories/PostgresFactoryGateRepo";
import { IItemAttributeValueRepo } from "../features/item_attribute_value/repositories/IItemAttributeValueRepo";
import { PostgresItemAttributeValueRepo } from "../features/item_attribute_value/repositories/PostgresItemAttributeValueRepo";
import { IItemAttributeRepo } from "../features/item_attribute/repositories/IItemAttributeRepo";
import { PostgresItemAttributeRepo } from "../features/item_attribute/repositories/PostgresItemAttributeRepo";
import { IItemCategoryRepo } from "../features/item_category/repositories/IItemCategoryRepo";
import { PostgresItemCategoryRepo } from "../features/item_category/repositories/PostgresItemCategoryRepo";
import { IItemUnitRepo } from "../features/item_unit/repositories/IItemUnitRepo";
import { PostgresItemUnitRepo } from "../features/item_unit/repositories/PostgresItemUnitRepo";
import { IRawMaterialRepo } from "../features/raw_material/repositories/IRawMaterialRepo";
import { PostgresRawMaterialRepo } from "../features/raw_material/repositories/PostgresRawMaterialRepo";
import { IStorageLocationRepo } from "../features/storage_locations/repositories/IStorageLocationRepo";
import { PostgresStorageLocationRepo } from "../features/storage_locations/repositories/PostgresStorageLocationRepo";
import { ISupplierRepo } from "../features/supplier/repositories/ISupplierRepo";
import { PostgresSupplierRepo } from "../features/supplier/repositories/PostgresSupplierRepo";
import { IRawMaterialStockRepo } from "../features/raw_material_stock/repositories/IRawMaterialStockRepo";
import { PostgresRawMaterialStockRepo } from "../features/raw_material_stock/repositories/PostgresRawMaterialStockRepo";
import { IAddressRepo } from "../features/address/repositories/IAddressRepo";
import { PostgresAddressRepo } from "../features/address/repositories/PostgresAddressRepo";
import { IPurchaseInvoiceRepo } from "../features/purchase_invoice/repositories/IPurchaseInvoiceRepo";
import { PostgresPurchaseInvoiceRepo } from "../features/purchase_invoice/repositories/PostgresPurchaseInvoiceRepo";
import { IDebitNoteRepo } from "../features/debit_note/repositories/IDebitNoteRepo";
import { PostgresDebitNoteRepo } from "../features/debit_note/repositories/PostgresDebitNoteRepo";
import { ILogRepo } from "../features/logs/repositories/ILogsRepo";
import { PostgresLogsRepo } from "../features/logs/repositories/PostgresLogsRepo";
import { IUserRoleRepo } from "../features/users/sub_feaures/user_roles/repositories/IUserRoleRepo";
import { PostgresUserRoleRepo } from "../features/users/sub_feaures/user_roles/repositories/PostgresUserRoleRepo";
import { INormalUserRepo } from "../features/users/sub_feaures/normal_user/repositories/INormalUserRepo";
import { PostgresNormalUserRepo } from "../features/users/sub_feaures/normal_user/repositories/PostgresNormalUserRepo";
import { ICloudImageUploadRepo } from './../features/cloud_image_upload/repo/ICloudImageUploadRepo';
import { CloudImageUploadRepo } from './../features/cloud_image_upload/repo/CloudImageUploadRepo';

import { IOpeningStockRepo } from '../features/opening_stock/repositories/IOpeningStockRepo'
import { PostgresOpeningStockRepo } from '../features/opening_stock/repositories/PostgresOpeningStockRepo'
import { ICoreUserRepo } from "../features/users/core/repositories/IUserRepo";
import { PostgresCoreUserRepo } from "../features/users/core/repositories/PostgresCoreUserRepo";
import { IRedisServerRepository } from "../features/redis/repositories/IRedisServerRepository";
import { RedisServerRepository } from "../features/redis/repositories/RedisServerRepository";
import { IPurchaseOrderRepo } from "../features/purchase_order/repositories/IPurchaseOrderRepo";
import { PostgresPurchaseOrderRepo } from "../features/purchase_order/repositories/PostgresPurchaseOrderRepo";
import { IFinalGoodsRepo } from "../features/final_goods/repos/IFinalGoodsRepo";
import { PostgresFinalGoodsRepo } from "../features/final_goods/repos/FinalGoodsRepo";
import { IBillOfMaterialRepo } from "../features/bill_of_materials/repositories/IBillOfMaterialRepo";
import { BillOfMaterialRepo } from "../features/bill_of_materials/repositories/BillOfMaterialRepo";
import { IDepartmentRepo } from "../features/department/repositories/IDepartmentRepo";
import { PostgresDepartmentRepo } from "../features/department/repositories/PostgresDepartmentRepo";
import { ITaxRateRepo } from "../features/tax-rate/repos/ITaxRateRepo";
import TaxRateRepo from "../features/tax-rate/repos/TaxRateRepo";
import { BrandRepo } from "../features/brand/repos/BrandRepo";
import { IBrandRepo } from "../features/brand/repos/IBrandRepo";
import { IThirdPartySystemUserRepo } from "../features/third_party_system_users/repositories/IThirdPartySystemUserRepo";
import { PostgresThirdPartySystemUserRepo } from "../features/third_party_system_users/repositories/PostgresThirdPartySystemUserRepo";
import { ISystemUserRepo } from "../features/system_users/repositories/SystemUserRepo";
import { PostgresSystemUserRepo } from "../features/system_users/repositories/PostgresSystemUserRepo";


export class RepoProvider {
  private static _supplierRepo: ISupplierRepo;
  private static _storageLocationRepo: IStorageLocationRepo;
  private static _factoryGateRepo: IFactoryGateRepo;
  private static _rawMaterialRepo: IRawMaterialRepo;
  private static _itemCategoryRepo: IItemCategoryRepo;
  private static _itemAttributeRepo: IItemAttributeRepo;
  private static _itemAttributeValueRepo: IItemAttributeValueRepo;
  private static _itemUnitRepo: IItemUnitRepo;
  private static _rawMaterialStockRepo: IRawMaterialStockRepo;
  private static _addressRepo: IAddressRepo;
  private static _purchaseInvoiceRepo: IPurchaseInvoiceRepo;
  private static _debitNoteRepo: IDebitNoteRepo;
  private static _logRepo: ILogRepo;
  private static _userRoleRepo: IUserRoleRepo;
  private static _coreUserRepo: ICoreUserRepo;
  private static _normalUserRepo: INormalUserRepo;
  private static _openingStockRepo: IOpeningStockRepo;
  private static _redisServerRepository: IRedisServerRepository;
  private static _purchaseOrderRepo: IPurchaseOrderRepo;
  private static _finalGoodsRepo: IFinalGoodsRepo;
  private static _bomRepo: IBillOfMaterialRepo;
  private static _departmentRepo: IDepartmentRepo;
  private static _taxRateRepo: ITaxRateRepo;
  private static _brandRepo: IBrandRepo;
  private static _cloudImageUploadRepo: ICloudImageUploadRepo;
  private static _thirdPartySystemUserRepo: IThirdPartySystemUserRepo;
  private static _systemUserRepo: ISystemUserRepo;


  static get supplierRepo() {
    if (!this._supplierRepo) {
      this._supplierRepo = new PostgresSupplierRepo();
    }
    return this._supplierRepo;
  }


  static get storageLocationRepo() {
    if (!this._storageLocationRepo) {
      this._storageLocationRepo = new PostgresStorageLocationRepo()
    }
    return this._storageLocationRepo
  }

  static get factoryGateRepo() {
    if (!this._factoryGateRepo) {
      this._factoryGateRepo = new PostgresFactoryGateRepo()
    }
    return this._factoryGateRepo
  }


  static get rawMaterialRepo() {
    if (!this._rawMaterialRepo) {
      this._rawMaterialRepo = new PostgresRawMaterialRepo()
    }
    return this._rawMaterialRepo
  }

  static get itemCategoryRepo() {
    if (!this._itemCategoryRepo) {
      this._itemCategoryRepo = new PostgresItemCategoryRepo()
    }
    return this._itemCategoryRepo
  }

  static get itemAttributeRepo() {
    if (!this._itemAttributeRepo) {
      this._itemAttributeRepo = new PostgresItemAttributeRepo()
    }
    return this._itemAttributeRepo
  }

  static get itemAttributeValueRepo() {
    if (!this._itemAttributeValueRepo) {
      this._itemAttributeValueRepo = new PostgresItemAttributeValueRepo()
    }
    return this._itemAttributeValueRepo
  }

  static get itemUnitRepo() {
    if (!this._itemUnitRepo) {
      this._itemUnitRepo = new PostgresItemUnitRepo()
    }
    return this._itemUnitRepo
  }

  static get rawMaterialStockRepo() {
    if (!this._rawMaterialStockRepo) {
      this._rawMaterialStockRepo = new PostgresRawMaterialStockRepo()
    }
    return this._rawMaterialStockRepo
  }

  static get addressRepo() {
    if (!this._addressRepo) {
      this._addressRepo = new PostgresAddressRepo()
    }
    return this._addressRepo
  }

  static get purchaseInvoiceRepo() {
    if (!this._purchaseInvoiceRepo) {
      this._purchaseInvoiceRepo = new PostgresPurchaseInvoiceRepo()
    }
    return this._purchaseInvoiceRepo
  }

  static get debitNoteRepo() {
    if (!this._debitNoteRepo) {
      this._debitNoteRepo = new PostgresDebitNoteRepo()
    }
    return this._debitNoteRepo
  }

  static get logRepo() {
    if (!this._logRepo) {
      this._logRepo = new PostgresLogsRepo();
    }
    return this._logRepo;
  }

  static get userRoleRepo() {
    if (!this._userRoleRepo) {
      this._userRoleRepo = new PostgresUserRoleRepo();
    }
    return this._userRoleRepo;
  }

  static get coreUserRepo() {
    if (!this._coreUserRepo) {
      this._coreUserRepo = new PostgresCoreUserRepo();
    }
    return this._coreUserRepo;
  }

  static get normalUserRepo() {
    if (!this._normalUserRepo) {
      this._normalUserRepo = new PostgresNormalUserRepo();
    }
    return this._normalUserRepo;
  }

  static get openingStockRepo() {
    if (!this._openingStockRepo) {
      this._openingStockRepo = new PostgresOpeningStockRepo()
    }
    return this._openingStockRepo
  }

  static get redisServerRepository() {
    if (!this._redisServerRepository) {
      this._redisServerRepository = new RedisServerRepository()
    }
    return this._redisServerRepository
  }

  static get purchaseOrderRepo() {
    if (!this._purchaseOrderRepo) {
      this._purchaseOrderRepo = new PostgresPurchaseOrderRepo()
    }
    return this._purchaseOrderRepo
  }

  static get finalGoodsRepo() {
    if (!this._finalGoodsRepo) {
      this._finalGoodsRepo = new PostgresFinalGoodsRepo()
    }
    return this._finalGoodsRepo
  }

  static get bomRepo() {
    if (!this._bomRepo) {
      this._bomRepo = new BillOfMaterialRepo();
    }
    return this._bomRepo;
  }

  static get departmentRepo() {
    if (!this._departmentRepo) {
      this._departmentRepo = new PostgresDepartmentRepo()
    }
    return this._departmentRepo
  }

  static get taxRateRepo() {
    if (!this._taxRateRepo) {
      this._taxRateRepo = new TaxRateRepo()
    }
    return this._taxRateRepo
  }

  static get cloudImageUploadRepo() {
    if (!this._cloudImageUploadRepo) {
      this._cloudImageUploadRepo = new CloudImageUploadRepo()
    }
    return this._cloudImageUploadRepo
  }
  static get brandRepo() {
    if (!this._brandRepo) {
      this._brandRepo = new BrandRepo();
    }
    return this._brandRepo;
  }
  static get thirdPartySystemUserRepo() {
    if (!this._thirdPartySystemUserRepo) {
      this._thirdPartySystemUserRepo = new PostgresThirdPartySystemUserRepo();
    }
    return this._thirdPartySystemUserRepo;
  }
  static get systemUserRepo() {
    if (!this._systemUserRepo) {
      this._systemUserRepo = new PostgresSystemUserRepo();
    }
    return this._systemUserRepo;
  }

}