<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import BillOfMaterialEditForm from "$lib/bill_of_materials/components/BillOfMaterialEditForm.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";
    import { page } from "$app/stores";
    import type { IBillOfMaterial } from "$lib/bill_of_materials/models/IBillOfMaterial";

    let isLoading: boolean = true;
    let billOfMaterialData: IBillOfMaterial | null = null;

    onMount(async () => {
        const id = $page.url.searchParams.get("id");
        if (!id) {
            showErrorToast("Bill of Material ID is required");
            await goto("/admin/bill-of-materials");
            return;
        }

        const bomId = parseInt(id);
        if (isNaN(bomId)) {
            showErrorToast("Invalid Bill of Material ID");
            await goto("/admin/bill-of-materials");
            return;
        }

        try {
            const res = await PresenterProvider.billOfMaterialPresenter.getBillOfMaterialById(bomId);
            if (res.success) {
                billOfMaterialData = res.data;
            } else {
                showErrorToast(res.message);
                await goto("/admin/bill-of-materials");
                return;
            }
        } catch (error) {
            showErrorToast("Failed to load bill of material data");
            await goto("/admin/bill-of-materials");
            return;
        }

        isLoading = false;
    });
</script>

<svelte:head><title>Edit Bill of Material</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL.UPDATE)}
    <NoPermissionView />
{:else if billOfMaterialData}
    <BillOfMaterialEditForm billOfMaterial={billOfMaterialData} />
{:else}
    <div class="flex items-center justify-center h-64">
        <p class="text-gray-500">Bill of Material not found</p>
    </div>
{/if}
