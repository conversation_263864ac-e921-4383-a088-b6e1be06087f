<script lang="ts">
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import BillOfMaterialTable from "$lib/bill_of_materials/components/BillOfMaterialTable.svelte";
    import type { IBillOfMaterialOverview } from "$lib/bill_of_materials/models/IBillOfMaterial";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";

    let isLoading: boolean = true;
    let paginationData: PaginatedDataWrapper<IBillOfMaterialOverview> = {
        pagination: {
            data: [],
            currentPage: 1,
            totalPages: 1,
            totalData: 0,
        },
        pageSize: 10,
        searchText: undefined,
        onPageChange: (page: number) => {
            loadData(page);
        },
    };
    let selectedRowsMap: Map<number, IBillOfMaterialOverview> = new Map();

    const loadData = async (page: number = 1) => {
        isLoading = true;
        const res = await PresenterProvider.billOfMaterialPresenter.getBillOfMaterials(
            page,
            paginationData.pageSize,
            paginationData.searchText
        );
        if (res.success) {
            paginationData.pagination = res.data;
            selectedRowsMap.clear();
            selectedRowsMap = selectedRowsMap;
            paginationData = paginationData;
            
        } else {
            showErrorToast(res.message);
        }
        isLoading = false;
    };

    onMount(() => {
        loadData();
    });
</script>

<svelte:head><title>Bill of Materials</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL.READ)}
    <NoPermissionView />
{:else}
    <BillOfMaterialTable
        {paginationData}
        bind:selectedRowsMap
        onSearchClear={() => {
            loadData(1);
        }}
    />
{/if}
