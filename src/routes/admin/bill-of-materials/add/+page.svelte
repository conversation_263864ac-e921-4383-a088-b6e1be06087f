<script lang="ts">
    import BillOfMaterialForm from "$lib/bill_of_materials/components/BillOfMaterialForm.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
</script>

<svelte:head><title>Add Bill of Material</title></svelte:head>

{#if !$userPermissions.includes(AppPermissions.RAW_MATERIAL.CREATE)}
    <NoPermissionView />
{:else}
    <BillOfMaterialForm />
{/if}
