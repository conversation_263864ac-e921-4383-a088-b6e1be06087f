<script lang="ts">
    import Loader from "$lib/common/components/Loader.svelte";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { showErrorToast, sortByField } from "$lib/common/utils/common-utils";
    import OperationalLogsTable from "$lib/operational-logs/components/OperationalLogsTable.svelte";
    import type { OperationalLogs } from "$lib/operational-logs/models/OperationalLogs";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { onMount } from "svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";

    let loading = true;
    let paginationData: PaginatedDataWrapper<OperationalLogs> = {
        pageSize: 10,
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {},
    };
    const loadData = async (page: number) => {
        loading = true;
        const response = await PresenterProvider.operationalLogsPresenter.getAll(
            page,
            paginationData.pageSize
        );
        if (!response.success) {
            return showErrorToast(response.message);
        }
        paginationData.pagination = response.data;
        paginationData.onPageChange = loadData;
        paginationData = paginationData;
        loading = false;
    };

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.LOG.READ)) {
            loading = false;
            return;
        }
        loadData(1);
    });
</script>
<svelte:head><title>Logs</title></svelte:head>

{#if loading}
    <div class="flex h-full w-full items-center justify-center">
        <Loader />
    </div>
{:else if !$userPermissions.includes(AppPermissions.LOG.READ)}
    <NoPermissionView />
{:else}
    <OperationalLogsTable {paginationData} />
{/if}
