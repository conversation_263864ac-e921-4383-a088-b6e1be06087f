<script lang="ts">
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import SupplierTable from "$lib/supplier/components/SupplierTable.svelte";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";
    

    let isLoading = true;

    let paginationData: PaginatedDataWrapper<ISupplier> = {
        pageSize: 10,
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {},
    };

    const loadData = async (page: number) => {
        isLoading = true;
        const response = await PresenterProvider.supplierPresenter.getAll(
            page,
            paginationData.pageSize
        );
        if (!response.success) {
            return showErrorToast(response.message);
        }
        paginationData.pagination = response.data;
        paginationData.onPageChange = loadData;
        paginationData = paginationData;
        isLoading = false;
    };

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.SUPPLIER.READ)) {
            isLoading = false;
            return;
        }
        loadData(1);
    });
</script>
<svelte:head><title>Suppliers</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.SUPPLIER.READ)}
    <NoPermissionView />
{:else}
    <SupplierTable {paginationData} />
{/if}
