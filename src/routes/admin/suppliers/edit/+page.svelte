<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import SupplierForm from "$lib/supplier/components/SupplierForm.svelte";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import { SupplierUtils } from "$lib/supplier/utils/SupplierUtils";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";

    let isLoading: boolean = true;
    let data: ISupplier = SupplierUtils.getEmpty();

    onMount(async () => {
        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        if (!id || isNaN(Number(id))) {
            return goto("/admin/suppliers");
        }

        const response = await PresenterProvider.supplierPresenter.getById(Number(id!));
        if (!response.success) {
            return showErrorToast(response.message);
        }
        data = response.data;
        isLoading = false;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.SUPPLIER.UPDATE)}
    <NoPermissionView />
{:else}
    <SupplierForm supplier={data} />
{/if}
