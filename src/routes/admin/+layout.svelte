<script lang="ts">
    import SuccessToast from "$lib/toasts/ui_components/SuccessToast.svelte";
    import ErrorToast from "$lib/toasts/ui_components/ErrorToast.svelte";
    import { sidebarOpen } from "$lib/common/utils/store";

    import Navigation from "$lib/common/components/admin/Navigation.svelte";
    import Sidebar from "$lib/common/components/admin/Sidebar.svelte";
    import { Spinner } from "flowbite-svelte";
    import CustomConfirmationComponent from "$lib/common/components/CustomConfirmationComponent.svelte";
    import { confirmDialog } from "$lib/common/utils/custom_confirmation_component_store";
    import ActivityComponent from "$lib/common/components/ActivityComponent.svelte";
    import { activityDialog } from "$lib/common/utils/activity_store";

    let drawerHidden: boolean = false;
    let width: number;
    let isLoading: boolean = false;
    let showConfirmDialog: boolean = false;
    let confirmDialogHeading: string = "";
    let confirmDialogBody: string = "";
    let confirmDialogPositiveButtonText: string = "";
    let confirmDialogNegativeButtonText: string = "";
    let confirmDialogPositiveAction: (() => void) | undefined = undefined;
    let confirmDialogNegativeAction: (() => void) | undefined = undefined;
    let showActivityComponent: boolean = false;
    let activityComponentMessage: string = "";

    const toggleDrawer = () => {
        drawerHidden = !drawerHidden;
        sidebarOpen.update(() => drawerHidden);
    };
    $: {
        const dialogState = $confirmDialog;
        showConfirmDialog = dialogState.show || false;
        confirmDialogHeading = dialogState.heading || "Confirmation";
        confirmDialogBody = dialogState.body || "Are you sure you want to proceed?";
        confirmDialogPositiveButtonText = dialogState.positiveButtonText || "Yes";
        confirmDialogNegativeButtonText = dialogState.negativeButtonText || "No";
        confirmDialogPositiveAction = dialogState.positiveAction;
        confirmDialogNegativeAction = dialogState.negativeAction;
    }

    $: {
        const activityState = $activityDialog;
        showActivityComponent = activityState.show || false;
        activityComponentMessage = activityState.message || "Please wait...";
    }
</script>

<svelte:window bind:innerWidth={width} />

{#if isLoading}
    <div class="flex h-[100vh] w-[100vw] items-center justify-center">
        <Spinner size={"8"} color="gray" />
    </div>
{:else}
    <Navigation {toggleDrawer} />
    <div
        class="{drawerHidden
            ? ``
            : `lg:ml-[16rem]`} relative mt-[60px] flex min-h-[calc(100vh-60px)] bg-primary-50 duration-500 ease-in-out dark:bg-primary-800"
    >
        <main class=" mx-auto w-full overflow-y-auto px-[4%] py-[4%]">
            <slot />
        </main>
    </div>
    <ErrorToast />
    <SuccessToast />
    <Sidebar bind:drawerHidden {width} />
    <CustomConfirmationComponent
        bind:show={showConfirmDialog}
        heading={confirmDialogHeading}
        body={confirmDialogBody}
        positiveButtonText={confirmDialogPositiveButtonText}
        negativeButtonText={confirmDialogNegativeButtonText}
        positiveAction={confirmDialogPositiveAction}
        negativeAction={confirmDialogNegativeAction}
    />
    <ActivityComponent show={showActivityComponent} message={activityComponentMessage} />
{/if}
