<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import FactoryGateForm from "$lib/factory_gates/components/FactoryGateForm.svelte";
    import type { IFactoryGate } from "$lib/factory_gates/models/IFactoryGate";
    import { FactoryGateUtils } from "$lib/factory_gates/utils/FactoryGateUtils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";

    let isLoading: boolean = true;
    let data: IFactoryGate = FactoryGateUtils.getEmpty();

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.FACTORY_GATE.UPDATE)) {
            isLoading = false;
            return;
        }
        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        if (!id || isNaN(Number(id))) {
            return goto("/admin/fatory-gates");
        }

        const response = await PresenterProvider.factoryGatesPresenter.getById(Number(id!));
        if (!response.success) {
            return showErrorToast(response.message);
        }
        data = response.data;
        isLoading = false;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.FACTORY_GATE.UPDATE)}
    <NoPermissionView />
{:else}
    <FactoryGateForm obj={data} />
{/if}
