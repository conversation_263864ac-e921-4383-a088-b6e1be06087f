<script lang="ts">
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import FactoryGateForm from "$lib/factory_gates/components/FactoryGateForm.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
</script>

{#if !$userPermissions.includes(AppPermissions.FACTORY_GATE.CREATE)}
    <NoPermissionView />
{:else}
    <FactoryGateForm />
{/if}
