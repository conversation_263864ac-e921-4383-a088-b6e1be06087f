<script lang="ts">
    import FinalGoodsForm from "$lib/final_goods/components/FinalGoodsForm.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
</script>

{#if !$userPermissions.includes(AppPermissions.RAW_MATERIAL.CREATE)}
    <NoPermissionView />
{:else}
    <FinalGoodsForm />
{/if}
