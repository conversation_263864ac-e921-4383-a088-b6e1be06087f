<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import FinalGoodsEditForm from "$lib/final_goods/components/FinalGoodsEditForm.svelte";
    import type { IFinalGoods } from "$lib/final_goods/models/IFinalGoods";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";

    let isLoading: boolean = true;
    let finalGoodsData: IFinalGoods | null = null;

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.RAW_MATERIAL.UPDATE)) {
            isLoading = false;
            return;
        }

        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        if (!id || isNaN(Number(id))) {
            return goto("/admin/final-goods");
        }
        const response = await PresenterProvider.finalGoodsPresenter.getFinalGoodsById(Number(id!));
        if (!response.success) {
            return showErrorToast(response.message);
        }
        finalGoodsData = response.data;
        isLoading = false;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL.UPDATE)}
    <NoPermissionView />
{:else if finalGoodsData}
    <FinalGoodsEditForm finalGoods={finalGoodsData} />
{:else}
    <div class="flex items-center justify-center h-64">
        <p class="text-gray-500 dark:text-gray-400">final goods not found.</p>
    </div>
{/if}
