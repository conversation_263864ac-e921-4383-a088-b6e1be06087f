<script lang="ts">
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import PurchaseInvoiceTable from "$lib/purchase_invoice/components/PurchaseInvoiceTable.svelte";
    import type { IPurchaseInvoice } from "$lib/purchase_invoice/models/IPurchaseInvoice";
    import { onMount } from "svelte";

    let isLoading = true;
    let paginationData: PaginatedDataWrapper<IPurchaseInvoice> = {
        pageSize: 10,
        searchText: "",
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {},
    };

    let filter: {
        startDate: string;
        endDate: string;
        currentFilter: "today" | "week" | "month";
    } | null = null;

    const loadData = async (page: number) => {
        isLoading = true;
        let response;

        if (!filter && !paginationData.searchText) {
            response = await PresenterProvider.purchaseInvoicePresenter.getAll(
                page,
                paginationData.pageSize
            );
        } else if (paginationData.searchText && paginationData.searchText.trim().length > 2) {
            response = await PresenterProvider.purchaseInvoicePresenter.searchByAny(
                paginationData.searchText!,
                page,
                paginationData.pageSize
            );
        } else {
            response = await PresenterProvider.purchaseInvoicePresenter.getByDate(
                filter!.startDate,
                filter!.endDate,
                page,
                paginationData.pageSize
            );
        }

        if (!response.success) {
            return showErrorToast(response.message);
        }
        paginationData.pagination = response.data;
        paginationData.onPageChange = loadData;
        paginationData = paginationData;
        isLoading = false;
    };
    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.RAW_MATERIAL_STOCK.READ_PURCHASE)) {
            isLoading = false;
            return;
        }
        loadData(1);
    });
</script>

<svelte:head><title>Purchase Invoices</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL_STOCK.READ_PURCHASE)}
    <NoPermissionView />
{:else}
    <PurchaseInvoiceTable
        onSearchClear={() => {
            loadData(1);
        }}
        currentFilter={filter?.currentFilter}
        onDateFilterChange={(dataFilter) => {
            filter = dataFilter;
            loadData(1);
        }}
        {paginationData}
    />
{/if}
