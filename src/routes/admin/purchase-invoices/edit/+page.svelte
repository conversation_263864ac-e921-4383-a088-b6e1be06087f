<script lang="ts">
    import { userPermissions } from "$lib/common/utils/store";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import PurchaseInvoice from "$lib/purchase_invoice/components/PurchaseInvoice.svelte";
    import type { IPurchaseInvoiceDetailed } from "$lib/purchase_invoice/models/IPurchaseInvoice";
    import { onMount } from "svelte";

    let isLoading: boolean = true;
    let data: IPurchaseInvoiceDetailed;

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.RAW_MATERIAL_STOCK.UPDATE_PURCHASE)) {
            isLoading = false;
            return;
        }

        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        if (!id || isNaN(Number(id))) {
            return goto("/admin");
        }

        const response = await PresenterProvider.purchaseInvoicePresenter.getById(Number(id!));
        if (!response.success) {
            return showErrorToast(response.message);
        }
        data = response.data;
        isLoading = false;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL_STOCK.UPDATE_PURCHASE)}
    <NoPermissionView />
{:else}
    <PurchaseInvoice obj={data} />
{/if}
