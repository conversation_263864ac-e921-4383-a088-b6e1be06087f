<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import DebitNoteTable from "$lib/debit-notes/components/DebitNoteTable.svelte";
    import type { IDebitNote } from "$lib/debit-notes/models/IDebitNote";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { onMount } from "svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";

    let isLoading = true;
    let isDoingTask = false;
    let data: IDebitNote[] = [];

    let note: string = "";

    const loadData = async () => {
        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        if (!id || isNaN(Number(id))) {
            return goto("/admin/suppliers");
        }

        const response = await PresenterProvider.debitNotePresenter.getByPurchaseInvoiceId(
            Number(id)
        );

        if (!response.success) {
            return showErrorToast(response.message);
        }

        data = response.data;

        isLoading = false;
    };

    const markPaid = async () => {
        isDoingTask = true;
        note = note.trim();
        const result = await PresenterProvider.debitNotePresenter.markPaid({
            purchaseInvoiceId: data[0].purchaseInvoiceId,
            note: note.length > 0 ? note : null,
        });
        if (!result.success) {
            showErrorToast(result.message);
            isDoingTask = false;
        } else {
            showSuccessToast("Mark Paid");
            goto("/admin/debit-notes");
        }
    };

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.DEBIT_NOTE.READ)) {
            isLoading = false;
            return;
        }
        loadData();
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.DEBIT_NOTE.READ)}
    <NoPermissionView />
{:else}
    <DebitNoteTable totalRows={data} />

    <div class="mt-5"></div>

    <!-- notes -->

    <!-- <Textarea
        on:change={(e) => {
            note = e.target.value;
        }}
    ></Textarea>

    <div class="mt-5"></div>
    <CustomButton title="Mark paid" onClick={markPaid} isLoading={isDoingTask} /> -->
{/if}
