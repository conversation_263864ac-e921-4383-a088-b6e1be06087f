<script lang="ts">
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import DebitNoteOverviewTable from "$lib/debit-notes/components/DebitNoteOverviewTable.svelte";
    import type { IDebitNoteOverviewResponse } from "$lib/debit-notes/models/IDebitNote";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { onMount } from "svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";

    let isLoading = true;

    let paginationData: PaginatedDataWrapper<IDebitNoteOverviewResponse> = {
        pageSize: 10,
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {},
    };

    const loadData = async (page: number) => {
        isLoading = true;
        const response = await PresenterProvider.debitNotePresenter.getAll(
            page,
            paginationData.pageSize
        );
        if (!response.success) {
            return showErrorToast(response.message);
        }
        paginationData.pagination = response.data;
        paginationData.onPageChange = loadData;
        paginationData = paginationData;
        isLoading = false;
    };

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.DEBIT_NOTE.READ)) {
            isLoading = false;
            return;
        }

        loadData(1);
    });
</script>
<svelte:head><title>Debit Notes</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.DEBIT_NOTE.READ)}
    <NoPermissionView />
{:else}
    <DebitNoteOverviewTable {paginationData} />
{/if}
