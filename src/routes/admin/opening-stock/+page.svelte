<script lang="ts">
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { showErrorToast } from "$lib/common/utils/common-utils";

    
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { onMount } from "svelte";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import type { IFileRecordResponse } from "$lib/opening-stock/models/IOpeningStock";
    import FileRecordTable from "$lib/opening-stock/components/FileRecordTable.svelte";


    let isLoading = true;
    let paginationData: PaginatedDataWrapper<IFileRecordResponse> = {
        pageSize: 10,
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {}, // Will be updated later
    };

    const loadData = async (page: number) => {
        isLoading = true;
        const response = await PresenterProvider.openingStockPresenter.onLoadFileRecord(
            page,
            paginationData.pageSize
        );
        console.log(response)
        if (!response.success) {
            return showErrorToast(response.message);
        }
        paginationData.pagination = response.data;
        paginationData.onPageChange = loadData;
        paginationData = paginationData;

        isLoading = false;
    };

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.STOCK_ADJUSTMENT.READ)) {
            isLoading = false;
            return;
        }
        loadData(1);
    });
</script>

<svelte:head><title>Opening Stocks</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.STOCK_ADJUSTMENT.READ)}
    <NoPermissionView />
{:else}
    <FileRecordTable {paginationData} />
{/if}
