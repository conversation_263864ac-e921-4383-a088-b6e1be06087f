<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import ItemUnitForm from "$lib/item_unit/components/ItemUnitForm.svelte";
    import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
    import { ItemUnitUtils } from "$lib/item_unit/utils/ItemUnitUtils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";

    let isLoading: boolean = true;
    let data: IItemUnit = ItemUnitUtils.getEmpty();

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.ITEM_UNIT.UPDATE)) {
            isLoading = false;
            return;
        }

        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        if (!id || isNaN(Number(id))) {
            return goto("/admin/item-units");
        }

        const response = await PresenterProvider.itemUnitPresenter.getById(Number(id!));
        if (!response.success) {
            return showErrorToast(response.message);
        }
        data = response.data;
        isLoading = false;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.ITEM_UNIT.UPDATE)}
    <NoPermissionView />
{:else}
    <ItemUnitForm obj={data} />
{/if}
