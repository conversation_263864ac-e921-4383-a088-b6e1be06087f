<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import RawMaterialEditForm from "$lib/raw_material/components/RawMaterialEditForm.svelte";
    import type { IRawMaterial } from "$lib/raw_material/models/IRawMaterial";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";

    let isLoading: boolean = true;
    let rawMaterialData: IRawMaterial | null = null;

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.RAW_MATERIAL.UPDATE)) {
            isLoading = false;
            return;
        }

        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        if (!id || isNaN(Number(id))) {
            return goto("/admin/raw-materials");
        }
        const response = await PresenterProvider.rawMaterialPresenter.getRawMaterialById(Number(id!));
        if (!response.success) {
            return showErrorToast(response.message);
        }
        rawMaterialData = response.data;
        isLoading = false;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL.UPDATE)}
    <NoPermissionView />
{:else if rawMaterialData}
    <RawMaterialEditForm rawMaterial={rawMaterialData} />
{:else}
    <div class="flex items-center justify-center h-64">
        <p class="text-gray-500 dark:text-gray-400">Raw material not found.</p>
    </div>
{/if}
