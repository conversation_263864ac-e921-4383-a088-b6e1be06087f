<script lang="ts">
    import { onMount } from "svelte";
    import TaxTable from "$lib/tax/components/admin/TaxTable.svelte";
    import type { ITax } from "$lib/tax/models/Tax";
    import { getEmptyTaxDetails } from "$lib/tax/utils/tax-utils";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    
    let isLoading = true;
    let paginationData: PaginatedDataWrapper<ITax> = {
        pageSize: 10,
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {},
    };

    const loadData = async (page: number) => {
        isLoading = true;
        const response = await PresenterProvider.taxPresenter.onLoad(page, paginationData.pageSize);
        if (!response.success) {
            return showErrorToast(response.message);
        }
        paginationData.pagination = response.data;
        paginationData.onPageChange = loadData;
        paginationData = paginationData;
        isLoading = false;
    };

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.ITEM_CATEGORY.READ)) {
            isLoading = false;
            return;
        }
        loadData(1);
    });
</script>

{#if isLoading}
    <PageLoader />
    <!-- {:else if !$userPermissions.includes("read#tax")}
    <NoPermissionView /> -->
{:else}
    <TaxTable
        editCase={false}
        formData={getEmptyTaxDetails()}
        {paginationData}
        id={null}
        onRefresh={() => loadData(1)}
    />
{/if}
