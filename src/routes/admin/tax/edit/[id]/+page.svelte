<script lang="ts">
    import { page } from "$app/stores";
    import { onMount } from "svelte";
    import { Spinner } from "flowbite-svelte";
    import type { ITax } from "$lib/tax/models/Tax";
    import TaxForm from "$lib/tax/components/admin/TaxForm.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { goto } from "$app/navigation";

    let tax: ITax;
    let isLoading: boolean = false;

    onMount(async () => {
        const permission = $userPermissions.includes(AppPermissions.DEBIT_NOTE.PRINT);
        if (!permission) {
            showErrorToast("You do not have permission to access this page.");
            goto("/admin");
            isLoading = false;
            return;
        }

        const id = $page.params.id;
        if (id) {
            const response = await PresenterProvider.taxPresenter.getById(Number(id));

            if (!response.success) {
                showErrorToast("Tax not found!");
            }
            tax = response.data!;
        }
        // isLoading = false;
    });
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-full items-center justify-center">
        <Spinner />
    </div>
{:else if tax}
    <TaxForm editCase={true} formData={tax} />
{/if}
