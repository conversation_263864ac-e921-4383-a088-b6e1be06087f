<script lang="ts">
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import ExcelUploadForm from "$lib/excel_upload/components/ExcelUploadForm.svelte";
</script>

<!-- {#if !$userPermissions.includes(AppPermissions.EXCEL_UPLOAD.UPLOAD)} -->
    <!-- <NoPermissionView /> -->
<!-- {:else} -->
    <ExcelUploadForm />
<!-- {/if} -->
