<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";
    import type { IOpeningStock } from "$lib/opening-stock/models/IOpeningStock";
    import { OpeningStockUtils } from "$lib/opening-stock/utils/OpeningStockUtils";
    import OpeningStockEditForm from "$lib/opening-stock/components/OpeningStockEditForm.svelte";

    let isLoading: boolean = true;
    let data: IOpeningStock = OpeningStockUtils.getEmptyOpeningStock();

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.STOCK_ADJUSTMENT.UPDATE)) {
            isLoading = false;
            return;
        }

        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        console.log(id);
        if (!id || isNaN(Number(id))) {
            return goto("/admin/opening-stocks");
        }

        const response = await PresenterProvider.openingStockPresenter.loadById(Number(id!));

        console.log("response", response);

        if (!response.success) {
            return showErrorToast(response.message);
        }
        data = response.data;
        isLoading = false;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.ITEM_UNIT.UPDATE)}
    <NoPermissionView />
{:else}
    <OpeningStockEditForm obj={data} />
{/if}
