<script lang="ts">
    import OpeningStockForm from "$lib/opening-stock/components/OpeningStockForm.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
</script>

{#if !$userPermissions.includes(AppPermissions.STOCK_ADJUSTMENT.CREATE)}
    <NoPermissionView />
{:else}
    <OpeningStockForm />
{/if}
