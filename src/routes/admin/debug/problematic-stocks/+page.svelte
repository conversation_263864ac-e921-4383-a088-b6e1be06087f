<script lang="ts">
    import ProblematicStocksList from '$lib/components/debug/ProblematicStocksList.svelte';
    
    function handleRefresh() {
        console.log('Data refreshed successfully!');
        // You can add additional logic here when data is refreshed
    }
</script>

<svelte:head>
    <title>Problematic Stocks Debug | IMS Admin Panel</title>
    <meta name="description" content="Debug interface for identifying raw material stock inconsistencies" />
</svelte:head>

<main class="min-h-screen bg-gray-100">
    <div class="container mx-auto py-8">
        <!-- Page header -->
        <div class="mb-8">
            <nav class="text-sm breadcrumbs mb-4">
                <ul class="flex items-center space-x-2 text-gray-500">
                    <li><a href="/" class="hover:text-blue-600">Dashboard</a></li>
                    <li class="before:content-['/'] before:mx-2">Debug Tools</li>
                    <li class="before:content-['/'] before:mx-2 text-gray-900">Problematic Stocks</li>
                </ul>
            </nav>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-center gap-2">
                    <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <h3 class="text-sm font-medium text-yellow-800">Development Tool</h3>
                        <p class="text-sm text-yellow-700 mt-1">
                            This debug interface is only available in development environment and helps identify stock calculation inconsistencies.
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main component -->
        <ProblematicStocksList onRefresh={handleRefresh} />
    </div>
</main>

<style>
    /* Custom styles for the debug page */
    .breadcrumbs a {
        transition: color 0.2s ease;
    }
</style> 