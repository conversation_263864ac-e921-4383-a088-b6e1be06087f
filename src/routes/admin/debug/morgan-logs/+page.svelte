<script lang="ts">
    import LogsDashboard from '$lib/components/logs/LogsDashboard.svelte';
    
    let autoRefresh = false;
    let refreshInterval = 30000; // 30 seconds
</script>

<svelte:head>
    <title>Server Logs Dashboard | IMS Admin Panel</title>
    <meta name="description" content="Monitor and analyze server logs with comprehensive statistics and filtering" />
</svelte:head>

<main class="min-h-screen bg-gray-100">
    <div class="container mx-auto">
        <!-- Main dashboard component -->
        <LogsDashboard 
            bind:autoRefresh 
            {refreshInterval}
        />
    </div>
</main>

<style>
    /* Custom styles for the logs page */
    .breadcrumbs a {
        transition: color 0.2s ease;
    }
</style> 