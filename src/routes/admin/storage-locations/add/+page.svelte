<script lang="ts">
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import StorageLocationForm from "$lib/storage_locations/components/StorageLocationForm.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
</script>

{#if !$userPermissions.includes(AppPermissions.STORAGE_LOCATION.CREATE)}
    <NoPermissionView />
{:else}
    <StorageLocationForm />
{/if}
