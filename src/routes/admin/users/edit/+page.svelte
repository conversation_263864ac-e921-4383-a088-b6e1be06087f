<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import UserForm from "$lib/users/components/admin/UserForm.svelte";
    import type { IUserUpdateRequest } from "$lib/users/models/User";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { USIUserUtils } from "$lib/users/utils/USIUserUtils";
    import { onMount } from "svelte";

    let isLoading: boolean = true;
    let data: IUserUpdateRequest = USIUserUtils.getEmpty();

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.USER_ROLE.UPDATE)) {
            isLoading = false;
            return;
        }

        const params = new URLSearchParams(window.location.search);
        const id = params.get("id");
        if (!id || isNaN(Number(id))) {
            return goto("/admin/users");
        }

        const response = await PresenterProvider.userPresenter.getById(Number(id!));
        if (!response.success) {
            return showErrorToast(response.message);
        }
        data = {
            id: response.data.id,
            firstName: response.data.firstName,
            lastName: response.data.lastName,
            roleId: response.data.role.id,
            status: response.data.status,
            address: response.data.address,
            coreUserId: response.data.coreUserId,
            mobile: response.data.mobile,
        };
        isLoading = false;
    });
</script>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.USER_ROLE.UPDATE)}
    <NoPermissionView />
{:else}
    <UserForm user={data} isEditCase={true}/>
{/if}
