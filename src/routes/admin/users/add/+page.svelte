<script lang="ts">
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import UserForm from "$lib/users/components/admin/UserForm.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
</script>

{#if !$userPermissions.includes(AppPermissions.USER.CREATE)}
    <NoPermissionView />
{:else}
    <UserForm />
{/if}
