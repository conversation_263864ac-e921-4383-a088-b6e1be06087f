<script lang="ts">
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import RawMaterialStockInTable from "$lib/raw_materia_stock/components/RawMaterialStockInTable.svelte";
    import type { IRawMaterialStockInDetails } from "$lib/raw_materia_stock/models/IRawMaterialStock";
    import { onMount } from "svelte";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import type { DTO } from "$lib/common/models/BaseDTO";
    import type {
        PaginatedBaseResponse,
        PaginatedDataWrapper,
    } from "$lib/common/models/base_model";
    import { userPermissions } from "$lib/common/utils/store";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";

    let isLoading = true;

    let paginationData: PaginatedDataWrapper<IRawMaterialStockInDetails> = {
        pageSize: 10,
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {},
    };

    let filter: { startDate: string; endDate: string } | null = null;

    const getData = async (page: number) => {
        isLoading = true;

        let response: DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>;

        if (!filter) {
            response =
                await PresenterProvider.rawMaterialStockPresenter.getStockInEntriesWithoutStorageLocation(
                    page,
                    paginationData.pageSize
                );
        } else {
            response = await PresenterProvider.rawMaterialStockPresenter.getStockInEntriesByDate(
                filter!.startDate,
                filter!.endDate,
                page,
                paginationData.pageSize
            );
        }

        if (!response.success) {
            return showErrorToast(response.message);
        }
        paginationData.pagination = response.data;
        paginationData.onPageChange = getData;
        paginationData = paginationData;

        isLoading = false;
    };

    onMount(async () => {
        if (!$userPermissions.includes(AppPermissions.RAW_MATERIAL_STOCK.ASSIGN_STORAGE)) {
            isLoading = false;
            return;
        }
        getData(1);
    });
</script>
<svelte:head><title>Assign Storage</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL_STOCK.ASSIGN_STORAGE)}
    <NoPermissionView />
{:else}
    <RawMaterialStockInTable
        currentFilter={undefined}
        onSearchClear={() => {
            filter = null;
            getData(1);
        }}
        withoutStorageLocation={true}
        onDateFilterChange={(dataFilter) => {
            filter = dataFilter;
            getData(1);
        }}
        {paginationData}
    />
{/if}
