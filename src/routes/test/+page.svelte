<script>
    import Dropdown from "$lib/item_unit/components/Dropdown.svelte";

    
  
    // Basic options
    const options = [
      { label: 'Option 1', value: '1' },
      { label: 'Option 2', value: '2' },
      { label: 'Option 3', value: '3' }
    ];
  
    // Options with disabled items
    const optionsWithDisabled = [
      { label: 'Enabled Option', value: '1' },
      { label: 'Disabled Option', value: '2', disabled: true }
    ];
  
    let selected = null;
  </script>
  
  <Dropdown
    options={options}
    placeholder="Select an option"
    on:change={(e) => console.log('Selected:', e.detail.value)}
  />