<script>
    import SignInForm from "$lib/auth/components/client/SignInForm.svelte";
    import { confirmDialog } from "$lib/common/utils/custom_confirmation_component_store";
    import { onMount } from "svelte";
    onMount(()=>{
        confirmDialog.update(() => ({
            show: false,
            heading: "Confirmation",
            body: "Are you sure you want to proceed?",
            positiveButtonText: "Yes",
            negativeButtonText: "No",
            positiveAction: undefined,
            negativeAction: undefined,
        }));
    });
</script>

<SignInForm />
