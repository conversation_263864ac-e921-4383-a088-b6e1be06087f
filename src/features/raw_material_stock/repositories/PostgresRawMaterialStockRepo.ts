import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { IRawMaterialStockRepo } from "./IRawMaterialStockRepo";
import { IAssignStorageToStockRequest, IRawMaterialStockDetails, IRawMaterialStockDetailsWithPrice, IRawMaterialStockInDetails, IRawMaterialStockUpdateRequest, IReceiveRawMaterialStock } from "../models/IRawMaterialStock";
import { RawMaterialStockTable } from "../database/RawMaterialStockTable";
import { RawMaterialVariationTable } from "../../raw_material/database/RawMaterialVariationTable";
import { RawMaterialStockInTable } from "../database/RawMaterialStockInTable";
import { SupplierTable } from "../../supplier/database/SupplierTable";
import { FactoryGateTable } from "../../factory_gates/database/FactoryGateTable";
import { StorageLocationTable } from "../../storage_locations/database/StorageLocationTable";
import { PurchaseInvoiceTable } from "../../purchase_invoice/database/PurchaseInvoiceTable";
import { ICreateRawMaterialStockIssuance, IRawMaterialStockIssuanceResponse } from "../models/IRawMaterialStockIssuance";
import { RawMaterialStockIssuanceTable } from "../database/RawMaterialStockIssuanceTable";
import { col, DatabaseError, fn, Op, QueryTypes, Sequelize, Transaction } from "sequelize";
import { ItemUnitTable } from "../../item_unit/database/ItemUnitTable";
import { RawMaterialRejectionTable } from "../database/RawMaterialRejectionTable";
import { RawMaterialHoldTable } from "../database/RawMaterialHoldTable";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { ItemCategoryTable } from "../../item_category/database/ItemCategoryTable";
import { RawMaterialPriceTable } from "../../raw_material/database/RawMaterialPriceTable";
import { NormalUserTable } from "../../users/sub_feaures/normal_user/database/NormalUserTable";
import { RawMaterialExcessEntryTable } from "../../raw_material/database/RawMaterialExcessEntry";
import { RawMaterialReplacementEntryTable } from "../../raw_material/database/RawMaterialReplacementEntry";
import { RawMaterialStockConstants } from "../utils/RawMaterialStockConstants";
import { RawMaterialMainTable } from "../../raw_material/database/RawMaterialMainTable";

export class PostgresRawMaterialStockRepo implements IRawMaterialStockRepo {
    async issueStock(payload: ICreateRawMaterialStockIssuance[], transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {

            const rawMaterialIds = payload.map(data => data.rawMaterialId);

            const rawMaterials = await RawMaterialVariationTable.findAll({
                where: {
                    id: rawMaterialIds
                },
                transaction
            });

            if (rawMaterials.length !== rawMaterialIds.length) {

                return HelperMethods.getErrorResponse("Some raw materials not found");
            }

            const stockData = await RawMaterialStockTable.findAll({
                where: {
                    rawMaterialId: rawMaterialIds
                },
                transaction
            });

            const stockMap = new Map(stockData.map(stock => [Number(stock.dataValues.rawMaterialId), stock]));

            let stock: RawMaterialStockTable | undefined;

            /* get entry id */
            const entrySequence = await sequelizeInit.query(
                `SELECT nextval(:sequenceName)`,
                {
                    replacements: {
                        sequenceName: RawMaterialStockConstants.ISSUANCE_TABLE_ENTRY_ID_SEQUENCE_NAME,
                    },
                    type: QueryTypes.SELECT,
                    transaction: transaction,
                }
            )
            const entryId = (entrySequence[0] as any).nextval

            for (const data of payload) {
                stock = stockMap.get(Number(data.rawMaterialId));
                if (!stock || stock.dataValues.usableStock < data.qty) {

                    return HelperMethods.getErrorResponse(
                        `Not enough stock for Raw Material ID: ${data.rawMaterialId}`
                    );
                }
                else {
                    data.entryId = "usi-rmi-" + entryId.toString();
                }
            }

            await Promise.all(
                payload.map(async (data) => {
                    stock = stockMap.get(Number(data.rawMaterialId));
                    await RawMaterialStockIssuanceTable.create(data, {
                        userId: data.createdById,
                        individualHooks: true,
                        transaction
                    });

                    await RawMaterialStockTable.decrement(["totalStock", "usableStock"], {
                        by: data.qty,
                        where: {
                            rawMaterialId: data.rawMaterialId,
                        },
                        transaction
                    });
                })
            );

            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getAllStockIssuance(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse> | null>> {
        try {
            const offset = (page - 1) * pageSize;


            /* get distinct data */
            const distinctData = await RawMaterialStockIssuanceTable.count({
                distinct: true,
                attributes: ['entryId'],
                group: ['entryId'], transaction
            });


            const { count, rows } = await RawMaterialStockIssuanceTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                transaction,
                include:
                    [
                        {
                            model: RawMaterialVariationTable,
                            as: 'rawMaterial',
                            required: true,
                        },
                        {
                            model: CoreUserTable,
                            as: "issuedBy",
                            required: true,
                            include: [
                                {
                                    model: NormalUserTable,
                                    as: "normalUser",
                                }
                            ]
                        },
                        {
                            model: CoreUserTable,
                            as: "issuedToUser",
                            include: [
                                {
                                    model: NormalUserTable,
                                    as: "normalUser",
                                }
                            ]
                        },

                    ],

            });

            const data: IRawMaterialStockIssuanceResponse[] = [];

            let groupedData: RawMaterialStockIssuanceTable[] = [];

            let dataToIterate: RawMaterialStockIssuanceTable[] = [];

            dataToIterate = rows;

            for (const fetchedItem of dataToIterate) {
                /* find all for a single */
                groupedData = dataToIterate.filter(data => data.dataValues.entryId === fetchedItem.dataValues.entryId);
                if (groupedData.length > 0) {
                    /* add data */
                    data.push({
                        entryId: fetchedItem.dataValues.entryId,
                        soNumber: fetchedItem.dataValues.soNumber,
                        notes: fetchedItem.dataValues.notes,
                        issuedTo: fetchedItem.issuedToUser ? {
                            ...fetchedItem.issuedToUser.dataValues,
                            id: Number(fetchedItem.issuedToUser.dataValues.id),
                            coreUserId: Number(fetchedItem.issuedToUser.normalUser.dataValues.coreUserId),
                            address: {} as any,
                            role: {
                            } as any
                        } : null,
                        issuedBy: {
                            ...fetchedItem.issuedBy.dataValues,
                            id: Number(fetchedItem.issuedBy.dataValues.id),
                            coreUserId: Number(fetchedItem.issuedBy.normalUser.dataValues.coreUserId),
                            address: {} as any,
                            role: {
                            } as any
                        },
                        rawMaterials: groupedData.map(data => ({
                            rawMaterial: {
                                ...data.rawMaterial.dataValues,
                                id: Number(data.rawMaterial.dataValues.id),
                                unitName: "",
                                categoryName: "",
                                priceData: [],
                            },
                            qty: Number(data.dataValues.qty),
                        })),
                        issuedAt: fetchedItem.dataValues.createdAt,
                    });
                }
                /*delete */
                dataToIterate = dataToIterate.filter(data => data.dataValues.entryId !== fetchedItem.dataValues.entryId);

            }
            const totalPages = Math.ceil(distinctData.length / pageSize);
            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: distinctData.length,
                totalPages: totalPages,
                data: data,
            });

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchStockIssuanceByText(text: string, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse> | null>> {
        try {

            /* first get raw materials */
            const rawMaterials = await RawMaterialVariationTable.findAll({
                where: {
                    name: {
                        [Op.iLike]: `%${text}%`
                    }
                }, transaction
            });


            const rawMaterialIds = rawMaterials.map((item) => Number(item.dataValues.id));

            const { count, rows } = await RawMaterialStockIssuanceTable.findAndCountAll({
                limit: 10,
                order: [['createdAt', 'DESC']],
                transaction,
                where: {

                    [Op.or]: [
                        {


                            rawMaterialId: {
                                [Op.in]: rawMaterialIds
                            },
                        },
                        {
                            soNumber: {
                                [Op.iLike]: `%${text}%`
                            }
                        }
                    ]
                },


                include: [{
                    model: RawMaterialVariationTable,
                    as: "rawMaterial",
                },

                {
                    model: CoreUserTable,
                    as: "issuedBy",
                },
                {
                    model: CoreUserTable,
                    as: "issuedToUser",
                }
                ],
            });

            const totalPages = 1;

            // const data: IRawMaterialStockIssuanceResponse[] = [];
            const data: any[] = [];

            for (const item of rows) {
                data.push({
                    id: item.getDataValue('id'),
                    entryId: item.dataValues.entryId,
                    qty: item.dataValues.qty,
                    soNumber: item.dataValues.soNumber,
                    rawMaterial: {
                        id: item.dataValues.rawMaterialId,
                        name: item.rawMaterial.dataValues.name,
                    },
                    issuedBy: {
                        id: Number(item.dataValues.createdById),
                        firstName: item.issuedBy.dataValues.firstName,
                        lastName: item.issuedBy.dataValues.lastName,
                    },
                    issuedTo: item.dataValues.issuedTo,
                    issuedToUser: item.issuedToUser ? {
                        id: Number(item.issuedToUser.dataValues.id),
                        firstName: item.issuedToUser.dataValues.firstName,
                        lastName: item.issuedToUser.dataValues.lastName,
                    } : null,
                    notes: item.getDataValue("notes"),
                    createdAt: item.getDataValue('createdAt'),
                    createdById: item.getDataValue('createdById'),
                    updatedAt: item.getDataValue('updatedAt'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                    deletedById: item.getDataValue('deletedById'),
                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async assignStorageToStock(request: IAssignStorageToStockRequest, transaction: Transaction): Promise<APIBaseResponse<null>> {

        try {


            /* get stock in entry */
            const stockInEntry = await RawMaterialStockInTable.findByPk(request.id, {
                transaction: transaction,
                include: [
                    {
                        model: PurchaseInvoiceTable,
                        as: "purchaseInvoice",
                        include: [

                            {
                                model: RawMaterialRejectionTable,
                                as: "rawMaterialRejections",
                                attributes: ['rejectedQty', "rawMaterialId"],
                            },
                            {
                                model: RawMaterialHoldTable,
                                as: "rawMaterialHolds",
                                attributes: ['holdQty', "rawMaterialId"],
                            },
                            {
                                model: RawMaterialExcessEntryTable,
                                as: "rawMaterialExcessEntry",
                                attributes: ['qty', "rawMaterialId"]
                            },
                            {
                                model: RawMaterialReplacementEntryTable,
                                as: "rawMaterialReplacementEntry",
                                attributes: ['qty', "rawMaterialId"]
                            }
                        ]
                    }
                ]
            });

            let isStorageLocationAlredyAssigned = false;

            if (!stockInEntry) {
                return HelperMethods.getErrorResponse("Stock in entry not found");
            }

            /* check if storage location already assigned */
            if (stockInEntry.dataValues.storageLocationId !== null) {
                isStorageLocationAlredyAssigned = true;
                // return HelperMethods.getErrorResponse("Storage location already assigned");
            }




            let rejectedQty = 0;
            let holdQty = 0;
            let incrementQty = 0;
            let excessQty = 0;
            let replaceableQty = 0;



            rejectedQty = Number((stockInEntry.purchaseInvoice.rawMaterialRejections
                .filter(x => x.dataValues.rawMaterialId === stockInEntry.dataValues.rawMaterialId)
                .reduce((sum, rejection) => sum + (Number(rejection.dataValues.rejectedQty) ?? 0), 0)
                .toFixed(2)));

            holdQty = Number((stockInEntry.purchaseInvoice.rawMaterialHolds
                .filter(x => x.dataValues.rawMaterialId === stockInEntry.dataValues.rawMaterialId)
                .reduce((sum, hold) => sum + (Number(hold.dataValues.holdQty) ?? 0), 0)
                .toFixed(2)));

            excessQty = Number((stockInEntry.purchaseInvoice.rawMaterialExcessEntry
                .filter(x => x.dataValues.rawMaterialId === stockInEntry.dataValues.rawMaterialId)
                .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                .toFixed(2)));

            replaceableQty = Number((stockInEntry.purchaseInvoice.rawMaterialReplacementEntry
                .filter(x => x.dataValues.rawMaterialId === stockInEntry.dataValues.rawMaterialId)
                .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                .toFixed(2)));

            incrementQty = Number(((Number(stockInEntry.dataValues.qty) + excessQty) - (rejectedQty + holdQty + replaceableQty)).toFixed(2));


            /* update stock in entry */
            await RawMaterialStockInTable.update({
                storageLocationId: request.storageLocationId,
                updatedById: request.updatedById,
            }, {
                where: {
                    id: request.id,
                },
                transaction: transaction,
                userId: request.updatedById,
                individualHooks: true,
            });







            /* update stock */

            if (!isStorageLocationAlredyAssigned) {
                await RawMaterialStockTable.increment("usableStock", {
                    by: incrementQty,
                    where: {
                        rawMaterialId: stockInEntry.dataValues.rawMaterialId,
                    },
                    transaction: transaction,
                });
            }


            return HelperMethods.getSuccessResponse(null);

        } catch (error) {

            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();

        }
    }

    async updateStock(request: IRawMaterialStockUpdateRequest, transaction: Transaction): Promise<APIBaseResponse<null>> {

        try {

            await RawMaterialStockTable.update({
                usableStock: request.qty,
            }, {
                where: {
                    rawMaterialId: request.rawMaterialId,

                },
                userId: request.updatedById!,
                individualHooks: true,
                transaction
            });


            return HelperMethods.getSuccessResponse(null);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();

        }


    }

    async receiveStock(payload: IReceiveRawMaterialStock, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            // 

            // /* save the purchase invoice */
            // const invoice = await PurchaseInvoiceTable.create({
            //     invoiceDate: payload.invoiceDate,
            //     supplierId: payload.supplierId,
            //     status: PURCHASE_INVOICE_STATUS.ACTIVE,
            //     createdById: payload.createdById,
            //     invoiceNumber: payload.invoiceNumber,
            // }, {
            //     transaction: transaction,
            // });


            // /* save raw material stock in */
            // await RawMaterialStockInTable.create(payload, {
            //     transaction: transaction,
            // });

            // /* update the raw material stock */
            // await RawMaterialStockTable.increment("totalStock", {
            //     by: payload.qty,
            //     where: {
            //         rawMaterialId: payload.rawMaterialId,
            //     },
            //     transaction: transaction,
            // });

            // await transaction.commit();
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockDetails> | null>> {
        try {
            const offset = (page - 1) * pageSize;
            const { count, rows } = await RawMaterialStockTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                transaction,
                include: [
                    {
                        model: RawMaterialVariationTable,
                        as: "rawMaterialVariation",
                        include: [
                            {
                                model: RawMaterialMainTable,
                                as: "rawMaterial",
                                include: [
                                    {
                                        model: ItemCategoryTable,
                                        attributes: ['name'],
                                        as: "category",
                                        where: {
                                            deletedAt: null
                                        }
                                    }
                                ]
                            }
                        ],
                        where: {
                            deletedAt: null
                        }
                    }
                ],
            });


            const totalPages = Math.ceil(count / pageSize);

            const data: IRawMaterialStockDetails[] = [];

            for (const item of rows) {
                data.push({
                    id: item.getDataValue('id'),
                    rawMaterialId: item.getDataValue('rawMaterialId'),
                    rawMaterialName: item.rawMaterialVariation.dataValues.name,
                    sku: item.rawMaterialVariation.dataValues.sku,
                    categoryName: item.rawMaterialVariation.rawMaterial.category.dataValues.name,
                    msq: item.rawMaterialVariation.dataValues.msq,
                    totalStock: item.getDataValue('totalStock'),
                    usableStock: item.dataValues.usableStock,
                    assignedStock: item.getDataValue('assignedStock'),
                    createdAt: item.getDataValue('createdAt'),
                    createdById: item.getDataValue('createdById'),
                    updatedAt: item.getDataValue('updatedAt'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                    deletedById: item.getDataValue('deletedById'),
                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchByRawMaterial(rawMaterialName: string, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockDetails> | null>> {
        try {

            /* get raw material, where name contains the raw material name */
            const rawMaterial = await RawMaterialVariationTable.findAll({
                where: {
                    name: {
                        [Op.iLike]: `%${rawMaterialName}%`,
                    }
                },
                transaction,
            });



            const { count, rows } = await RawMaterialStockTable.findAndCountAll({
                where: {
                    rawMaterialId: {
                        [Op.in]: rawMaterial.map((item) => item.dataValues.id)
                    }
                },
                limit: 5,
                order: [['createdAt', 'DESC']],
                transaction,
                include: [{
                    model: RawMaterialVariationTable,
                    as: "rawMaterialVariation",
                    where: {
                        deletedAt: null
                    },
                    include: [{
                        model: RawMaterialMainTable,
                        as: "rawMaterial",
                        include: [{
                            model: ItemCategoryTable,
                            attributes: ['name'],
                            as: "category",
                        }]
                    }]
                }],
            });

            const totalPages = 1;

            const data: IRawMaterialStockDetails[] = [];

            for (const item of rows) {
                data.push({
                    id: item.getDataValue('id'),
                    rawMaterialId: item.getDataValue('rawMaterialId'),
                    rawMaterialName: item.rawMaterialVariation.dataValues.name,
                    sku: item.rawMaterialVariation.dataValues.sku,
                    categoryName: item.rawMaterialVariation.rawMaterial.category.dataValues.name,
                    msq: item.rawMaterialVariation.dataValues.msq,
                    totalStock: item.getDataValue('totalStock'),
                    usableStock: item.dataValues.usableStock,
                    assignedStock: item.getDataValue('assignedStock'),
                    createdAt: item.getDataValue('createdAt'),
                    createdById: item.getDataValue('createdById'),
                    updatedAt: item.getDataValue('updatedAt'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                    deletedById: item.getDataValue('deletedById'),
                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number, transaction: Transaction): Promise<APIBaseResponse<IRawMaterialStockDetails | null>> {
        try {
            const result = await RawMaterialStockTable.findByPk(id, {
                transaction,
                include: [{
                    model: RawMaterialVariationTable,
                    attributes: ['name'],
                    as: 'rawMaterial',
                    include: [{
                        model: ItemCategoryTable,
                        attributes: ['name'],
                        as: "category",
                    }]
                }],
            });


            if (!result) {
                return HelperMethods.getErrorResponse("Raw Material Stock not found");
            }
            return HelperMethods.getSuccessResponse(
                {
                    id: result.getDataValue('id'),
                    rawMaterialId: result.getDataValue('rawMaterialId'),
                    sku: result.rawMaterialVariation.getDataValue('sku'),
                    rawMaterialName: result.rawMaterialVariation.getDataValue('name'),
                    categoryName: result.rawMaterialVariation.rawMaterial.category.getDataValue('name'),
                    msq: result.rawMaterialVariation.getDataValue('msq'),
                    totalStock: result.getDataValue('totalStock'),
                    usableStock: result.dataValues.usableStock,
                    assignedStock: result.getDataValue('assignedStock'),
                    createdAt: result.getDataValue('createdAt'),
                    createdById: result.getDataValue('createdById'),
                    updatedAt: result.getDataValue('updatedAt'),
                    updatedById: result.getDataValue('updatedById'),
                    deletedAt: result.getDataValue('deletedAt'),
                    deletedById: result.getDataValue('deletedById'),
                }
            );
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getByRawMaterialId(id: number, transaction: Transaction): Promise<APIBaseResponse<IRawMaterialStockDetails | null>> {
        try {
            const result = await RawMaterialStockTable.findOne({
                where: {
                    rawMaterialId: id,
                },
                transaction,
                include: [{
                    model: RawMaterialVariationTable,
                    attributes: ['name'],
                    as: 'rawMaterialVariation',
                    where: {
                        deletedAt: null
                    },
                    include: [
                        {
                            model: RawMaterialMainTable,
                            attributes: ['name'],
                            as: 'rawMaterial',
                            include: [
                                {
                                    model: ItemCategoryTable,
                                    attributes: ['name'],
                                    as: "category",
                                }
                            ]
                        }
                    ]
                }],
            });


            if (!result) {
                return HelperMethods.getErrorResponse("Raw Material Stock not found");
            }
            return HelperMethods.getSuccessResponse(
                {
                    id: result.getDataValue('id'),
                    rawMaterialId: result.getDataValue('rawMaterialId'),
                    sku: result.rawMaterialVariation.getDataValue('sku'),
                    rawMaterialName: result.rawMaterialVariation.getDataValue('name'),
                    categoryName: result.rawMaterialVariation.rawMaterial.category.getDataValue('name'),
                    msq: result.rawMaterialVariation.getDataValue('msq'),
                    totalStock: result.getDataValue('totalStock'),
                    usableStock: result.dataValues.usableStock,
                    assignedStock: result.getDataValue('assignedStock'),
                    createdAt: result.getDataValue('createdAt'),
                    createdById: result.getDataValue('createdById'),
                    updatedAt: result.getDataValue('updatedAt'),
                    updatedById: result.getDataValue('updatedById'),
                    deletedAt: result.getDataValue('deletedAt'),
                    deletedById: result.getDataValue('deletedById'),
                }
            );
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getStockIn(page: number, pageSize: number,transaction: Transaction, text?: string, startDate?: Date,
        endDate?: Date): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>> {
        try {

            let whereConditions: any = {};

            if (startDate && endDate) {
                whereConditions.createdAt = {
                    [Op.between]: [startDate, endDate]
                };
            }

            if (text) {
                whereConditions[Op.or] = [
                    {
                        '$rawMaterialVariation.name$': { [Op.iLike]: `%${text}%` }
                    },
                    {
                        '$rawMaterialVariation.rawMaterial.category.name$': { [Op.iLike]: `%${text}%` }
                    },
                    {
                        '$supplier.name$': { [Op.iLike]: `%${text}%` }
                    },
                    {
                        '$purchaseInvoice.poNumber$': { [Op.iLike]: `%${text}%` }
                    },
                    {
                        '$purchaseInvoice.invoiceNumber$': { [Op.iLike]: `%${text}%` }
                    }
                ];
            }

            const offset = (page - 1) * pageSize;
            const { count, rows } = await RawMaterialStockInTable.findAndCountAll({
                limit: pageSize === -1 ? undefined : pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: whereConditions,
                subQuery: false,
                transaction,
                include: [
                    {
                        model: RawMaterialVariationTable,
                        attributes: ['id', 'name'],
                        as: "rawMaterialVariation",
                        where: {
                            deletedAt: null
                        },
                        include: [{
                            model: RawMaterialMainTable,
                            attributes: ['id', 'name'],
                            as: "rawMaterial",
                            include: [{
                                model: ItemCategoryTable,
                                attributes: ['name'],
                                as: "category",

                            },
                            {
                                model: ItemUnitTable,
                                attributes: ['name'],
                                as: "unit",
                            }
                            ]
                        }]
                    },
                    {
                        model: SupplierTable,
                        attributes: ['name'],
                        as: "supplier",

                    },
                    {
                        model: FactoryGateTable,
                        attributes: ['name'],
                        as: "factoryGate",

                    },
                    {
                        model: StorageLocationTable,
                        attributes: ['name'],
                        as: "storageLocation",

                    },
                    {
                        model: PurchaseInvoiceTable,
                        as: "purchaseInvoice",
                        include: [
                            {
                                model: CoreUserTable,
                                as: "purchasedBy",

                            },
                            {
                                model: RawMaterialRejectionTable,
                                as: "rawMaterialRejections",
                                attributes: ['rejectedQty', 'rawMaterialId']
                            },
                            {
                                model: RawMaterialHoldTable,
                                as: "rawMaterialHolds",
                                attributes: ['holdQty', 'rawMaterialId']
                            },
                            {
                                model: RawMaterialExcessEntryTable,
                                as: "rawMaterialExcessEntry",
                                attributes: ['qty', 'rawMaterialId']
                            },
                            {
                                model: RawMaterialReplacementEntryTable,
                                as: "rawMaterialReplacementEntry",
                                attributes: ['qty', 'rawMaterialId']
                            }
                        ]
                    }
                ]
            });

            const totalPages = Math.ceil(count / pageSize);

            const data: IRawMaterialStockInDetails[] = [];


            let rejectedQty = 0;
            let holdQty = 0;
            let excessQty = 0;
            let replaceableQty = 0;

            for (const item of rows) {

                rejectedQty = Number(item.purchaseInvoice.rawMaterialRejections
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, rejection) => sum + (Number(rejection.dataValues.rejectedQty) ?? 0), 0)
                    .toFixed(2));

                holdQty = Number(item.purchaseInvoice.rawMaterialHolds
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, hold) => sum + (Number(hold.dataValues.holdQty) ?? 0), 0)
                    .toFixed(2));

                excessQty = Number(item.purchaseInvoice.rawMaterialExcessEntry
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                    .toFixed(2));

                replaceableQty = Number(item.purchaseInvoice.rawMaterialReplacementEntry
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                    .toFixed(2));


                data.push({
                    id: item.dataValues.id,
                    rawMaterialId: item.rawMaterialVariation.dataValues.id,
                    rawMaterial: item.rawMaterialVariation.dataValues.name,
                    categoryName: item.rawMaterialVariation.rawMaterial.category.dataValues.name,
                    rawMaterialUnit: item.rawMaterialVariation.rawMaterial.unit.dataValues.name,
                    supplierId: item.getDataValue('supplierId'),
                    supplier: item.supplier.getDataValue('name'),
                    price: item.getDataValue('price'),
                    storageLocationId: item.getDataValue('storageLocationId'),
                    storageLocation: item.storageLocation?.getDataValue('name') ?? null,
                    factoryGateId: item.getDataValue('factoryGateId'),
                    factoryGate: item.factoryGate.getDataValue('name'),
                    totalQty: Number(item.getDataValue('qty')),
                    rejectedQty: rejectedQty,
                    holdQty: holdQty,
                    excessQty: excessQty,
                    replaceableQty: replaceableQty,
                    createdById: item.getDataValue('createdById'),
                    updatedAt: item.getDataValue('updatedAt'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                    deletedById: item.getDataValue('deletedById'),
                    poNumber: item.purchaseInvoice.dataValues.poNumber,
                    purchaseInvoiceNumber: item.purchaseInvoice.dataValues.invoiceNumber,
                    purchasedBy: item.purchaseInvoice.purchasedBy.dataValues.firstName,
                    receivedAt: item.purchaseInvoice.dataValues.invoiceDate,
                    createdAt: item.purchaseInvoice.dataValues.createdAt,

                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getStockInWithoutStorage(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>> {
        try {
            const offset = (page - 1) * pageSize;
            const { count, rows } = await RawMaterialStockInTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: {
                    storageLocationId: null,
                },
                transaction,
                include: [
                    {
                        model: RawMaterialVariationTable,
                        attributes: ['name', 'id', 'parentRawMaterialId'],
                        as: "rawMaterialVariation",
                        where: {
                            deletedAt: null
                        },
                        include: [
                            {
                                model: RawMaterialMainTable,
                                as: "rawMaterial",
                                include: [
                                    {
                                        model: ItemCategoryTable,
                                        attributes: ['name'],
                                        as: "category",
                                    },
                                    {
                                        model: ItemUnitTable,
                                        attributes: ['name'],
                                        as: "unit",
                                    }
                                ]
                            }

                        ]
                    },
                    {
                        model: SupplierTable,
                        attributes: ['name',],
                        as: "supplier",

                    },
                    {
                        model: FactoryGateTable,
                        attributes: ['name',],
                        as: "factoryGate",

                    },
                    {
                        model: StorageLocationTable,
                        attributes: ['name',],
                        as: "storageLocation",

                    },
                    {
                        model: PurchaseInvoiceTable,
                        as: "purchaseInvoice",
                        include: [
                            {
                                model: CoreUserTable,
                                as: "purchasedBy",
                            },
                            {
                                model: RawMaterialRejectionTable,
                                as: "rawMaterialRejections",
                                attributes: ['rejectedQty', 'rawMaterialId'],
                            },
                            {
                                model: RawMaterialHoldTable,
                                as: "rawMaterialHolds",
                                attributes: ['holdQty', 'rawMaterialId'],
                            },
                            {
                                model: RawMaterialExcessEntryTable,
                                as: "rawMaterialExcessEntry",
                                attributes: ['qty', 'rawMaterialId']
                            },
                            {
                                model: RawMaterialReplacementEntryTable,
                                as: "rawMaterialReplacementEntry",
                                attributes: ['qty', 'rawMaterialId']
                            }
                        ]
                    },
                ],
            });




            const totalPages = Math.ceil(count / pageSize);

            const data: IRawMaterialStockInDetails[] = [];


            let rejectedQty = 0;
            let holdQty = 0;
            let excessQty = 0;
            let replaceableQty = 0;


            for (const item of rows) {
                console.log("checking rejected qty for");
                console.log(item.purchaseInvoice.rawMaterialRejections);
                console.log("2");

                console.log(item.rawMaterialVariation.dataValues);
                rejectedQty = (item.purchaseInvoice.rawMaterialRejections
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, rejection) => sum + (Number(rejection.dataValues.rejectedQty) ?? 0), 0));
                console.log("rejected qty", rejectedQty);

                rejectedQty = Number(rejectedQty.toFixed(2));

                holdQty = item.purchaseInvoice.rawMaterialHolds
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, hold) => sum + (Number(hold.dataValues.holdQty) ?? 0), 0);

                holdQty = Number(holdQty.toFixed(2));

                excessQty = item.purchaseInvoice.rawMaterialExcessEntry
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0);

                excessQty = Number(excessQty.toFixed(2));

                replaceableQty = item.purchaseInvoice.rawMaterialReplacementEntry
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0);

                replaceableQty = Number(replaceableQty.toFixed(2));

                data.push({
                    id: item.getDataValue('id'),
                    rawMaterialId: item.getDataValue('rawMaterialId'),
                    categoryName: item.rawMaterialVariation.rawMaterial.category.dataValues.name,
                    rawMaterial: item.rawMaterialVariation.getDataValue('name'),
                    rawMaterialUnit: item.rawMaterialVariation.rawMaterial.unit.dataValues.name,
                    supplierId: item.getDataValue('supplierId'),
                    supplier: item.supplier.getDataValue('name'),
                    price: item.getDataValue('price'),
                    storageLocationId: item.getDataValue('storageLocationId'),
                    storageLocation: item.storageLocation?.getDataValue('name') ?? null,
                    factoryGateId: item.getDataValue('factoryGateId'),
                    factoryGate: item.factoryGate.getDataValue('name'),
                    totalQty: Number(item.getDataValue('qty')),
                    rejectedQty: rejectedQty,
                    holdQty: holdQty,
                    excessQty: excessQty,
                    replaceableQty: replaceableQty,
                    createdAt: item.getDataValue('createdAt'),
                    createdById: item.getDataValue('createdById'),
                    updatedAt: item.getDataValue('updatedAt'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                    deletedById: item.getDataValue('deletedById'),
                    poNumber: item.purchaseInvoice.dataValues.poNumber,
                    purchaseInvoiceNumber: item.purchaseInvoice.dataValues.invoiceNumber,
                    purchasedBy: item.purchaseInvoice.purchasedBy.dataValues.firstName,
                    receivedAt: item.purchaseInvoice.dataValues.invoiceDate,

                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchInStockByText(text: string, storageLocationNotAssigned: boolean, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>> {
        try {
            /* first get raw materials */
            const rawMaterials = await RawMaterialVariationTable.findAll({
                where: {
                    name: {
                        [Op.iLike]: `%${text}%`
                    }
                },transaction
            });


            if (rawMaterials.length === 0) {
                return HelperMethods.getSuccessResponse({
                    currentPage: 1,
                    totalData: 0,
                    totalPages: 1,
                    data: [],
                });
            }

            const rawMaterialIds = rawMaterials.map((item) => item.dataValues.id);

            const whereConditions: any = {
                rawMaterialId: {
                    [Op.in]: rawMaterialIds
                }
            };

            if (!storageLocationNotAssigned) {

                whereConditions.storageLocationId = null;
            }


            const { count, rows } = await RawMaterialStockInTable.findAndCountAll({
                limit: 10,
                order: [['createdAt', 'DESC']],
                where: whereConditions,
                transaction,
                include: [
                    {
                        model: RawMaterialVariationTable,
                        attributes: ['name',],
                        as: "rawMaterialVariation",
                        include: [
                            {
                                model: RawMaterialMainTable,
                                as: "rawMaterial",
                                include: [
                                    {
                                        model: ItemCategoryTable,
                                        attributes: ['name'],
                                        as: "category",
                                    },
                                    {
                                        model: ItemUnitTable,
                                        attributes: ['name'],
                                        as: "unit",
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        model: SupplierTable,
                        attributes: ['name',],
                        as: "supplier",
                    },
                    {
                        model: FactoryGateTable,
                        attributes: ['name',],
                        as: "factoryGate",
                    },
                    {
                        model: StorageLocationTable,
                        attributes: ['name',],
                        as: "storageLocation",
                    },
                    {
                        model: PurchaseInvoiceTable,
                        as: "purchaseInvoice",
                        include: [
                            {
                                model: CoreUserTable,
                                as: "purchasedBy",
                            },
                            {
                                model: RawMaterialRejectionTable,
                                as: "rawMaterialRejections",
                                attributes: ['rejectedQty',],
                            },
                            {
                                model: RawMaterialHoldTable,
                                as: "rawMaterialHolds",
                                attributes: ['holdQty',],
                            },
                            {
                                model: RawMaterialExcessEntryTable,
                                as: "rawMaterialExcessEntry",
                                attributes: ['qty']
                            },
                            {
                                model: RawMaterialReplacementEntryTable,
                                as: "rawMaterialReplacementEntry",
                                attributes: ['qty']
                            }
                        ]
                    },
                ],
            });




            const totalPages = 1;

            const data: IRawMaterialStockInDetails[] = [];

            let rejectedQty = 0;
            let holdQty = 0;
            let excessQty = 0;
            let replaceableQty = 0;

            for (const item of rows) {

                rejectedQty = Number(item.purchaseInvoice.rawMaterialRejections
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, rejection) => sum + (Number(rejection.dataValues.rejectedQty) ?? 0), 0)
                    .toFixed(2));

                holdQty = Number(item.purchaseInvoice.rawMaterialHolds
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, hold) => sum + (Number(hold.dataValues.holdQty) ?? 0), 0)
                    .toFixed(2));

                excessQty = Number(item.purchaseInvoice.rawMaterialExcessEntry
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                    .toFixed(2));

                replaceableQty = Number(item.purchaseInvoice.rawMaterialReplacementEntry
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                    .toFixed(2));


                data.push({
                    id: item.getDataValue('id'),
                    rawMaterialId: item.getDataValue('rawMaterialId'),
                    rawMaterial: item.rawMaterialVariation.getDataValue('name'),
                    categoryName: item.rawMaterialVariation.rawMaterial.category.dataValues.name,
                    rawMaterialUnit: item.rawMaterialVariation.rawMaterial.unit.dataValues.name,
                    supplierId: item.getDataValue('supplierId'),
                    supplier: item.supplier.getDataValue('name'),
                    price: item.getDataValue('price'),
                    storageLocationId: item.getDataValue('storageLocationId'),
                    storageLocation: item.storageLocation?.getDataValue('name') ?? null,
                    factoryGateId: item.getDataValue('factoryGateId'),
                    factoryGate: item.factoryGate.getDataValue('name'),
                    totalQty: Number(item.getDataValue('qty')),
                    rejectedQty: rejectedQty,
                    holdQty: holdQty,
                    excessQty: excessQty,
                    replaceableQty: replaceableQty,
                    createdAt: item.getDataValue('createdAt'),
                    createdById: item.getDataValue('createdById'),
                    updatedAt: item.getDataValue('updatedAt'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                    deletedById: item.getDataValue('deletedById'),
                    poNumber: item.purchaseInvoice.dataValues.poNumber,
                    purchaseInvoiceNumber: item.purchaseInvoice.dataValues.invoiceNumber,
                    purchasedBy: item.purchaseInvoice.purchasedBy.dataValues.firstName,
                    receivedAt: item.purchaseInvoice.dataValues.invoiceDate,

                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getStockInById(id: number, transaction: Transaction): Promise<APIBaseResponse<IRawMaterialStockInDetails | null>> {
        try {
            const result = await RawMaterialStockInTable.findByPk(id, {
                transaction,
                include: [
                    {
                        model: RawMaterialVariationTable,
                        as: "rawMaterialVariation",
                        attributes: ['name'],
                        where: {
                            deletedAt: null
                        },
                        include: [
                            {
                                model: RawMaterialMainTable,
                                as: "rawMaterial",
                                include: [
                                    {
                                        model: ItemCategoryTable,
                                        attributes: ['name'],
                                        as: "category",
                                        where: {
                                            deletedAt: null
                                        }
                                    },
                                    {
                                        model: ItemUnitTable,
                                        attributes: ['name'],
                                        as: "unit",
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        model: SupplierTable,
                        attributes: ['name',],
                        as: "supplier",
                    },
                    {
                        model: FactoryGateTable,
                        attributes: ['name',],
                        as: "factoryGate",
                    },
                    {
                        model: StorageLocationTable,
                        attributes: ['name',],
                        as: "storageLocation",
                    },
                    {
                        model: PurchaseInvoiceTable,
                        as: "purchaseInvoice",
                        include: [
                            {
                                model: CoreUserTable,
                                as: "purchasedBy",
                            },
                            {
                                model: RawMaterialRejectionTable,
                                as: "rawMaterialRejections",
                                attributes: ['rejectedQty',],
                            },
                            {
                                model: RawMaterialHoldTable,
                                as: "rawMaterialHolds",
                                attributes: ['holdQty',],
                            },
                            {
                                model: RawMaterialExcessEntryTable,
                                as: "rawMaterialExcessEntry",
                                attributes: ['qty']
                            },
                            {
                                model: RawMaterialReplacementEntryTable,
                                as: "rawMaterialReplacementEntry",
                                attributes: ['qty']
                            }
                        ]
                    },
                ],
            });

            if (!result) {
                return HelperMethods.getErrorResponse("No record found");
            }

            let rejectedQty = 0;
            let holdQty = 0;
            let excessQty = 0;
            let replaceableQty = 0;

            rejectedQty = Number(result.purchaseInvoice.rawMaterialRejections
                .filter(x => x.dataValues.rawMaterialId === result.rawMaterialVariation.dataValues.id)
                .reduce((sum, rejection) => sum + (Number(rejection.dataValues.rejectedQty) ?? 0), 0)
                .toFixed(2));

            holdQty = Number(result.purchaseInvoice.rawMaterialHolds
                .filter(x => x.dataValues.rawMaterialId === result.rawMaterialVariation.dataValues.id)
                .reduce((sum, hold) => sum + (Number(hold.dataValues.holdQty) ?? 0), 0)
                .toFixed(2));

            excessQty = Number(result.purchaseInvoice.rawMaterialExcessEntry
                .filter(x => x.dataValues.rawMaterialId === result.rawMaterialVariation.dataValues.id)
                .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                .toFixed(2));

            replaceableQty = Number(result.purchaseInvoice.rawMaterialReplacementEntry
                .filter(x => x.dataValues.rawMaterialId === result.rawMaterialVariation.dataValues.id)
                .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                .toFixed(2));


            const data: IRawMaterialStockInDetails = {
                id: Number(result.getDataValue('id')),
                rawMaterialId: Number(result.getDataValue('rawMaterialId')),
                rawMaterial: result.rawMaterialVariation.getDataValue('name'),
                rawMaterialUnit: result.rawMaterialVariation.rawMaterial.unit.dataValues.name,
                categoryName: result.rawMaterialVariation.rawMaterial.category.dataValues.name,
                supplierId: result.getDataValue('supplierId'),
                supplier: result.supplier.getDataValue('name'),
                price: result.getDataValue('price'),
                storageLocationId: result.getDataValue('storageLocationId') ? Number(result.getDataValue('storageLocationId')!) : null,
                storageLocation: result.storageLocation?.getDataValue('name') ?? null,
                factoryGateId: result.getDataValue('factoryGateId'),
                factoryGate: result.factoryGate.getDataValue('name'),
                totalQty: Number(result.getDataValue('qty')),
                rejectedQty: rejectedQty,
                holdQty: holdQty,
                excessQty: excessQty,
                replaceableQty: replaceableQty,
                createdAt: result.getDataValue('createdAt'),
                createdById: result.getDataValue('createdById'),
                updatedAt: result.getDataValue('updatedAt'),
                updatedById: result.getDataValue('updatedById'),
                deletedAt: result.getDataValue('deletedAt'),
                deletedById: result.getDataValue('deletedById'),
                poNumber: result.purchaseInvoice.dataValues.poNumber,
                purchaseInvoiceNumber: result.purchaseInvoice.dataValues.invoiceNumber,
                purchasedBy: result.purchaseInvoice.purchasedBy.dataValues.firstName,
                receivedAt: result.purchaseInvoice.dataValues.invoiceDate,

            };

            return HelperMethods.getSuccessResponse(data);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getStockInByDateRange(startDate: Date, endDate: Date, page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>> {
        try {
            /* get purchase invoice ids by date range */
            const purchaseInvoiceData = await PurchaseInvoiceTable.findAll({
                where: {
                    invoiceDate: {
                        [Op.between]: [startDate, endDate],
                    }
                },
                transaction
            });


            /* get raw material stock in */

            const offset = (page - 1) * pageSize;
            const { count, rows } = await RawMaterialStockInTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                transaction,
                where: {
                    purchaseInvoiceId: {
                        [Op.in]: purchaseInvoiceData.map((item) => item.dataValues.id)
                    }
                },
                include: [
                    {
                        model: RawMaterialVariationTable,
                        attributes: ['name',],
                        as: "rawMaterialVariation",
                    },
                    {
                        model: SupplierTable,
                        attributes: ['name',],
                        as: "supplier",
                    },
                    {
                        model: FactoryGateTable,
                        attributes: ['name',],
                        as: "factoryGate",
                    },
                    {
                        model: StorageLocationTable,
                        attributes: ['name',],
                        as: "storageLocation",
                    },
                    {
                        model: PurchaseInvoiceTable,
                        as: "purchaseInvoice",
                        include: [
                            {
                                model: CoreUserTable,
                                as: "purchasedBy",
                            },
                            {
                                model: RawMaterialRejectionTable,
                                as: "rawMaterialRejections",
                                attributes: ['rejectedQty',],
                            },
                            {
                                model: RawMaterialHoldTable,
                                as: "rawMaterialHolds",
                                attributes: ['holdQty',],
                            },
                            {
                                model: RawMaterialExcessEntryTable,
                                as: "rawMaterialExcessEntry",
                                attributes: ['qty']
                            },
                            {
                                model: RawMaterialReplacementEntryTable,
                                as: "rawMaterialReplacementEntry",
                                attributes: ['qty']
                            }
                        ]

                    },
                ],
            });

            const totalPages = Math.ceil(count / pageSize);

            const data: IRawMaterialStockInDetails[] = [];

            let rejectedQty = 0;
            let holdQty = 0;
            let excessQty = 0;
            let replaceableQty = 0;

            for (const item of rows) {
                rejectedQty = Number(item.purchaseInvoice.rawMaterialRejections
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, rejection) => sum + (Number(rejection.dataValues.rejectedQty) ?? 0), 0)
                    .toFixed(2));

                holdQty = Number(item.purchaseInvoice.rawMaterialHolds
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, hold) => sum + (Number(hold.dataValues.holdQty) ?? 0), 0)
                    .toFixed(2));

                excessQty = Number(item.purchaseInvoice.rawMaterialExcessEntry
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                    .toFixed(2));

                replaceableQty = Number(item.purchaseInvoice.rawMaterialReplacementEntry
                    .filter(x => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id)
                    .reduce((sum, excess) => sum + (Number(excess.dataValues.qty) ?? 0), 0)
                    .toFixed(2));


                data.push({
                    id: item.getDataValue('id'),
                    rawMaterialId: item.getDataValue('rawMaterialId'),
                    rawMaterial: item.rawMaterialVariation.getDataValue('name'),
                    rawMaterialUnit: "",
                    categoryName: "",
                    supplierId: item.getDataValue('supplierId'),
                    supplier: item.supplier.getDataValue('name'),
                    price: item.getDataValue('price'),
                    poNumber: item.purchaseInvoice.dataValues.poNumber,
                    purchaseInvoiceNumber: item.purchaseInvoice.dataValues.invoiceNumber,
                    purchasedBy: item.purchaseInvoice.purchasedBy.dataValues.firstName,
                    storageLocationId: item.getDataValue('storageLocationId'),
                    storageLocation: item.storageLocation?.getDataValue('name') ?? null,
                    factoryGateId: item.getDataValue('factoryGateId'),
                    factoryGate: item.factoryGate.getDataValue('name'),
                    totalQty: Number(item.getDataValue('qty')),
                    rejectedQty: rejectedQty,
                    holdQty: holdQty,
                    excessQty: excessQty,
                    replaceableQty: replaceableQty,
                    createdAt: item.getDataValue('createdAt'),
                    createdById: item.getDataValue('createdById'),
                    updatedAt: item.getDataValue('updatedAt'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                    deletedById: item.getDataValue('deletedById'),
                    receivedAt: item.purchaseInvoice.dataValues.invoiceDate,
                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });

        } catch (error) {


            if (error instanceof DatabaseError) {
                if (error.toString().includes("Invalid date")) {
                    return HelperMethods.getErrorResponse('Invalid date');
                }
            }

            // if (error instanceof UniqueConstraintError) {
            //     if (error.errors[0].path === 'email') {
            //         return HelperMethods.getErrorResponse('Email already exists');
            //     }
            //     else if (error.errors[0].path === 'phone') {
            //         return HelperMethods.getErrorResponse('Contact number already exists');
            //     }

            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async exportByCategory(categoryId: number, transaction: Transaction): Promise<APIBaseResponse<IRawMaterialStockDetails[] | null>> {
        try {
            const { rows } = await RawMaterialStockTable.findAndCountAll({
                transaction,
                include: [
                    {
                        model: RawMaterialVariationTable,
                        as: "rawMaterialVariation",
                        include: [
                            {
                                model: RawMaterialMainTable,
                                as: "rawMaterial",
                                where: {
                                    categoryId: categoryId,
                                },
                                include: [
                                    {
                                        model: ItemCategoryTable,
                                        attributes: ['name'],
                                        as: "category",
                                    }
                                ]
                            }
                        ]
                    }
                ],
            });

            const data: IRawMaterialStockDetails[] = [];

            for (const item of rows) {
                data.push({
                    id: item.getDataValue('id'),
                    rawMaterialId: item.getDataValue('rawMaterialId'),

                    rawMaterialName: item.rawMaterialVariation?.dataValues?.name ?? "",
                    sku: item.rawMaterialVariation?.dataValues?.sku ?? "",
                    categoryName: item.rawMaterialVariation?.rawMaterial?.category?.dataValues?.name ?? "",
                    msq: item.rawMaterialVariation?.dataValues?.msq ?? 0,
                    totalStock: item.getDataValue('totalStock'),
                    usableStock: item.dataValues.usableStock,
                    assignedStock: item.getDataValue('assignedStock'),
                    createdAt: item.getDataValue('createdAt'),
                    createdById: item.getDataValue('createdById'),
                    updatedAt: item.getDataValue('updatedAt'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                    deletedById: item.getDataValue('deletedById'),
                });
            }

            return HelperMethods.getSuccessResponse(data);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async exportByCategoryWithPrice(categoryId: number, transaction: Transaction): Promise<APIBaseResponse<IRawMaterialStockDetailsWithPrice[] | null>> {
        try {
            const averagePricesQuery = await RawMaterialPriceTable.findAll({
                attributes: [
                    'rawMaterialId',
                    [fn("AVG", col("price")), "averagePrice"]
                ],
                group: ['rawMaterialId'],
                transaction
            });

            const averagePricesMap = averagePricesQuery.reduce<Record<number, number>>((acc, current) => {
                acc[Number(current.dataValues.rawMaterialId)] = Number(Number(current.dataValues.averagePrice ?? 0).toFixed(2));
                return acc;
            }, {});


            const { rows } = await RawMaterialStockTable.findAndCountAll({
                transaction,
                include: [
                    {
                        model: RawMaterialVariationTable,
                        as: "rawMaterialVariation",
                        required: true,
                        include: [
                            {
                                model: RawMaterialMainTable,
                                as: "rawMaterial",
                                where: {
                                    categoryId: categoryId,
                                },
                                include: [
                                    {
                                        model: ItemCategoryTable,
                                        attributes: ['id', 'name'],
                                        as: "category",
                                    }
                                ]
                            },

                        ]
                    }
                ],
                // attributes: {
                //     include: [
                //         [fn("AVG", col("rawMaterialVariation->prices.price")), "averagePrice"]
                //     ]
                // },
                group: [
                    "RawMaterialStockTable.id",
                    "rawMaterialVariation.id",
                    "rawMaterialVariation.rawMaterial.id",
                    "rawMaterialVariation.rawMaterial->category.id",
                    "rawMaterialVariation.rawMaterial->category.name",
                    "rawMaterialVariation.name",
                    "rawMaterialVariation.msq",

                ],
                logging: true,
            })

            const data: IRawMaterialStockDetailsWithPrice[] = rows.map(item => ({
                id: item.getDataValue("id"),
                rawMaterialId: item.getDataValue("rawMaterialId"),
                rawMaterialName: item.rawMaterialVariation?.dataValues?.name ?? "",
                sku: item.rawMaterialVariation?.dataValues?.sku ?? "",
                categoryName: item.rawMaterialVariation?.rawMaterial.category?.dataValues?.name ?? "",
                msq: item.rawMaterialVariation?.dataValues?.msq ?? 0,
                totalStock: item.getDataValue("totalStock"),
                usableStock: item.getDataValue("usableStock"),
                assignedStock: item.getDataValue("assignedStock"),
                price: (averagePricesMap[item.rawMaterialVariation?.dataValues?.id] ?? 0),
                createdAt: item.getDataValue("createdAt"),
                createdById: item.getDataValue("createdById"),
                updatedAt: item.getDataValue("updatedAt"),
                updatedById: item.getDataValue("updatedById"),
                deletedAt: item.getDataValue("deletedAt"),
                deletedById: item.getDataValue("deletedById"),
            }));

            return HelperMethods.getSuccessResponse(data);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getStockIssuanceById(entryId: string, transaction: Transaction): Promise<APIBaseResponse<IRawMaterialStockIssuanceResponse | null>> {
        try {


            const { rows } = await RawMaterialStockIssuanceTable.findAndCountAll({
                where: {
                    entryId: entryId
                },
                transaction,
                include:
                    [
                        {
                            model: RawMaterialVariationTable,
                            as: 'rawMaterial',
                            required: true,
                        },
                        {
                            model: CoreUserTable,
                            as: "issuedBy",
                            required: true,
                            include: [
                                {
                                    model: NormalUserTable,
                                    as: "normalUser",
                                }
                            ]
                        },
                        {
                            model: CoreUserTable,
                            as: "issuedToUser",
                            include: [
                                {
                                    model: NormalUserTable,
                                    as: "normalUser",
                                }
                            ]
                        },
                    ],
            });

            let data: IRawMaterialStockIssuanceResponse | null = null;

            if (rows.length > 0) {
                const fetchedItem = rows[0];
                data = {
                    entryId: fetchedItem.dataValues.entryId,
                    soNumber: fetchedItem.dataValues.soNumber,
                    notes: fetchedItem.dataValues.notes,
                    issuedTo: fetchedItem.issuedToUser ? {
                        ...fetchedItem.issuedToUser.dataValues,
                        id: Number(fetchedItem.issuedToUser.dataValues.id),
                        coreUserId: Number(fetchedItem.issuedToUser.normalUser.dataValues.coreUserId),
                        address: {} as any,
                        role: {} as any
                    } : null,
                    issuedBy: {
                        ...fetchedItem.issuedBy.dataValues,
                        id: Number(fetchedItem.issuedBy.dataValues.id),
                        coreUserId: Number(fetchedItem.issuedBy.normalUser.dataValues.coreUserId),
                        address: {} as any,
                        role: {} as any
                    },
                    rawMaterials: rows.map(data => ({
                        rawMaterial: {
                            ...data.rawMaterial.dataValues,
                            id: Number(data.rawMaterial.dataValues.id),
                            unitName: "",
                            categoryName: "",
                            priceData: [],
                        },
                        qty: Number(data.dataValues.qty),
                    })),
                    issuedAt: fetchedItem.dataValues.createdAt,
                };
            }

            return HelperMethods.getSuccessResponse(data);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
    async getProblematicStocks(transaction: Transaction): Promise<APIBaseResponse<IRawMaterialStockDetails[] | null>> {
        try {
            const { rows } = await RawMaterialStockTable.findAndCountAll({
                transaction,
                where: {
                    [Op.or]: [
                        // usableStock > totalStock (impossible scenario)
                        Sequelize.where(
                            Sequelize.col('usableStock'),
                            Op.gt,
                            Sequelize.col('totalStock')
                        ),
                        // totalStock < 0 (negative total stock)
                        {
                            totalStock: {
                                [Op.lt]: 0
                            }
                        },
                        // usableStock < 0 (negative usable stock)
                        {
                            usableStock: {
                                [Op.lt]: 0
                            }
                        }
                    ]
                },
                include: [
                    {
                        model: RawMaterialVariationTable,
                        as: "rawMaterialVariation",
                        include: [
                            {
                                model: RawMaterialMainTable,
                                as: "rawMaterial",
                                include: [
                                    {
                                        model: ItemCategoryTable,
                                        attributes: ['name'],
                                        as: "category",
                                    }
                                ]
                            }
                        ]
                    }
                ],
                order: [['rawMaterialId', 'ASC']]
            });

            const data: IRawMaterialStockDetails[] = rows.map(item => ({
                id: item.getDataValue('id'),
                rawMaterialId: item.getDataValue('rawMaterialId'),
                rawMaterialName: item.rawMaterialVariation?.dataValues?.name ?? 'Unknown Material',
                sku: item.rawMaterialVariation?.dataValues?.sku ?? '',
                categoryName: item.rawMaterialVariation?.rawMaterial?.category?.dataValues?.name ?? 'Unknown Category',
                msq: item.rawMaterialVariation?.dataValues?.msq ?? 0,
                totalStock: item.getDataValue('totalStock'),
                usableStock: item.dataValues.usableStock,
                assignedStock: item.getDataValue('assignedStock'),
                createdAt: item.getDataValue('createdAt'),
                createdById: item.getDataValue('createdById'),
                updatedAt: item.getDataValue('updatedAt'),
                updatedById: item.getDataValue('updatedById'),
                deletedAt: item.getDataValue('deletedAt'),
                deletedById: item.getDataValue('deletedById'),
            }));

            return HelperMethods.getSuccessResponse(data);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}