import { InterfaceMetaData } from "../../../core/CoreInterfaces";


interface BrandPayload {
    title: string,
    description: string,
    logo: string,
    whiteListedDomains: string[],
}

interface IBrandTable extends BrandPayload, InterfaceMetaData {

}
interface IBrandCreationAttributes extends BrandPayload {
    createdById: number
}

interface ParsedBrand extends InterfaceMetaData {
    title: string,
    description: string,
    logo: string,
}


export {
    IBrandTable,
    BrandPayload,
    ParsedBrand,
    IBrandCreationAttributes
}
