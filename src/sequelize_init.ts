require('dotenv').config();
import { Sequelize } from 'sequelize';


export enum APP_ENV {
    dev = "dev",
    test = "test",
    prod = "prod",
}



let WorkingENV: any = APP_ENV.dev;



const neonDBConnectionString = process.env.PROD_DB_URL || "";
const neonDBStaging = process.env.STAG_DB_URL || "";

let username = "";
let password = "";
let database = "";
let host = "";

if (WorkingENV === APP_ENV.dev) {
    username = process.env.DEV_DB_USERNAME || "";
    password = process.env.DEV_DB_PASSWORD || "";
    database = process.env.DEV_DB_NAME || "";
    host = process.env.DEV_DB_HOST || "";
}
else if (WorkingENV === APP_ENV.prod) {
    username = process.env.PROD_DB_USERNAME || "";
    password = process.env.PROD_DB_PASSWORD || "";
    database = process.env.PROD_DB_NAME || "";
    host = process.env.PROD_DB_HOST || "";
}


let sequelizeInit: Sequelize;

if (WorkingENV === APP_ENV.dev) {

    sequelizeInit = new Sequelize(
        database, username, password
        , {
            dialect: "postgres",
            host: host,
            logging: false,
            pool: {
                max: 50,
                min: 0,
                acquire: 5000,
                idle: 3000,
            },
        });
}
else if (WorkingENV === APP_ENV.test) {
    sequelizeInit = new Sequelize(neonDBStaging, {
        logging: false,
    });

}

else if (WorkingENV === APP_ENV.prod) {

    sequelizeInit = new Sequelize(
        database, username, password
        , {
            dialect: "postgres",
            host: host,
            logging: true
        });

}

export { sequelizeInit, WorkingENV };