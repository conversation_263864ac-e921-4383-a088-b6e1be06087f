const { url } = require('inspector');

require('dotenv').config();


const neonDBConnectionString = process.env.NEON_DB_CONNECTION_STRING || "";

module.exports = {
    development: {
        // url: neonDBConnectionString,
        // dialectOptions: {
        //     ssl: {
        //         require: true,
        //     }
        // },
        dialect: 'postgres',
        username: process.env.PROD_DB_USERNAME || "",
        password: process.env.PROD_DB_PASSWORD || "",
        database: process.env.PROD_DB_NAME || "",
        host: process.env.PROD_DB_HOST || "",
    },
      production: {
        // url: neonDBConnectionString,
        // dialectOptions: {
        //     ssl: {
        //         require: true,
        //     }
        // },
        dialect: 'postgres',
        username: process.env.PROD_DB_USERNAME || "",
        password: process.env.PROD_DB_PASSWORD || "",
        database: process.env.PROD_DB_NAME || "",
        host: process.env.PROD_DB_HOST || "",
    },
};