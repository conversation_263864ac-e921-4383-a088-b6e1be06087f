{"plugins": ["prettier-plugin-svelte", "prettier-plugin-tailwindcss"], "pluginSearchDirs": ["."], "overrides": [{"files": "*.svelte", "options": {"parser": "svelte"}}], "svelteSortOrder": "options-scripts-markup-styles", "svelteStrictMode": false, "svelteBracketNewLine": true, "svelteAllowShorthand": true, "svelteIndentScriptAndStyle": true, "trailingComma": "es5", "tabWidth": 4, "semi": true, "singleQuote": false, "arrayBracketSpacing": false, "printWidth": 100, "htmlWhitespaceSensitivity": "ignore"}